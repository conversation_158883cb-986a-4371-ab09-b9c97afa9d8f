# LP Creator

Next.jsとMastra AIフレームワークを使用したAI駆動ランディングページ作成ツール

## ✨ 新機能：統合LP生成

🚀 **V0/Claude Artifacts風の統合LP生成機能**を追加！
- 自然言語で完全なランディングページを一気に生成
- セクション分割の制約なし、完全に自由な構造
- HTML/CSS/JSコード完備で即座にダウンロード・表示可能

### 使用例
```
「SaaSツールの完全なLPを作って」
「ECサイトのモダンなランディングページ生成」  
「スタートアップ向けの紫色のLPを作成」
```

## 機能

### 🚀 統合LP生成（V0/Claude Artifacts風）
- **generateUnifiedLP**: ユーザーの自然言語指示から完全なHTMLベースのLPを一気に生成
- 6つのデザインスタイル（modern, corporate, startup, creative, minimal, bold）
- 6つのカラースキーム（blue, green, purple, orange, dark, gradient）
- 8種類のセクション（hero, problem, solution, features, testimonials, pricing, faq, contact）
- 完全なHTMLコード出力で即座にダウンロード・表示可能
- セクション分割の制約なし、完全に自由な構造

## はじめ方

### 開発サーバーの起動

開発サーバーを起動：

```bash
npm run dev
```

ブラウザで [http://localhost:3000](http://localhost:3000) を開いて結果を確認してください。

### Mastra AI統合

このプロジェクトでは、インテリジェントなコンテンツ生成とワークフロー自動化のために[Mastra AIフレームワーク](https://mastra.ai)を使用しています。

#### Mastra開発

Mastra開発サーバーを起動：

```bash
npm run dev:mastra
```

#### 環境変数

以下の変数を含む`.env.local`ファイルを作成してください：

```bash
# Mastra AI機能に必要
ANTHROPIC_API_KEY=your_anthropic_api_key

# オプションのMastra設定
ENABLE_MASTRA=true
DATABASE_URL=file:./.mastra/memory.db
MASTRA_LOG_LEVEL=debug
```

#### Mastraコンポーネント

- **エージェント**: ランディングページ作成用AIアシスタント (`mastra/agents/`)
- **ツール**: コンテンツ処理用カスタムツール (`mastra/tools/`)
- **データベース**: 会話履歴用SQLiteストレージ
- **ヘルスチェック**: `/api/mastra-health`のAPIエンドポイント

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
