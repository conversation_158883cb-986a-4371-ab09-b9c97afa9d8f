import { tool as createTool } from 'ai';
import { z } from 'zod';

// 統合LP生成ツール - V0/<PERSON> Artifacts風
export const generateUnifiedLP = createTool({
  description: `
ユーザーの自然言語指示から完全なランディングページを一気に生成します。
V0やClaude Artifactsのように、型に縛られない自由な構造でLPを作成。
完全なHTML/CSS/JSコードを生成し、即座に表示可能な状態で返します。
`,
  parameters: z.object({
    userRequest: z.string().describe('ユーザーの自然言語による要求（例：「SaaSツールのLP作って」「ECサイトのLP作って」）'),
    productName: z.string().describe('商品・サービス名'),
    targetAudience: z.string().describe('ターゲット層'),
    keyValue: z.string().describe('主要な価値提案'),
    style: z.enum(['modern', 'corporate', 'startup', 'creative', 'minimal', 'bold']).describe('デザインスタイル'),
    colorScheme: z.enum(['blue', 'green', 'purple', 'orange', 'dark', 'gradient']).describe('カラースキーム'),
    sections: z.array(z.string()).describe('含めたいセクション（hero, problem, solution, features, testimonials, pricing, faq, contact等）'),
  }),
  execute: async ({ userRequest, productName, targetAudience, keyValue, style, colorScheme, sections }) => {
    
    // カラースキーム定義
    const colorSchemes = {
      blue: { primary: '#3b82f6', secondary: '#1d4ed8', accent: '#60a5fa', bg: '#f8fafc' },
      green: { primary: '#10b981', secondary: '#059669', accent: '#34d399', bg: '#f0fdf4' },
      purple: { primary: '#8b5cf6', secondary: '#7c3aed', accent: '#a78bfa', bg: '#faf5ff' },
      orange: { primary: '#f59e0b', secondary: '#d97706', accent: '#fbbf24', bg: '#fffbeb' },
      dark: { primary: '#1f2937', secondary: '#111827', accent: '#6b7280', bg: '#f9fafb' },
      gradient: { primary: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', secondary: '#4f46e5', accent: '#8b5cf6', bg: '#f8fafc' }
    };

    const colors = colorSchemes[colorScheme];

    // スタイル定義
    const stylePresets = {
      modern: {
        fontFamily: '"Inter", "Segoe UI", sans-serif',
        borderRadius: '12px',
        shadow: '0 4px 20px rgba(0,0,0,0.1)',
        spacing: '60px'
      },
      corporate: {
        fontFamily: '"Arial", sans-serif',
        borderRadius: '8px',
        shadow: '0 2px 10px rgba(0,0,0,0.1)',
        spacing: '50px'
      },
      startup: {
        fontFamily: '"Poppins", sans-serif',
        borderRadius: '16px',
        shadow: '0 8px 30px rgba(0,0,0,0.12)',
        spacing: '70px'
      },
      creative: {
        fontFamily: '"Montserrat", sans-serif',
        borderRadius: '20px',
        shadow: '0 6px 25px rgba(0,0,0,0.15)',
        spacing: '80px'
      },
      minimal: {
        fontFamily: '"Helvetica Neue", sans-serif',
        borderRadius: '4px',
        shadow: '0 1px 5px rgba(0,0,0,0.1)',
        spacing: '40px'
      },
      bold: {
        fontFamily: '"Oswald", sans-serif',
        borderRadius: '24px',
        shadow: '0 10px 40px rgba(0,0,0,0.2)',
        spacing: '90px'
      }
    };

    const styleConfig = stylePresets[style];

    // 基本CSS
    const baseCSS = `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: ${styleConfig.fontFamily};
        line-height: 1.6;
        color: #333;
        background: ${colors.bg};
      }
      
      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
      }
      
      .section {
        padding: ${styleConfig.spacing} 0;
      }
      
      .btn-primary {
        background: ${colors.primary};
        color: white;
        padding: 16px 32px;
        border: none;
        border-radius: ${styleConfig.borderRadius};
        font-size: 18px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: ${styleConfig.shadow};
        text-decoration: none;
        display: inline-block;
      }
      
      .btn-primary:hover {
        background: ${colors.secondary};
        transform: translateY(-2px);
      }
      
      .card {
        background: white;
        padding: 30px;
        border-radius: ${styleConfig.borderRadius};
        box-shadow: ${styleConfig.shadow};
        margin: 20px 0;
      }
    `;

    // セクション生成ロジック
    const generateSection = (sectionType: string) => {
      switch (sectionType) {
        case 'hero':
          return `
            <section class="hero section" style="background: ${colorScheme === 'gradient' ? colors.primary : colors.primary}; color: white; text-align: center; min-height: 100vh; display: flex; align-items: center;">
              <div class="container">
                <h1 style="font-size: 3.5rem; font-weight: 700; margin-bottom: 20px; line-height: 1.2;">${productName}</h1>
                <p style="font-size: 1.5rem; margin-bottom: 40px; opacity: 0.95;">${keyValue}</p>
                <p style="font-size: 1.2rem; margin-bottom: 50px;">${targetAudience}のための最適なソリューション</p>
                <a href="#contact" class="btn-primary" style="font-size: 20px; padding: 20px 40px;">今すぐ始める</a>
              </div>
            </section>
          `;
          
        case 'problem':
          return `
            <section class="problem section">
              <div class="container">
                <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 40px; color: ${colors.secondary};">こんな悩みありませんか？</h2>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px;">
                  <div class="card">
                    <h3 style="color: #e74c3c; margin-bottom: 15px;">❌ 問題1</h3>
                    <p>時間がかかりすぎて効率が悪い</p>
                  </div>
                  <div class="card">
                    <h3 style="color: #e74c3c; margin-bottom: 15px;">❌ 問題2</h3>
                    <p>複雑すぎて使いこなせない</p>
                  </div>
                  <div class="card">
                    <h3 style="color: #e74c3c; margin-bottom: 15px;">❌ 問題3</h3>
                    <p>コストが高くて続けられない</p>
                  </div>
                </div>
              </div>
            </section>
          `;
          
        case 'solution':
          return `
            <section class="solution section" style="background: white;">
              <div class="container">
                <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 40px; color: ${colors.secondary};">${productName}なら解決できます</h2>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px;">
                  <div class="card" style="background: ${colors.bg};">
                    <h3 style="color: ${colors.primary}; margin-bottom: 15px;">✅ 解決1</h3>
                    <p>自動化により作業時間を80%短縮</p>
                  </div>
                  <div class="card" style="background: ${colors.bg};">
                    <h3 style="color: ${colors.primary}; margin-bottom: 15px;">✅ 解決2</h3>
                    <p>直感的なUIで誰でも簡単操作</p>
                  </div>
                  <div class="card" style="background: ${colors.bg};">
                    <h3 style="color: ${colors.primary}; margin-bottom: 15px;">✅ 解決3</h3>
                    <p>業界最安値でコストパフォーマンス抜群</p>
                  </div>
                </div>
              </div>
            </section>
          `;
          
        case 'features':
          return `
            <section class="features section">
              <div class="container">
                <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 40px; color: ${colors.secondary};">主要機能</h2>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 40px;">
                  <div style="text-align: center;">
                    <div style="width: 80px; height: 80px; background: ${colors.primary}; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">🚀</div>
                    <h3 style="margin-bottom: 15px; color: ${colors.secondary};">高速処理</h3>
                    <p>最新のAI技術により、従来の10倍の速度で処理を実行</p>
                  </div>
                  <div style="text-align: center;">
                    <div style="width: 80px; height: 80px; background: ${colors.primary}; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">🛡️</div>
                    <h3 style="margin-bottom: 15px; color: ${colors.secondary};">セキュリティ</h3>
                    <p>業界最高水準のセキュリティでデータを完全保護</p>
                  </div>
                  <div style="text-align: center;">
                    <div style="width: 80px; height: 80px; background: ${colors.primary}; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">📊</div>
                    <h3 style="margin-bottom: 15px; color: ${colors.secondary};">分析機能</h3>
                    <p>リアルタイムでデータを分析し、最適な改善提案を提供</p>
                  </div>
                </div>
              </div>
            </section>
          `;
          
        case 'testimonials':
          return `
            <section class="testimonials section" style="background: white;">
              <div class="container">
                <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 40px; color: ${colors.secondary};">お客様の声</h2>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px;">
                  <div class="card">
                    <div style="display: flex; margin-bottom: 15px;">
                      ${'★'.repeat(5)}
                    </div>
                    <p style="margin-bottom: 20px; font-style: italic;">"導入後、作業効率が劇的に改善されました。もう手放せません！"</p>
                    <div style="display: flex; align-items: center;">
                      <div style="width: 50px; height: 50px; background: ${colors.primary}; border-radius: 50%; margin-right: 15px; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">田</div>
                      <div>
                        <div style="font-weight: 600;">田中様</div>
                        <div style="color: #666; font-size: 14px;">株式会社ABC / 営業部長</div>
                      </div>
                    </div>
                  </div>
                  <div class="card">
                    <div style="display: flex; margin-bottom: 15px;">
                      ${'★'.repeat(5)}
                    </div>
                    <p style="margin-bottom: 20px; font-style: italic;">"UIが直感的で、チーム全員がすぐに使いこなせました。"</p>
                    <div style="display: flex; align-items: center;">
                      <div style="width: 50px; height: 50px; background: ${colors.primary}; border-radius: 50%; margin-right: 15px; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">佐</div>
                      <div>
                        <div style="font-weight: 600;">佐藤様</div>
                        <div style="color: #666; font-size: 14px;">XYZ Inc. / プロジェクトマネージャー</div>
                      </div>
                    </div>
                  </div>
                  <div class="card">
                    <div style="display: flex; margin-bottom: 15px;">
                      ${'★'.repeat(5)}
                    </div>
                    <p style="margin-bottom: 20px; font-style: italic;">"コストパフォーマンスが素晴らしく、ROIが期待以上でした。"</p>
                    <div style="display: flex; align-items: center;">
                      <div style="width: 50px; height: 50px; background: ${colors.primary}; border-radius: 50%; margin-right: 15px; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">山</div>
                      <div>
                        <div style="font-weight: 600;">山田様</div>
                        <div style="color: #666; font-size: 14px;">DEF Corp. / CEO</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          `;
          
        case 'pricing':
          return `
            <section class="pricing section">
              <div class="container">
                <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 40px; color: ${colors.secondary};">料金プラン</h2>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; max-width: 1000px; margin: 0 auto;">
                  <div class="card" style="text-align: center;">
                    <h3 style="color: ${colors.secondary}; margin-bottom: 10px;">ベーシック</h3>
                    <div style="font-size: 2.5rem; font-weight: 700; color: ${colors.primary}; margin-bottom: 20px;">¥9,800<span style="font-size: 1rem; font-weight: 400;">/月</span></div>
                    <ul style="list-style: none; margin-bottom: 30px;">
                      <li style="margin-bottom: 10px;">✅ 基本機能すべて</li>
                      <li style="margin-bottom: 10px;">✅ 5ユーザーまで</li>
                      <li style="margin-bottom: 10px;">✅ メールサポート</li>
                    </ul>
                    <a href="#contact" class="btn-primary" style="width: 100%;">選択する</a>
                  </div>
                  <div class="card" style="text-align: center; border: 3px solid ${colors.primary}; position: relative;">
                    <div style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); background: ${colors.primary}; color: white; padding: 5px 20px; border-radius: 20px; font-size: 14px; font-weight: 600;">人気</div>
                    <h3 style="color: ${colors.secondary}; margin-bottom: 10px;">プロ</h3>
                    <div style="font-size: 2.5rem; font-weight: 700; color: ${colors.primary}; margin-bottom: 20px;">¥19,800<span style="font-size: 1rem; font-weight: 400;">/月</span></div>
                    <ul style="list-style: none; margin-bottom: 30px;">
                      <li style="margin-bottom: 10px;">✅ 全機能無制限</li>
                      <li style="margin-bottom: 10px;">✅ 20ユーザーまで</li>
                      <li style="margin-bottom: 10px;">✅ 優先サポート</li>
                      <li style="margin-bottom: 10px;">✅ 高度な分析機能</li>
                    </ul>
                    <a href="#contact" class="btn-primary" style="width: 100%;">選択する</a>
                  </div>
                  <div class="card" style="text-align: center;">
                    <h3 style="color: ${colors.secondary}; margin-bottom: 10px;">エンタープライズ</h3>
                    <div style="font-size: 2.5rem; font-weight: 700; color: ${colors.primary}; margin-bottom: 20px;">要相談</div>
                    <ul style="list-style: none; margin-bottom: 30px;">
                      <li style="margin-bottom: 10px;">✅ カスタム機能</li>
                      <li style="margin-bottom: 10px;">✅ 無制限ユーザー</li>
                      <li style="margin-bottom: 10px;">✅ 専任サポート</li>
                      <li style="margin-bottom: 10px;">✅ オンサイト導入</li>
                    </ul>
                    <a href="#contact" class="btn-primary" style="width: 100%;">お問い合わせ</a>
                  </div>
                </div>
              </div>
            </section>
          `;
          
        case 'faq':
          return `
            <section class="faq section" style="background: white;">
              <div class="container">
                <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 40px; color: ${colors.secondary};">よくある質問</h2>
                <div style="max-width: 800px; margin: 0 auto;">
                  <div class="card">
                    <h3 style="color: ${colors.primary}; margin-bottom: 10px;">Q. 導入までどのくらい時間がかかりますか？</h3>
                    <p>A. 最短1日で導入可能です。複雑な設定は不要で、アカウント作成後すぐにご利用いただけます。</p>
                  </div>
                  <div class="card">
                    <h3 style="color: ${colors.primary}; margin-bottom: 10px;">Q. サポート体制はどうなっていますか？</h3>
                    <p>A. 平日9:00-18:00のメールサポート、プロプラン以上では優先サポートをご提供しています。</p>
                  </div>
                  <div class="card">
                    <h3 style="color: ${colors.primary}; margin-bottom: 10px;">Q. データの安全性は大丈夫ですか？</h3>
                    <p>A. 業界最高水準のセキュリティを採用し、定期的なセキュリティ監査も実施しています。</p>
                  </div>
                </div>
              </div>
            </section>
          `;
          
        case 'contact':
          return `
            <section class="contact section" style="background: ${colorScheme === 'gradient' ? colors.primary : colors.primary}; color: white;" id="contact">
              <div class="container">
                <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 40px;">今すぐ始めましょう</h2>
                <div style="max-width: 600px; margin: 0 auto; text-align: center;">
                  <p style="font-size: 1.2rem; margin-bottom: 40px; opacity: 0.95;">
                    ${productName}で${targetAudience}の課題を解決し、ビジネスを次のレベルへ
                  </p>
                  <div style="display: flex; gap: 20px; justify-content: center; flex-wrap: wrap;">
                    <a href="#" class="btn-primary" style="background: white; color: ${colors.primary}; font-size: 18px;">無料トライアル</a>
                    <a href="#" class="btn-primary" style="background: transparent; border: 2px solid white; font-size: 18px;">お問い合わせ</a>
                  </div>
                  <p style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
                    ✅ 無料トライアル14日間 ✅ クレジットカード不要 ✅ いつでもキャンセル可能
                  </p>
                </div>
              </div>
            </section>
          `;
          
        default:
          return '';
      }
    };

    // セクションを組み合わせてHTML生成
    const sectionsHTML = sections.map(section => generateSection(section)).join('\n');

    // 完全なHTMLドキュメント
    const fullHTML = `
<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${productName} - ${keyValue}</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&family=Montserrat:wght@400;500;600;700&family=Oswald:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        ${baseCSS}
        
        /* レスポンシブ対応 */
        @media (max-width: 768px) {
          .hero h1 { font-size: 2.5rem !important; }
          .hero p { font-size: 1.2rem !important; }
          .section { padding: 40px 0 !important; }
          .container { padding: 0 15px !important; }
          .btn-primary { padding: 14px 28px !important; font-size: 16px !important; }
        }
        
        /* スムーススクロール */
        html { scroll-behavior: smooth; }
        
        /* アニメーション */
        .card {
          transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 35px rgba(0,0,0,0.15);
        }
    </style>
</head>
<body>
    ${sectionsHTML}
    
    <script>
        // 簡単なアニメーション
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });
            
            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
    `;

    return {
      html: fullHTML,
      productName,
      style,
      colorScheme,
      sections,
      userRequest
    };
  },
}); 