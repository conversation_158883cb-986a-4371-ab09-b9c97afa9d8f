import { NextRequest, NextResponse } from 'next/server';

interface UnsplashPhoto {
  id: string;
  urls: {
    regular: string;
  };
  alt_description?: string;
  user: {
    name: string;
  };
  links: {
    download: string;
  };
  description?: string;
  width: number;
  height: number;
}

interface UnsplashResponse {
  results: UnsplashPhoto[];
  total: number;
}

export async function POST(request: NextRequest) {
  try {
    const { query, orientation = 'landscape', count = 9 } = await request.json();

    if (!query) {
      return NextResponse.json({ error: 'Query is required' }, { status: 400 });
    }

    // Unsplash API検索
    try {
      const response = await fetch(
        `https://api.unsplash.com/search/photos?query=${encodeURIComponent(query)}&orientation=${orientation}&per_page=${count}&client_id=${process.env.UNSPLASH_ACCESS_KEY}`
      );
      
      if (!response.ok) {
        // APIキーがない場合のフォールバック
        return NextResponse.json({
          images: [
            {
              id: 'placeholder-1',
              url: `https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&h=600&fit=crop&crop=center`,
              alt: `${query} image placeholder`,
              photographer: 'Unsplash',
              downloadUrl: 'https://unsplash.com/s/photos/' + encodeURIComponent(query),
              description: `${query}関連の画像プレースホルダー`,
            }
          ],
          searchQuery: query,
          message: 'プレースホルダー画像を提供しました。実際の画像取得にはUnsplash APIキーが必要です。',
        });
      }
      
      const data: UnsplashResponse = await response.json();
      const images = data.results.map((photo: UnsplashPhoto) => ({
        id: photo.id,
        url: photo.urls.regular,
        alt: photo.alt_description || `${query} image`,
        photographer: photo.user.name,
        downloadUrl: photo.links.download,
        description: photo.description || `${query}関連の画像`,
        width: photo.width,
        height: photo.height,
      }));
      
      return NextResponse.json({
        images,
        searchQuery: query,
        total: data.total,
        message: `${images.length}枚の画像を取得しました`,
      });
    } catch (apiError) {
      console.error('Unsplash API error:', apiError);
      // エラー時のフォールバック
      return NextResponse.json({
        images: [
          {
            id: 'fallback-1',
            url: `https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&h=600&fit=crop&crop=center`,
            alt: `${query} image`,
            photographer: 'Unsplash',
            downloadUrl: 'https://unsplash.com/s/photos/' + encodeURIComponent(query),
            description: `${query}関連の画像`,
          }
        ],
        searchQuery: query,
        message: 'フォールバック画像を提供しました。',
        error: 'API接続エラー',
      });
    }
  } catch (error) {
    console.error('Unsplash search error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}