// app/api/mastra/route-selector.ts
import { NextRequest, NextResponse } from 'next/server';
import { shouldUseMastra } from '../../mastra/utils/ab-test';

// Legacy request handler (placeholder)
async function handleLegacyRequest(): Promise<NextResponse> {
  // TODO: Implement actual legacy chat logic
  return NextResponse.json({
    message: 'Legacy implementation response',
    source: 'legacy'
  });
}

// Mastra request handler (placeholder)
async function handleMastraRequest(): Promise<NextResponse> {
  // TODO: Implement actual Mastra chat logic
  return NextResponse.json({
    message: 'Mastra implementation response',
    source: 'mastra'
  });
}

export const selectRoute = async (req: NextRequest, sessionId: string): Promise<NextResponse> => {
  try {
    if (shouldUseMastra(sessionId)) {
      return await handleMastraRequest();
    } else {
      return await handleLegacyRequest();
    }
  } catch (error) {
    console.error('Route selection error:', {
      error,
      sessionId,
      url: req.url,
      method: req.method
    });
    // Fallback to legacy implementation on error
    return await handleLegacyRequest();
  }
};

export { handleLegacyRequest, handleMastraRequest };