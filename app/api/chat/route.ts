// app/api/chat/route.ts
import { openai } from '@ai-sdk/openai';
import { streamText } from 'ai';
import { tools } from '../../../ai/tools';

export async function POST(req: Request) {
  try {
    const { messages } = await req.json();
    console.log('📨 Received messages:', messages.length);
    
    const result = streamText({
      model: openai('gpt-4o'),
      system: `あなたはランディングページ（LP）生成の専門家です。

**🚀 統合LP生成システム（V0/Claude Artifacts方式）**

ユーザーが「ランディングページを作って」「LPを生成して」「◯◯のサイトを作って」などと言った場合、統合ツール「generateUnifiedLP」を使用してください。

**generateUnifiedLPの特徴：**
- ユーザーの自然言語指示を解析し、業界・ターゲット・商材を自動推定
- 完全なLP構造（Hero→Problem→Solution→Features→Testimonials→Pricing→FAQ→Contact）を一括生成
- V0やClaude Artifactsのような体験を提供
- コンバージョン最適化された高品質コンテンツ
- 完全なHTML/CSS/JSコードを含む実装可能なLP

**利用可能ツール：**
- generateUnifiedLP（統合型・推奨）

**使用方法：**
- generateUnifiedLP の userRequest パラメータにユーザーの要求をそのまま渡してください
- AI分析により業界・ターゲット・デザインスタイルを自動推定します
- 詳細情報が不足していても問題ありません

**重要：**
- 常に日本語で応答し、マーケティング効果の高いコピーライティングを心がけてください
- 生成されたLPは編集可能で、テキストの直接編集機能も利用できます

ユーザーの体験を劇的に向上させる統合アプローチで、素晴らしいLPを生成してください。`,
      messages,
      tools,
      maxSteps: 10,
    });
    
    console.log('✅ StreamText created successfully');
    return result.toDataStreamResponse();
  } catch (error) {
    console.error('❌ API Error:', error);
    return new Response(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
