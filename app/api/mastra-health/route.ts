// app/api/mastra-health/route.ts
// Health check endpoint for Mastra integration
export async function GET() {
  try {
    // Check if Mastra is enabled
    const mastraEnabled = process.env.ENABLE_MASTRA === 'true';
    
    if (!mastraEnabled) {
      return Response.json({ 
        status: 'disabled',
        message: 'Mastra integration is currently disabled'
      });
    }

    // Basic health check for Mastra components
    return Response.json({ 
      status: 'healthy',
      message: 'Mastra integration is ready',
      timestamp: new Date().toISOString(),
      database: process.env.DATABASE_URL || 'file:./.mastra/memory.db'
    });
  } catch (error) {
    console.error('❌ Mastra Health Check Error:', error);
    return Response.json({ 
      status: 'error',
      message: 'Mastra health check failed'
    }, { status: 500 });
  }
}