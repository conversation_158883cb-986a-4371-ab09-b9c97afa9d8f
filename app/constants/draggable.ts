import { Resize<PERSON>andle } from '../types/image';

// サイズ関連の定数
export const DRAGGABLE_CONSTANTS = {
  MIN_SIZE: 50,
  DEFAULT_SIZE: { width: 300, height: 200 },
  HANDLE_SIZE: 8,
  HANDLE_OFFSET: -4, // ハンドルサイズの半分
  BORDER_WIDTH: 2,
  Z_INDEX: {
    DEFAULT: 1,
    SELECTED: 1000,
    HANDLE: 10,
    INDICATOR: 11,
  },
  ANIMATION: {
    TRANSITION_DURATION: 150,
    DEBOUNCE_DELAY: 16, // 60fps
  },
} as const;

// リサイズハンドルの定義
export const RESIZE_HANDLES: ResizeHandle[] = [
  { cursor: 'nw-resize', position: 'nw' },
  { cursor: 'n-resize', position: 'n' },
  { cursor: 'ne-resize', position: 'ne' },
  { cursor: 'e-resize', position: 'e' },
  { cursor: 'se-resize', position: 'se' },
  { cursor: 's-resize', position: 's' },
  { cursor: 'sw-resize', position: 'sw' },
  { cursor: 'w-resize', position: 'w' },
];

// カラーテーマ
export const COLORS = {
  PRIMARY: '#2196f3',
  WHITE: '#ffffff',
  TRANSPARENT: 'transparent',
} as const;

// タッチ関連
export const TOUCH = {
  MIN_MOVE_DISTANCE: 5,
  LONG_PRESS_DURATION: 500,
} as const; 