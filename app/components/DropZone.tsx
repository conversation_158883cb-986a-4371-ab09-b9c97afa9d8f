'use client';
import React, { useState, useRef } from 'react';
import { useEditMode } from '../contexts/EditModeContext';

interface ImageData {
  url: string;
  alt: string;
  photographer?: string;
  description?: string;
  width?: number;
  height?: number;
  position?: {
    x: number;
    y: number;
  };
  size?: {
    width: number;
    height: number;
  };
  zIndex?: number;
}

interface DropZoneProps {
  onImageDrop?: (imageData: ImageData, position: { x: number; y: number }) => void;
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
}

export function DropZone({
  onImageDrop,
  children,
  className = '',
  style = {},
}: DropZoneProps) {
  const {
    isEditMode,
    setDraggedImageId,
    setDragOverZone,
  } = useEditMode();

  const [isDropZoneActive, setIsDropZoneActive] = useState(false);
  const dropZoneRef = useRef<HTMLDivElement>(null);

  // ファイル形式の検証
  const validateImageFile = (file: File): boolean => {
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
    const maxSize = 10 * 1024 * 1024; // 10MB

    if (!validTypes.includes(file.type)) {
      alert('サポートされていないファイル形式です。JPEG、PNG、WebP、GIFファイルをお使いください。');
      return false;
    }

    if (file.size > maxSize) {
      alert('ファイルサイズが大きすぎます。10MB以下のファイルをお使いください。');
      return false;
    }

    return true;
  };

  // ファイルを読み込んでImageDataに変換
  const processImageFile = (file: File, dropPosition: { x: number; y: number }): Promise<ImageData> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        const img = new Image();
        
        img.onload = () => {
          const imageData: ImageData = {
            url: e.target?.result as string,
            alt: file.name,
            photographer: 'User Upload',
            description: `Uploaded file: ${file.name}`,
            width: img.naturalWidth,
            height: img.naturalHeight,
            position: dropPosition,
            size: {
              width: Math.min(img.naturalWidth, 400), // 最大幅を制限
              height: Math.min(img.naturalHeight, 300) // 最大高さを制限
            },
            zIndex: Date.now() // 新しい画像を上に表示
          };
          
          resolve(imageData);
        };
        
        img.onerror = () => {
          reject(new Error('画像の読み込みに失敗しました'));
        };
        
        img.src = e.target?.result as string;
      };
      
      reader.onerror = () => {
        reject(new Error('ファイルの読み込みに失敗しました'));
      };
      
      reader.readAsDataURL(file);
    });
  };

  // ドラッグエンター
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    setIsDropZoneActive(true);
    setDragOverZone('file-drop');
  };

  // ドラッグリーブ
  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    setIsDropZoneActive(false);
    setDragOverZone(null);
  };

  // ドラッグオーバー
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (isEditMode && e.dataTransfer.types.includes('Files')) {
      e.dataTransfer.dropEffect = 'copy';
    }
  };

  // ドロップ
  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    setIsDropZoneActive(false);
    setDragOverZone(null);

    if (!isEditMode) return;

    const files = Array.from(e.dataTransfer.files);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));

    if (imageFiles.length === 0) {
      alert('画像ファイルが見つかりませんでした。');
      return;
    }

    // ドロップ位置を計算
    const rect = dropZoneRef.current?.getBoundingClientRect();
    const dropPosition = rect ? {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    } : { x: 100, y: 100 };

    try {
      // 複数ファイルを順次処理
      for (let i = 0; i < imageFiles.length; i++) {
        const file = imageFiles[i];
        
        if (!validateImageFile(file)) continue;

        // 複数ファイルの場合は位置をずらす
        const offsetPosition = {
          x: dropPosition.x + (i * 20),
          y: dropPosition.y + (i * 20)
        };

        const imageData = await processImageFile(file, offsetPosition);
        const imageId = `dropped-${Date.now()}-${i}`;
        
        // グローバル状態に追加
        setDraggedImageId(imageId);
        
        // コールバック呼び出し
        onImageDrop?.(imageData, offsetPosition);
      }
      
      if (imageFiles.length > 1) {
        alert(`${imageFiles.length}個の画像ファイルをドロップしました。`);
      }
    } catch (error) {
      console.error('Image drop error:', error);
      alert('画像の処理中にエラーが発生しました。');
    }
  };

  return (
    <div
      ref={dropZoneRef}
      className={className}
      style={{
        position: 'relative',
        transition: 'all 0.2s ease',
        ...style,
        ...(isDropZoneActive && {
          backgroundColor: 'rgba(33, 150, 243, 0.1)',
          border: '2px dashed #2196f3',
          borderRadius: '8px',
        })
      }}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {/* ドロップオーバーレイ */}
      {isDropZoneActive && (
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(33, 150, 243, 0.2)',
          border: '2px dashed #2196f3',
          borderRadius: '8px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          pointerEvents: 'none',
        }}>
          <div style={{
            fontSize: '48px',
            marginBottom: '16px',
            opacity: 0.8,
          }}>
            📁
          </div>
          <div style={{
            fontSize: '18px',
            fontWeight: '600',
            color: '#2196f3',
            textAlign: 'center',
            marginBottom: '8px',
          }}>
            ここに画像をドロップ
          </div>
          <div style={{
            fontSize: '14px',
            color: '#555',
            textAlign: 'center',
          }}>
            JPEG, PNG, WebP, GIF (最大10MB)
          </div>
        </div>
      )}

      {children}
    </div>
  );
}