import React from 'react';

interface KeyboardInstructionsProps {
  imageId: string;
}

export const KeyboardInstructions: React.FC<KeyboardInstructionsProps> = ({ imageId }) => {
  return (
    <div
      id={`image-instructions-${imageId}`}
      className="sr-only"
    >
      矢印キーで移動、Shift+矢印キーでリサイズ、Escapeで選択解除
    </div>
  );
};

interface ResizeInstructionsProps {
  imageId: string;
}

export const ResizeInstructions: React.FC<ResizeInstructionsProps> = ({ imageId }) => {
  return (
    <div
      id={`resize-instructions-${imageId}`}
      className="sr-only"
    >
      ドラッグしてリサイズ、Shiftキーでアスペクト比保持
    </div>
  );
}; 