// app/components/FeatureFlagDemo.tsx
'use client';

import { useMastraFlags } from '../hooks/useMastraFlags';

export default function FeatureFlagDemo() {
  const { flags, loading, error, shouldUseMastra, experimentGroup } = useMastraFlags();

  if (loading) {
    return (
      <div className="p-6 bg-gray-50 rounded-lg">
        <h2 className="text-xl font-bold mb-4">Feature Flags Status</h2>
        <p>Loading feature flags...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-red-50 rounded-lg border border-red-200">
        <h2 className="text-xl font-bold mb-4 text-red-800">Feature Flags Error</h2>
        <p className="text-red-600">Error: {error}</p>
        <p className="text-sm text-red-500 mt-2">
          Falling back to safe defaults (all features disabled)
        </p>
      </div>
    );
  }

  return (
    <div className="p-6 bg-white rounded-lg border shadow-sm" data-testid="feature-flags-dashboard">
      <h2 className="text-xl font-bold mb-4">🎛️ Feature Flags Dashboard</h2>
      
      {/* Feature Flags Status */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Current Feature States</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {flags && Object.entries(flags).map(([key, value]) => (
            <div 
              key={key} 
              className={`p-3 rounded-lg border ${
                value 
                  ? 'bg-green-50 border-green-200 text-green-800' 
                  : 'bg-gray-50 border-gray-200 text-gray-600'
              }`}
            >
              <div className="flex items-center justify-between">
                <span className="font-medium">{key}</span>
                <span className={`w-3 h-3 rounded-full ${
                  value ? 'bg-green-500' : 'bg-gray-400'
                }`} />
              </div>
              <div className="text-sm mt-1">
                {value ? 'Enabled' : 'Disabled'}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* A/B Test Information */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">A/B Test Status</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          <div className={`p-3 rounded-lg border ${
            experimentGroup === 'mastra'
              ? 'bg-blue-50 border-blue-200 text-blue-800'
              : 'bg-purple-50 border-purple-200 text-purple-800'
          }`}>
            <div className="font-medium">Experiment Group</div>
            <div className="text-lg font-bold" data-testid="experiment-group-value">{experimentGroup}</div>
          </div>
          
          <div className={`p-3 rounded-lg border ${
            shouldUseMastra
              ? 'bg-green-50 border-green-200 text-green-800'
              : 'bg-orange-50 border-orange-200 text-orange-800'
          }`}>
            <div className="font-medium">Should Use Mastra</div>
            <div className="text-lg font-bold">
              {shouldUseMastra ? 'Yes' : 'No'}
            </div>
          </div>
        </div>
      </div>

      {/* Implementation Status */}
      <div>
        <h3 className="text-lg font-semibold mb-2">Implementation Behavior</h3>
        <div className={`p-4 rounded-lg border ${
          shouldUseMastra
            ? 'bg-blue-50 border-blue-200'
            : 'bg-gray-50 border-gray-200'
        }`}>
          <div className="font-medium text-sm uppercase tracking-wide mb-2">
            Active Implementation
          </div>
          <div className="text-lg font-bold mb-2">
            {shouldUseMastra ? '🚀 Mastra System' : '🏛️ Legacy System'}
          </div>
          <div className="text-sm text-gray-600">
            {shouldUseMastra 
              ? 'Using Mastra agents, tools, and workflow engine'
              : 'Using traditional implementation with fallback behavior'
            }
          </div>
        </div>
      </div>
    </div>
  );
}