'use client';
import { useChat } from 'ai/react';
import { useEffect } from 'react';
import type { Message } from 'ai';

interface ChatPanelClaudeProps {
  onMessagesUpdate: (messages: Message[]) => void;
}

export default function ChatPanelClaude({ onMessagesUpdate }: ChatPanelClaudeProps) {
  const { messages, input, handleInputChange, handleSubmit, isLoading, error } = useChat({
    api: '/api/chat-claude',
  });

  // メッセージが更新されたら親コンポーネントに通知
  useEffect(() => {
    onMessagesUpdate(messages);
  }, [messages, onMessagesUpdate]);

  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      height: '100%',
      minHeight: '0'
    }}>
      {/* デバッグ情報 */}
      <div style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>
        Claude AI SDK | Status: {isLoading ? 'streaming' : 'ready'} | Input: &quot;{input}&quot; | Length: {input.length} | Messages: {messages.length}
      </div>
      
      {/* 処理状態の表示 */}
      {isLoading && (
        <div style={{ 
          padding: '12px', 
          backgroundColor: '#f3e8ff', 
          borderRadius: '8px',
          marginBottom: '8px',
          color: '#7c3aed',
          fontSize: '14px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          <div style={{ 
            width: '16px', 
            height: '16px', 
            border: '2px solid #7c3aed', 
            borderTop: '2px solid transparent',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }} />
          Claude がランディングページを生成中です...
        </div>
      )}
      
      {error && (
        <div style={{ 
          padding: '12px', 
          backgroundColor: '#ffebee', 
          borderRadius: '8px',
          marginBottom: '8px',
          color: '#c62828',
          fontSize: '14px'
        }}>
          ❌ エラーが発生しました: {error.message}
        </div>
      )}

      {/* メッセージ履歴 */}
      <div style={{ 
        flex: 1,
        overflowY: 'auto', 
        marginBottom: '16px',
        padding: '12px',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px',
        minHeight: '200px'
      }}>
        {messages.length === 0 ? (
          <div style={{ 
            textAlign: 'center', 
            color: '#333', 
            fontSize: '14px',
            padding: '20px'
          }}>
            Claude でランディングページの生成を開始してください...
          </div>
        ) : (
          messages.map((msg: Message) => (
            <div 
              key={msg.id} 
              style={{ 
                marginBottom: '12px',
                padding: '12px',
                borderRadius: '8px',
                backgroundColor: msg.role === 'user' ? '#f3e8ff' : '#ffffff',
                border: '1px solid #e0e0e0'
              }}
            >
              <div style={{ 
                fontSize: '12px', 
                fontWeight: '600', 
                marginBottom: '4px',
                color: msg.role === 'user' ? '#7c3aed' : '#333'
              }}>
                {msg.role === 'user' ? '👤 You' : '🤖 Claude'}
              </div>
              <div style={{ 
                fontSize: '14px', 
                lineHeight: '1.4',
                color: '#333',
                whiteSpace: 'pre-wrap'
              }}>
                {msg.content}
              </div>
              
              {/* ツール実行状況の表示 */}
              {msg.toolInvocations && msg.toolInvocations.length > 0 && (
                <div style={{ marginTop: '8px' }}>
                  {msg.toolInvocations.map((tool, index) => (
                    <div key={index} style={{ 
                      fontSize: '12px', 
                      color: '#7c3aed',
                      backgroundColor: '#f3e8ff',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      margin: '2px 0'
                    }}>
                      🔧 {tool.toolName} {tool.state === 'result' ? '✅' : '⏳'}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {/* 入力フォーム */}
      <form onSubmit={handleSubmit} style={{ display: 'flex', gap: '8px' }}>
        <input
          name="prompt"
          value={input}
          onChange={handleInputChange}
          placeholder="例：「SaaSツールのヒーローセクションを作って」"
          disabled={isLoading}
          style={{
            flex: 1,
            padding: '12px',
            border: '1px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '14px',
            outline: 'none',
            backgroundColor: isLoading ? '#f5f5f5' : 'white',
            color: '#333'
          }}
        />
        <button 
          type="submit" 
          disabled={!input.trim() || isLoading}
          style={{
            padding: '12px 20px',
            backgroundColor: (!input.trim() || isLoading) ? '#e0e0e0' : '#7c3aed',
            color: (!input.trim() || isLoading) ? '#666' : 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '600',
            cursor: (!input.trim() || isLoading) ? 'not-allowed' : 'pointer',
            transition: 'background-color 0.2s'
          }}
        >
          {isLoading ? '生成中...' : 'Claude で生成'}
        </button>
      </form>
    </div>
  );
} 