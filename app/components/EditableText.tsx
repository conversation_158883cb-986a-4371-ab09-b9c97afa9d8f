'use client';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useEditMode } from '../contexts/EditModeContext';

interface EditableTextProps {
  value: string;
  onChange: (newValue: string) => void;
  placeholder?: string;
  multiline?: boolean;
  maxLength?: number;
  className?: string;
  style?: React.CSSProperties;
  editableWhen?: boolean;
  component?: 'span' | 'h1' | 'h2' | 'h3' | 'p' | 'div';
}

export const EditableText: React.FC<EditableTextProps> = ({
  value,
  onChange,
  placeholder = 'Click to edit text...',
  multiline = false,
  maxLength,
  className = '',
  style = {},
  editableWhen = true,
  component = 'span'
}) => {
  const { isEditMode, isTextEditMode, editingTextId, setEditingTextId } = useEditMode();
  const [isEditing, setIsEditing] = useState(false);
  
  // valueがundefinedの場合に空文字列をデフォルトとして使用
  const safeValue = value || '';
  const [tempValue, setTempValue] = useState(safeValue);
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);
  const elementRef = useRef<HTMLElement>(null);
  const textId = useRef<string>(`editable-${Math.random().toString(36).substr(2, 9)}`);

  const shouldShowEditable = isEditMode && isTextEditMode && editableWhen;
  const isCurrentlyEditing = isEditing && editingTextId === textId.current;

  const handleCancel = useCallback(() => {
    setTempValue(safeValue);
    setIsEditing(false);
    setEditingTextId(null);
  }, [safeValue, setEditingTextId]);

  // Update temp value when external value changes
  useEffect(() => {
    if (!isCurrentlyEditing) {
      setTempValue(safeValue);
    }
  }, [safeValue, isCurrentlyEditing]);

  // Focus input when entering edit mode
  useEffect(() => {
    if (isCurrentlyEditing && inputRef.current) {
      inputRef.current.focus();
      if (multiline) {
        inputRef.current.setSelectionRange(0, inputRef.current.value.length);
      } else {
        (inputRef.current as HTMLInputElement).select();
      }
    }
  }, [isCurrentlyEditing, multiline]);

  // Handle clicks outside to cancel editing
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isCurrentlyEditing && 
          inputRef.current && 
          !inputRef.current.contains(event.target as Node)) {
        handleCancel();
      }
    };

    if (isCurrentlyEditing) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isCurrentlyEditing, handleCancel]);

  // ESCキーでキャンセル
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        handleCancel();
      }
    };

    if (isEditing) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isEditing, handleCancel]);

  const handleClick = useCallback(() => {
    if (shouldShowEditable && !isCurrentlyEditing) {
      setIsEditing(true);
      setEditingTextId(textId.current);
      setTempValue(safeValue);
    }
  }, [shouldShowEditable, isCurrentlyEditing, setEditingTextId, safeValue]);

  const handleConfirm = useCallback(() => {
    if (tempValue !== safeValue) {
      onChange(tempValue);
    }
    setIsEditing(false);
    setEditingTextId(null);
  }, [tempValue, safeValue, onChange, setEditingTextId]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !multiline) {
      e.preventDefault();
      handleConfirm();
    } else if (e.key === 'Enter' && multiline && e.ctrlKey) {
      e.preventDefault();
      handleConfirm();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  }, [multiline, handleConfirm, handleCancel]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    if (!maxLength || newValue.length <= maxLength) {
      setTempValue(newValue);
    }
  }, [maxLength]);

  // Dynamic styles for editing state
  const editableStyles: React.CSSProperties = shouldShowEditable ? {
    cursor: 'pointer',
    position: 'relative',
    outline: isFocused ? '2px solid #007bff' : 'none',
    borderRadius: '4px',
    padding: '2px 4px',
    margin: '-2px -4px',
    transition: 'all 0.2s ease',
    backgroundColor: isFocused ? 'rgba(0, 123, 255, 0.1)' : 'transparent',
  } : {};

  const inputStyles: React.CSSProperties = {
    ...style,
    border: '2px solid #007bff',
    borderRadius: '4px',
    padding: '4px 8px',
    fontSize: 'inherit',
    fontFamily: 'inherit',
    fontWeight: 'inherit',
    color: 'inherit',
    backgroundColor: 'white',
    outline: 'none',
    width: '100%',
    minHeight: multiline ? '60px' : 'auto',
    resize: multiline ? 'vertical' : 'none',
    lineHeight: 'inherit',
  };

  // Show placeholder if value is empty
  const displayValue = safeValue || (shouldShowEditable ? placeholder : '');
  const isEmpty = !safeValue;

  if (isCurrentlyEditing) {
    const InputComponent = multiline ? 'textarea' : 'input';
    return (
      <InputComponent
        ref={inputRef as React.RefObject<HTMLInputElement & HTMLTextAreaElement>}
        value={tempValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        maxLength={maxLength}
        style={inputStyles}
        className={className}
        autoComplete="off"
        spellCheck={false}
      />
    );
  }

  const combinedStyles = {
    ...style,
    ...editableStyles,
    color: isEmpty && shouldShowEditable ? '#999' : style.color,
    fontStyle: isEmpty && shouldShowEditable ? 'italic' : style.fontStyle,
  };

  const Component = component;

  return (
    <Component
      ref={elementRef as React.RefObject<HTMLElement>}
      onClick={handleClick}
      onMouseEnter={() => shouldShowEditable && setIsFocused(true)}
      onMouseLeave={() => setIsFocused(false)}
      style={combinedStyles}
      className={className}
      title={shouldShowEditable ? 'Click to edit' : undefined}
      data-editable={shouldShowEditable ? 'true' : 'false'}
      data-text-id={textId.current}
      tabIndex={shouldShowEditable ? 0 : -1}
    >
      {displayValue}
      {shouldShowEditable && maxLength && (
        <span style={{
          fontSize: '12px',
          color: '#999',
          marginLeft: '8px',
          opacity: isFocused ? 1 : 0,
          transition: 'opacity 0.2s ease'
        }}>
          {safeValue.length}/{maxLength}
        </span>
      )}
    </Component>
  );
};