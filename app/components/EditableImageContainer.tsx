'use client';
import React, { useState, useEffect } from 'react';
import { useEditMode } from '../contexts/EditModeContext';
import { DraggableImage } from './DraggableImage';
import { DropZone } from './DropZone';

interface ImageData {
  url: string;
  alt: string;
  photographer?: string;
  description?: string;
  width?: number;
  height?: number;
  position?: {
    x: number;
    y: number;
  };
  size?: {
    width: number;
    height: number;
  };
  zIndex?: number;
}

interface EditableImageContainerProps {
  imageId: string;
  imageData: ImageData | null;
  onImageChange?: (imageData: ImageData) => void;
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
}

export function EditableImageContainer({
  imageId,
  imageData,
  onImageChange,
  className,
  style,
  children
}: EditableImageContainerProps) {
  const { isEditMode, selectedImageId, setSelectedImageId, setIsImageSelectorOpen, getImage, updateImage } = useEditMode();
  const [isHovered, setIsHovered] = useState(false);

  // グローバル画像状態をチェック
  const globalImageData = getImage(imageId);
  const currentImageData = globalImageData || imageData;

  // グローバル画像状態が更新された場合、ローカルコンポーネントに通知
  useEffect(() => {
    if (globalImageData && onImageChange) {
      onImageChange(globalImageData);
    }
  }, [globalImageData, onImageChange]);

  const handleClick = () => {
    if (isEditMode) {
      setSelectedImageId(imageId);
      setIsImageSelectorOpen(true);
    }
  };

  const handleImageDrop = (droppedImageData: ImageData, position: { x: number; y: number }) => {
    // ドロップされた画像を現在のコンテナに設定
    const imageDataWithPosition = {
      ...droppedImageData,
      position,
    };
    
    updateImage(imageId, imageDataWithPosition);
    onImageChange?.(imageDataWithPosition);
  };

  const isSelected = isEditMode && selectedImageId === imageId;

  // 画像がある場合はDraggableImageを使用、ない場合は従来のプレースホルダー
  if (currentImageData) {
    return (
      <DropZone
        className={className}
        style={style}
        onImageDrop={handleImageDrop}
      >
        <DraggableImage
          imageId={imageId}
          imageData={currentImageData}
          onImageChange={onImageChange}
        />
        {children}
      </DropZone>
    );
  }

  return (
    <DropZone
      className={className}
      style={style}
      onImageDrop={handleImageDrop}
    >
      <div
        style={{
          position: 'relative',
          cursor: isEditMode ? 'pointer' : 'default',
        }}
        onClick={handleClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* 編集可能インジケーター */}
        {isEditMode && (isHovered || isSelected) && (
          <div style={{
            position: 'absolute',
            top: 8,
            left: 8,
            backgroundColor: '#2196f3',
            color: 'white',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            fontWeight: '500',
            zIndex: 10,
            boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
          }}>
            🖼️ 画像を変更・ドロップ
          </div>
        )}

        {/* 編集モード時のオーバーレイ */}
        {isEditMode && (isHovered || isSelected) && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: isSelected ? 'rgba(33, 150, 243, 0.3)' : 'rgba(33, 150, 243, 0.1)',
            border: isSelected ? '2px solid #2196f3' : '2px solid transparent',
            borderRadius: '8px',
            zIndex: 5,
            transition: 'all 0.2s ease',
          }} />
        )}

        {/* プレースホルダー画像（画像がない場合） */}
        <div style={{
          width: '100%',
          height: '100%',
          minHeight: '200px',
          backgroundColor: '#f5f5f5',
          border: '2px dashed #ddd',
          borderRadius: '8px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#333',
          fontSize: '14px',
        }}>
          <div style={{ fontSize: '24px', marginBottom: '8px' }}>🖼️</div>
          <div>画像を挿入</div>
          {isEditMode && (
            <div style={{ fontSize: '12px', marginTop: '4px', color: '#555', textAlign: 'center' }}>
              クリックして画像を選択<br />
              またはファイルをドラッグ&ドロップ
            </div>
          )}
        </div>

        {children}
      </div>
    </DropZone>
  );
}