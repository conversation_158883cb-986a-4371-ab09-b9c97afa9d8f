// app/components/ChatPanel.tsx
'use client';
import { useChat } from '@ai-sdk/react';
import type { Message } from 'ai';
import { FormEvent, useEffect, useCallback } from 'react';

interface ChatPanelProps {
  onMessagesUpdate: (messages: Message[]) => void;
  externalMessage?: string;
  onExternalMessageProcessed?: () => void;
}

export default function ChatPanel({ 
  onMessagesUpdate, 
  externalMessage, 
  onExternalMessageProcessed 
}: ChatPanelProps) {
  const { messages, input, handleInputChange, handleSubmit, status, append } = useChat({
    api: '/api/chat',
  });

  // メッセージが更新されたら親コンポーネントに通知
  const memoizedOnMessagesUpdate = useCallback(onMessagesUpdate, [onMessagesUpdate]);
  
  useEffect(() => {
    memoizedOnMessagesUpdate(messages);
  }, [messages, memoizedOnMessagesUpdate]);

  // 外部からのメッセージを処理
  const memoizedOnExternalMessageProcessed = useCallback(() => {
    onExternalMessageProcessed?.();
  }, [onExternalMessageProcessed]);

  useEffect(() => {
    if (externalMessage) {
      append({
        role: 'user',
        content: externalMessage
      });
      memoizedOnExternalMessageProcessed();
    }
  }, [externalMessage, append, memoizedOnExternalMessageProcessed]);

  const onFormSubmit = (e: FormEvent<HTMLFormElement>) => {
    handleSubmit(e);
  };

  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      height: '100%',
      minHeight: '0'
    }}>
      {/* メッセージ履歴 */}
      <div style={{ 
        flex: 1,
        overflowY: 'auto', 
        marginBottom: '16px',
        padding: '12px',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px',
        minHeight: '200px'
      }}>
        {messages.length === 0 ? (
          <div style={{ 
            textAlign: 'center', 
            color: '#333', 
            fontSize: '14px',
            padding: '20px'
          }}>
            ランディングページの生成を開始してください...
          </div>
        ) : (
          messages.map((msg: Message) => (
            <div 
              key={msg.id} 
              style={{ 
                marginBottom: '12px',
                padding: '12px',
                borderRadius: '8px',
                backgroundColor: msg.role === 'user' ? '#e3f2fd' : '#ffffff',
                border: '1px solid #e0e0e0'
              }}
            >
              <div style={{ 
                fontSize: '12px', 
                fontWeight: '600', 
                marginBottom: '4px',
                color: msg.role === 'user' ? '#1976d2' : '#333'
              }}>
                {msg.role === 'user' ? '👤 You' : '🤖 AI'}
              </div>
              <div style={{ 
                fontSize: '14px', 
                lineHeight: '1.4',
                color: '#333',
                whiteSpace: 'pre-wrap'
              }}>
                {msg.content}
              </div>
              
              {/* ツール呼び出し状況の表示 */}
              {msg.toolInvocations && msg.toolInvocations.length > 0 && (
                <div style={{ marginTop: '8px' }}>
                  {msg.toolInvocations.map((inv, index) => (
                    <div key={index} style={{ 
                      fontSize: '12px',
                      color: '#666',
                      backgroundColor: '#f0f0f0',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      marginBottom: '4px'
                    }}>
                      🔧 {inv.toolName}: {inv.state === 'result' ? '完了' : inv.state === 'call' ? '実行中' : inv.state}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {/* デバッグ情報 */}
      <div style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>
        Status: {status} | Input: &quot;{input}&quot; | Length: {input.length} | Messages: {messages.length}
      </div>
      
      {/* 処理状態の表示 */}
      {status === 'streaming' && (
        <div style={{ 
          padding: '12px', 
          backgroundColor: '#e3f2fd', 
          borderRadius: '8px',
          marginBottom: '8px',
          color: '#1976d2',
          fontSize: '14px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          <div style={{ 
            width: '16px', 
            height: '16px', 
            border: '2px solid #1976d2', 
            borderTop: '2px solid transparent',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }} />
          AIがランディングページを生成中です...
        </div>
      )}
      
      {status === 'error' && (
        <div style={{ 
          padding: '12px', 
          backgroundColor: '#ffebee', 
          borderRadius: '8px',
          marginBottom: '8px',
          color: '#c62828',
          fontSize: '14px'
        }}>
          ❌ エラーが発生しました。もう一度お試しください。
        </div>
      )}

      {/* 入力フォーム */}
      <form onSubmit={onFormSubmit} style={{ display: 'flex', gap: '8px' }}>
        <input
          name="prompt"
          value={input}
          onChange={handleInputChange}
          placeholder="例：「SaaSツールのヒーローセクションを作って」"
          disabled={status !== 'ready'}
          style={{
            flex: 1,
            padding: '12px',
            border: '1px solid #e0e0e0',
            borderRadius: '8px',
            fontSize: '14px',
            outline: 'none',
            backgroundColor: status !== 'ready' ? '#f5f5f5' : 'white',
            color: '#333'
          }}
        />
        <button 
          type="submit" 
          disabled={!input.trim()}
          style={{
            padding: '12px 20px',
            backgroundColor: !input.trim() ? '#e0e0e0' : '#3b82f6',
            color: !input.trim() ? '#666' : 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '600',
            cursor: !input.trim() ? 'not-allowed' : 'pointer',
            transition: 'background-color 0.2s'
          }}
        >
          {status === 'streaming' ? '生成中...' : '生成'}
        </button>
      </form>
    </div>
  );
}
