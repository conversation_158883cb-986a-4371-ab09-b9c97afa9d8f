'use client';
import React, { useRef, useEffect } from 'react';

interface UnifiedLPViewerProps {
  html: string;
  productName: string;
  style: string;
  colorScheme: string;
  sections: string[];
  userRequest: string;
}

export function UnifiedLPViewer({ 
  html, 
  productName, 
  style, 
  colorScheme, 
  sections,
  userRequest 
}: UnifiedLPViewerProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    if (iframeRef.current && html) {
      const iframe = iframeRef.current;
      iframe.srcdoc = html;
    }
  }, [html]);

  const downloadHTML = () => {
    const blob = new Blob([html], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${productName.replace(/\s+/g, '-').toLowerCase()}-landing-page.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div style={{
      margin: '20px 0',
      border: '2px solid #e0e0e0',
      borderRadius: '12px',
      overflow: 'hidden',
      backgroundColor: 'white'
    }}>
      {/* ヘッダー情報 */}
      <div style={{
        padding: '16px 20px',
        backgroundColor: '#f8f9fa',
        borderBottom: '1px solid #e0e0e0',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: '10px'
      }}>
        <div>
          <h3 style={{ 
            margin: '0 0 8px 0', 
            color: '#333',
            fontSize: '18px',
            fontWeight: '600'
          }}>
            🚀 統合LP: {productName}
          </h3>
          <div style={{ 
            fontSize: '14px', 
            color: '#666',
            display: 'flex',
            gap: '15px',
            flexWrap: 'wrap'
          }}>
            <span>🎨 スタイル: <strong>{style}</strong></span>
            <span>🎯 カラー: <strong>{colorScheme}</strong></span>
            <span>📋 セクション数: <strong>{sections.length}</strong></span>
          </div>
        </div>
        
        <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
          <button
            onClick={downloadHTML}
            style={{
              padding: '8px 16px',
              backgroundColor: '#4caf50',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              fontSize: '14px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '6px'
            }}
          >
            💾 HTMLダウンロード
          </button>
          
          <button
            onClick={() => {
              if (iframeRef.current) {
                const newWindow = window.open('', '_blank');
                if (newWindow) {
                  newWindow.document.write(html);
                  newWindow.document.close();
                }
              }
            }}
            style={{
              padding: '8px 16px',
              backgroundColor: '#2196f3',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              fontSize: '14px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '6px'
            }}
          >
            🔗 新しいタブで開く
          </button>
        </div>
      </div>

      {/* セクション情報 */}
      <div style={{
        padding: '12px 20px',
        backgroundColor: '#fff3cd',
        borderBottom: '1px solid #e0e0e0',
        fontSize: '14px',
        color: '#333'
      }}>
        <strong style={{ color: '#333' }}>生成されたセクション:</strong> {sections.map(section => `📄 ${section}`).join(' | ')}
      </div>

      {/* リクエスト情報 */}
      <div style={{
        padding: '12px 20px',
        backgroundColor: '#d1ecf1',
        borderBottom: '1px solid #e0e0e0',
        fontSize: '14px',
        color: '#333'
      }}>
        <strong style={{ color: '#333' }}>ユーザーリクエスト:</strong> {userRequest}
      </div>

      {/* iframe プレビュー */}
      <div style={{ position: 'relative' }}>
        <iframe
          ref={iframeRef}
          style={{
            width: '100%',
            height: '600px',
            border: 'none',
            backgroundColor: 'white'
          }}
          title={`${productName} Landing Page Preview`}
        />
        
        {/* フルスクリーンボタン */}
        <button
          onClick={() => {
            if (iframeRef.current) {
              if (iframeRef.current.requestFullscreen) {
                iframeRef.current.requestFullscreen();
              }
            }
          }}
          style={{
            position: 'absolute',
            top: '10px',
            right: '10px',
            padding: '8px',
            backgroundColor: 'rgba(0,0,0,0.7)',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '12px',
            cursor: 'pointer',
            opacity: 0.8
          }}
          title="フルスクリーン表示"
        >
          ⛶
        </button>
      </div>

      {/* フッター情報 */}
      <div style={{
        padding: '12px 20px',
        backgroundColor: '#f8f9fa',
        borderTop: '1px solid #e0e0e0',
        fontSize: '12px',
        color: '#666',
        textAlign: 'center'
      }}>
        💡 V0/Claude Artifacts風の統合LP生成により作成されました | 
        完全なHTML/CSS/JSコードが含まれています
      </div>
    </div>
  );
} 