'use client';
import React, { createContext, useContext, useState, ReactNode, useCallback } from 'react';
import { ImageData } from '../types/image';
import { DRAGGABLE_CONSTANTS } from '../constants/draggable';

interface EditModeContextType {
  isEditMode: boolean;
  toggleEditMode: () => void;
  selectedImageId: string | null;
  setSelectedImageId: (id: string | null) => void;
  isImageSelectorOpen: boolean;
  setIsImageSelectorOpen: (open: boolean) => void;
  // テキスト編集状態管理
  isTextEditMode: boolean;
  toggleTextEditMode: () => void;
  editingTextId: string | null;
  setEditingTextId: (id: string | null) => void;
  // グローバル画像状態管理
  images: Record<string, ImageData>;
  updateImage: (imageId: string, imageData: ImageData) => void;
  getImage: (imageId: string) => ImageData | null;
  // ドラッグ&ドロップ状態管理
  isDragging: boolean;
  setIsDragging: (dragging: boolean) => void;
  draggedImageId: string | null;
  setDraggedImageId: (id: string | null) => void;
  dragOverZone: string | null;
  setDragOverZone: (zone: string | null) => void;
  // Z-index管理
  bringToFront: (imageId: string) => void;
  sendToBack: (imageId: string) => void;
  getNextZIndex: () => number;
}

const EditModeContext = createContext<EditModeContextType | undefined>(undefined);

interface EditModeProviderProps {
  children: ReactNode;
}

export function EditModeProvider({ children }: EditModeProviderProps) {
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedImageId, setSelectedImageId] = useState<string | null>(null);
  const [isImageSelectorOpen, setIsImageSelectorOpen] = useState(false);
  const [images, setImages] = useState<Record<string, ImageData>>({});
  // テキスト編集状態
  const [isTextEditMode, setIsTextEditMode] = useState(false);
  const [editingTextId, setEditingTextId] = useState<string | null>(null);
  // ドラッグ&ドロップ状態
  const [isDragging, setIsDragging] = useState(false);
  const [draggedImageId, setDraggedImageId] = useState<string | null>(null);
  const [dragOverZone, setDragOverZone] = useState<string | null>(null);
  // Z-index管理
  const [maxZIndex, setMaxZIndex] = useState<number>(DRAGGABLE_CONSTANTS.Z_INDEX.DEFAULT);

  const toggleEditMode = () => {
    setIsEditMode(!isEditMode);
    // 編集モード終了時は選択状態をクリア
    if (isEditMode) {
      setSelectedImageId(null);
      setIsImageSelectorOpen(false);
      setIsDragging(false);
      setDraggedImageId(null);
      setDragOverZone(null);
      setIsTextEditMode(false);
      setEditingTextId(null);
    }
  };

  const toggleTextEditMode = () => {
    setIsTextEditMode(!isTextEditMode);
    // テキスト編集モード終了時は編集中のテキストをクリア
    if (isTextEditMode) {
      setEditingTextId(null);
    }
  };

  const updateImage = useCallback((imageId: string, imageData: ImageData) => {
    setImages(prev => ({
      ...prev,
      [imageId]: imageData
    }));
  }, []);

  const getImage = useCallback((imageId: string): ImageData | null => {
    return images[imageId] || null;
  }, [images]);

  const getNextZIndex = useCallback(() => {
    const newZIndex = maxZIndex + 1;
    setMaxZIndex(newZIndex);
    return newZIndex;
  }, [maxZIndex]);

  const bringToFront = useCallback((imageId: string) => {
    const image = images[imageId];
    if (image) {
      const newZIndex = getNextZIndex();
      updateImage(imageId, { ...image, zIndex: newZIndex });
    }
  }, [images, getNextZIndex, updateImage]);

  const sendToBack = useCallback((imageId: string) => {
    const image = images[imageId];
    if (image) {
      updateImage(imageId, { ...image, zIndex: DRAGGABLE_CONSTANTS.Z_INDEX.DEFAULT });
    }
  }, [images, updateImage]);

  const handleSetSelectedImageId = useCallback((id: string | null) => {
    setSelectedImageId(id);
    // 選択時に前面に移動
    if (id && images[id]) {
      bringToFront(id);
    }
  }, [images, bringToFront]);

  return (
    <EditModeContext.Provider
      value={{
        isEditMode,
        toggleEditMode,
        selectedImageId,
        setSelectedImageId: handleSetSelectedImageId,
        isImageSelectorOpen,
        setIsImageSelectorOpen,
        isTextEditMode,
        toggleTextEditMode,
        editingTextId,
        setEditingTextId,
        images,
        updateImage,
        getImage,
        isDragging,
        setIsDragging,
        draggedImageId,
        setDraggedImageId,
        dragOverZone,
        setDragOverZone,
        bringToFront,
        sendToBack,
        getNextZIndex,
      }}
    >
      {children}
    </EditModeContext.Provider>
  );
}

export function useEditMode() {
  const context = useContext(EditModeContext);
  if (context === undefined) {
    throw new Error('useEditMode must be used within an EditModeProvider');
  }
  return context;
}