export interface AccessibilityProps {
  role?: string;
  'aria-label'?: string;
  'aria-describedby'?: string;
  'aria-selected'?: boolean;
  'aria-dragged'?: boolean;
  tabIndex?: number;
}

export const createDraggableAccessibilityProps = (
  imageId: string,
  isSelected: boolean,
  isDragging: boolean,
  isEditMode: boolean
): AccessibilityProps => {
  if (!isEditMode) {
    return {
      role: 'img',
      tabIndex: -1,
    };
  }

  return {
    role: 'button',
    'aria-label': `ドラッグ可能な画像 ${imageId}${isSelected ? ' (選択中)' : ''}`,
    'aria-describedby': `image-instructions-${imageId}`,
    'aria-selected': isSelected,
    'aria-dragged': isDragging,
    tabIndex: 0,
  };
};

export const createResizeHandleAccessibilityProps = (
  position: string,
  imageId: string
): AccessibilityProps => {
  const positionNames: Record<string, string> = {
    'nw': '左上',
    'n': '上',
    'ne': '右上',
    'e': '右',
    'se': '右下',
    's': '下',
    'sw': '左下',
    'w': '左',
  };

  return {
    role: 'button',
    'aria-label': `${positionNames[position] || position}のリサイズハンドル`,
    'aria-describedby': `resize-instructions-${imageId}`,
    tabIndex: 0,
  };
};

export const announceToScreenReader = (message: string): void => {
  // ARIAライブリージョンに動的にメッセージを追加
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', 'polite');
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.textContent = message;
  
  document.body.appendChild(announcement);
  
  // 短時間後に削除
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
};

export const createKeyboardInstructionsElement = (imageId: string): HTMLDivElement => {
  const div = document.createElement('div');
  div.id = `image-instructions-${imageId}`;
  div.className = 'sr-only';
  div.textContent = '矢印キーで移動、Shift+矢印キーでリサイズ、Escapeで選択解除';
  return div;
};

export const createResizeInstructionsElement = (imageId: string): HTMLDivElement => {
  const div = document.createElement('div');
  div.id = `resize-instructions-${imageId}`;
  div.className = 'sr-only';
  div.textContent = 'ドラッグしてリサイズ、Shiftキーでアスペクト比保持';
  return div;
}; 