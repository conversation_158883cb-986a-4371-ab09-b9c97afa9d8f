import { ImageData } from '../types/image';

export class ImageError extends Error {
  constructor(
    message: string,
    public readonly imageId: string,
    public readonly imageUrl?: string,
    public readonly originalError?: Error
  ) {
    super(message);
    this.name = 'ImageError';
  }
}

export interface ImageErrorInfo {
  imageId: string;
  error: ImageError;
  timestamp: number;
  retryCount: number;
}

export const createImageError = (
  message: string,
  imageId: string,
  imageUrl?: string,
  originalError?: Error
): ImageError => {
  return new ImageError(message, imageId, imageUrl, originalError);
};

export const handleImageLoadError = (
  imageId: string,
  imageUrl: string,
  onError?: (error: Error) => void
): void => {
  // URLが長すぎる場合は短縮して表示
  const displayUrl = imageUrl.length > 50 ? imageUrl.substring(0, 50) + '...' : imageUrl;
  
  const error = createImageError(
    `画像の読み込みに失敗しました: ${displayUrl}`,
    imageId,
    imageUrl
  );
  
  console.warn('Image load error:', {
    imageId,
    imageUrl,
    timestamp: new Date().toISOString()
  });
  
  // エラーを投げずに、コールバックのみ実行
  if (onError) {
    try {
      onError(error);
    } catch (callbackError) {
      console.error('Error in image error callback:', callbackError);
    }
  }
};

export const validateImageData = (imageData: ImageData | null, imageId: string): boolean => {
  if (!imageData) {
    console.warn(`Image validation failed for ${imageId}: No image data`);
    return false;
  }

  if (!imageData.url) {
    console.warn(`Image validation failed for ${imageId}: Invalid URL`);
    return false;
  }

  if (!imageData.alt) {
    console.warn(`Image ${imageId} is missing alt text - accessibility concern`);
  }

  return true;
};

export const createFallbackImageData = (imageId: string): ImageData => {
  // SVGベースのプレースホルダーを直接データURLとして生成
  const svgContent = `<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f5f5f5" stroke="#ddd" stroke-width="2"/>
  <text x="50%" y="50%" text-anchor="middle" dy="0.3em" font-family="Arial, sans-serif" font-size="14" fill="#666">
    画像を読み込めませんでした
  </text>
  <text x="50%" y="70%" text-anchor="middle" dy="0.3em" font-family="Arial, sans-serif" font-size="12" fill="#999">
    ID: ${imageId}
  </text>
</svg>`;

  // URLエンコーディングを使用（Base64より安全で確実）
  const placeholderSvg = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgContent)}`;

  return {
    url: placeholderSvg,
    alt: `画像が読み込めませんでした (ID: ${imageId})`,
    width: 300,
    height: 200,
    position: { x: 0, y: 0 },
    size: { width: 300, height: 200 },
    zIndex: 1,
  };
}; 