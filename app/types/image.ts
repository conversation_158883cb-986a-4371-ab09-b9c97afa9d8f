export interface ImageData {
  url: string;
  alt: string;
  photographer?: string;
  description?: string;
  width?: number;
  height?: number;
  position?: {
    x: number;
    y: number;
  };
  size?: {
    width: number;
    height: number;
  };
  zIndex?: number;
}

export interface DraggableImageProps {
  imageId: string;
  imageData: ImageData | null;
  onImageChange?: (imageData: ImageData) => void;
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
  onError?: (error: Error) => void;
}

export interface ResizeHandle {
  cursor: string;
  position: 'nw' | 'n' | 'ne' | 'e' | 'se' | 's' | 'sw' | 'w';
}

export interface DragState {
  isDragging: boolean;
  offset: { x: number; y: number };
}

export interface ResizeState {
  isResizing: boolean;
  startData: {
    position: { x: number; y: number };
    size: { width: number; height: number };
    mousePos: { x: number; y: number };
    handle: string;
  } | null;
}

export interface TouchState {
  isTouching: boolean;
  startPosition: { x: number; y: number };
  currentPosition: { x: number; y: number };
} 