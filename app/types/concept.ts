/**
 * Shared type definitions for concept data
 * Single source of truth to avoid duplication
 */

export interface ConceptData {
  headline: string;
  subheadline: string;
  valueProposition: string;
  targetJustification: string;
  marketAnalysis: string;
  reasoning: string;
  confidenceScore: number;
  expectedConversionLift: string;
  keyDifferentiators: string[];
  actionableInsights: string[];
}

export interface LPContent {
  heroSection?: {
    title: string;
    subtitle: string;
    ctaText: string;
    backgroundImage?: string;
  };
  featuresSection?: {
    title: string;
    features: Array<{
      title: string;
      description: string;
      icon?: string;
    }>;
  };
  testimonialsSection?: {
    title: string;
    testimonials: Array<{
      name: string;
      role: string;
      content: string;
      avatar?: string;
    }>;
  };
  pricingSection?: {
    title: string;
    plans: Array<{
      name: string;
      price: string;
      features: string[];
      highlighted?: boolean;
    }>;
  };
  // 新統合型LP構造
  integratedLP?: any;
  type?: 'complete-landing-page' | 'individual-sections';
}