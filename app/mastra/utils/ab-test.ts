// app/mastra/utils/ab-test.ts
import { getFeatureFlags } from '../config/feature-flags';

/**
 * Simple hash function for consistent session assignment.
 * Uses a standard string hashing algorithm to ensure consistent
 * assignment of sessions to experiment groups.
 * @param str - The session ID to hash
 * @returns A positive integer hash value
 */
function hashCode(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash);
}

export const getExperimentGroup = (sessionId: string): 'control' | 'mastra' => {
  if (!getFeatureFlags().MASTRA_AB_TEST) return 'control';
  
  // Stable assignment based on session ID
  const hash = hashCode(sessionId);
  return (hash % 100) < 50 ? 'control' : 'mastra';
};

export const shouldUseMastra = (sessionId: string): boolean => {
  const flags = getFeatureFlags();
  if (!flags.ENABLE_MASTRA) return false;
  if (!flags.MASTRA_AB_TEST) return true;
  
  return getExperimentGroup(sessionId) === 'mastra';
};