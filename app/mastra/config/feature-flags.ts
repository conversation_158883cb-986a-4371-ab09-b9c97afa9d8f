// app/mastra/config/feature-flags.ts
interface FeatureFlags {
  ENABLE_MASTRA: boolean;
  MASTRA_AGENT_TOOLS: boolean;
  MASTRA_MEMORY_SYSTEM: boolean;
  MASTRA_WORKFLOW_ENGINE: boolean;
  MASTRA_AB_TEST: boolean;
}

const parseEnvBoolean = (value: string | undefined, defaultValue = false): boolean => {
  if (value === undefined) return defaultValue;
  return value.toLowerCase() === 'true';
};

export const DEFAULT_FEATURE_FLAGS: FeatureFlags = {
  ENABLE_MASTRA: false,
  MASTRA_AGENT_TOOLS: false,
  MASTRA_MEMORY_SYSTEM: false,
  MASTRA_WORKFLOW_ENGINE: false,
  MASTRA_AB_TEST: false
};

export const getFeatureFlags = (): FeatureFlags => ({
  ENABLE_MASTRA: parseEnvBoolean(process.env.ENABLE_MASTRA),
  MASTRA_AGENT_TOOLS: parseEnvBoolean(process.env.MASTRA_AGENT_TOOLS),
  MASTRA_MEMORY_SYSTEM: parseEnvBoolean(process.env.MASTRA_MEMORY_SYSTEM),
  MASTRA_WORKFLOW_ENGINE: parseEnvBoolean(process.env.MASTRA_WORKFLOW_ENGINE),
  MASTRA_AB_TEST: parseEnvBoolean(process.env.MASTRA_AB_TEST)
});

export type { FeatureFlags };