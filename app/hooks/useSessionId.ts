// app/hooks/useSessionId.ts
import { useState, useEffect } from 'react';

export const useSessionId = (): string => {
  const [sessionId, setSessionId] = useState<string>('');

  useEffect(() => {
    try {
      // Check if session ID exists in localStorage
      let existingSessionId = typeof window !== 'undefined' 
        ? localStorage.getItem('mastra-session-id') 
        : null;

      if (!existingSessionId) {
        // Generate new unique session ID
        existingSessionId = typeof crypto !== 'undefined' && crypto.randomUUID 
          ? `session-${crypto.randomUUID()}`
          : `session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
        
        if (typeof window !== 'undefined') {
          localStorage.setItem('mastra-session-id', existingSessionId);
        }
      }

      setSessionId(existingSessionId || '');
    } catch (error) {
      console.warn('Failed to access localStorage, using temporary session ID:', error);
      const tempSessionId = `temp-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      setSessionId(tempSessionId);
    }
  }, []);

  return sessionId;
};