import { useEffect } from 'react';
import { useEditMode } from '../contexts/EditModeContext';
import { announceToScreenReader } from '../utils/accessibility';

export function useGlobalKeyboardControls() {
  const { 
    isEditMode, 
    isTextEditMode, 
    toggleTextEditMode, 
    editingTextId,
    setEditingTextId 
  } = useEditMode();

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle shortcuts when not in a text input
      const activeElement = document.activeElement;
      const isInTextInput = activeElement && (
        activeElement.tagName === 'INPUT' ||
        activeElement.tagName === 'TEXTAREA' ||
        activeElement.getAttribute('contenteditable') === 'true'
      );

      if (isInTextInput) return;

      // Handle global shortcuts
      if (e.key === 't' && e.ctrlKey && isEditMode) {
        e.preventDefault();
        toggleTextEditMode();
        announceToScreenReader(
          `テキスト編集モード${!isTextEditMode ? '有効' : '無効'}にしました`
        );
      }

      // Tab navigation for text editing mode
      if (isEditMode && isTextEditMode && e.key === 'Tab') {
        e.preventDefault();
        
        // Find all editable text elements
        const editableElements = document.querySelectorAll('[data-editable="true"]');
        if (editableElements.length === 0) return;

        const currentIndex = Array.from(editableElements).findIndex(
          el => el.getAttribute('data-text-id') === editingTextId
        );

        let nextIndex;
        if (e.shiftKey) {
          // Previous element
          nextIndex = currentIndex <= 0 ? editableElements.length - 1 : currentIndex - 1;
        } else {
          // Next element
          nextIndex = currentIndex >= editableElements.length - 1 ? 0 : currentIndex + 1;
        }

        const nextElement = editableElements[nextIndex] as HTMLElement;
        const nextTextId = nextElement.getAttribute('data-text-id');
        
        if (nextTextId) {
          nextElement.click();
          announceToScreenReader(`フォーカスを移動しました`);
        }
      }

      // Escape to exit text editing mode
      if (e.key === 'Escape' && isTextEditMode && editingTextId) {
        e.preventDefault();
        setEditingTextId(null);
        announceToScreenReader('テキスト編集をキャンセルしました');
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [
    isEditMode,
    isTextEditMode,
    toggleTextEditMode,
    editingTextId,
    setEditingTextId
  ]);
}