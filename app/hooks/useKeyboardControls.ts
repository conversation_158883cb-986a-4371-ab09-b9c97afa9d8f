import { useEffect, useCallback } from 'react';
import { ImageData } from '../types/image';
import { DRAGGABLE_CONSTANTS } from '../constants/draggable';
import { announceToScreenReader } from '../utils/accessibility';

interface UseKeyboardControlsProps {
  imageId: string;
  currentImageData: ImageData | null;
  isSelected: boolean;
  isEditMode: boolean;
  onImageChange?: (imageData: ImageData) => void;
  updateImage: (imageId: string, imageData: ImageData) => void;
  setSelectedImageId: (id: string | null) => void;
}

const KEYBOARD_MOVE_STEP = 10;
const KEYBOARD_RESIZE_STEP = 10;

export function useKeyboardControls({
  imageId,
  currentImageData,
  isSelected,
  isEditMode,
  onImageChange,
  updateImage,
  setSelectedImageId,
}: UseKeyboardControlsProps) {
  
  const moveImage = useCallback((deltaX: number, deltaY: number) => {
    if (!currentImageData) return;

    const currentPosition = currentImageData.position || { x: 0, y: 0 };
    const size = currentImageData.size || DRAGGABLE_CONSTANTS.DEFAULT_SIZE;
    
    // 境界制限
    const maxX = window.innerWidth - size.width;
    const maxY = window.innerHeight - size.height;
    
    const newX = Math.max(0, Math.min(currentPosition.x + deltaX, maxX));
    const newY = Math.max(0, Math.min(currentPosition.y + deltaY, maxY));

    const updatedImageData = {
      ...currentImageData,
      position: { x: newX, y: newY },
    };

    updateImage(imageId, updatedImageData);
    onImageChange?.(updatedImageData);
    
    announceToScreenReader(`画像を移動しました: X ${newX}, Y ${newY}`);
  }, [currentImageData, imageId, updateImage, onImageChange]);

  const resizeImage = useCallback((deltaWidth: number, deltaHeight: number) => {
    if (!currentImageData) return;

    const currentSize = currentImageData.size || DRAGGABLE_CONSTANTS.DEFAULT_SIZE;
    
    const newWidth = Math.max(DRAGGABLE_CONSTANTS.MIN_SIZE, currentSize.width + deltaWidth);
    const newHeight = Math.max(DRAGGABLE_CONSTANTS.MIN_SIZE, currentSize.height + deltaHeight);

    const updatedImageData = {
      ...currentImageData,
      size: { width: newWidth, height: newHeight },
    };

    updateImage(imageId, updatedImageData);
    onImageChange?.(updatedImageData);
    
    announceToScreenReader(`画像をリサイズしました: ${newWidth}x${newHeight}`);
  }, [currentImageData, imageId, updateImage, onImageChange]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isSelected || !isEditMode || !currentImageData) return;

      // フォーカスが入力フィールドにある場合は操作しない
      const activeElement = document.activeElement;
      if (activeElement && (
        activeElement.tagName === 'INPUT' ||
        activeElement.tagName === 'TEXTAREA' ||
        activeElement.getAttribute('contenteditable') === 'true'
      )) {
        return;
      }

      switch (e.key) {
        case 'ArrowUp':
          e.preventDefault();
          if (e.shiftKey) {
            resizeImage(0, -KEYBOARD_RESIZE_STEP);
          } else {
            moveImage(0, -KEYBOARD_MOVE_STEP);
          }
          break;
          
        case 'ArrowDown':
          e.preventDefault();
          if (e.shiftKey) {
            resizeImage(0, KEYBOARD_RESIZE_STEP);
          } else {
            moveImage(0, KEYBOARD_MOVE_STEP);
          }
          break;
          
        case 'ArrowLeft':
          e.preventDefault();
          if (e.shiftKey) {
            resizeImage(-KEYBOARD_RESIZE_STEP, 0);
          } else {
            moveImage(-KEYBOARD_MOVE_STEP, 0);
          }
          break;
          
        case 'ArrowRight':
          e.preventDefault();
          if (e.shiftKey) {
            resizeImage(KEYBOARD_RESIZE_STEP, 0);
          } else {
            moveImage(KEYBOARD_MOVE_STEP, 0);
          }
          break;
          
        case 'Escape':
          e.preventDefault();
          setSelectedImageId(null);
          announceToScreenReader('画像の選択を解除しました');
          break;
          
        case 'Enter':
        case ' ':
          e.preventDefault();
          // フォーカス状態の切り替え
          announceToScreenReader(`画像 ${imageId} を選択しました`);
          break;
      }
    };

    if (isSelected && isEditMode) {
      document.addEventListener('keydown', handleKeyDown);
      
      return () => {
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [isSelected, isEditMode, currentImageData, imageId, moveImage, resizeImage, setSelectedImageId]);

  const handleFocus = useCallback(() => {
    if (isEditMode) {
      announceToScreenReader(`画像 ${imageId} にフォーカスしました。矢印キーで移動、Shift+矢印キーでリサイズできます`);
    }
  }, [imageId, isEditMode]);

  const handleClick = useCallback(() => {
    if (isEditMode) {
      setSelectedImageId(imageId);
      announceToScreenReader(`画像 ${imageId} を選択しました`);
    }
  }, [imageId, isEditMode, setSelectedImageId]);

  return {
    handleFocus,
    handleClick,
  };
} 