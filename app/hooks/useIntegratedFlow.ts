import { useState, useCallback, useEffect } from 'react';
import { ConceptData, LPContent } from '../types/concept';
import { sanitizeText } from '../utils/sanitize';

type FlowStep = 
  | 'concept-input'
  | 'analyzing' 
  | 'concept-result'
  | 'lp-generation'
  | 'lp-completed'
  | 'edit-mode';

interface FlowState {
  currentStep: FlowStep;
  completedSteps: FlowStep[];
  progressPercentage: number;
  startTime?: Date;
  analysisTime?: number;
  lpGenerationTime?: number;
}

export function useIntegratedFlow() {
  const [currentStep, setCurrentStep] = useState<FlowStep>('concept-input');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [concept, setConcept] = useState<ConceptData | null>(null);
  const [lpContent, setLpContent] = useState<LPContent | null>(null);
  const [formData, setFormData] = useState<Record<string, string>>({});
  
  const [flowState, setFlowState] = useState<FlowState>({
    currentStep: 'concept-input',
    completedSteps: [],
    progressPercentage: 0
  });

  // フロー状態の更新
  useEffect(() => {
    const stepProgressMap: Record<FlowStep, number> = {
      'concept-input': 0,
      'analyzing': 20,
      'concept-result': 40,
      'lp-generation': 60,
      'lp-completed': 80,
      'edit-mode': 100
    };

    const stepOrder: FlowStep[] = [
      'concept-input', 
      'analyzing', 
      'concept-result', 
      'lp-generation', 
      'lp-completed', 
      'edit-mode'
    ];

    const currentIndex = stepOrder.indexOf(currentStep);
    const completedSteps = stepOrder.slice(0, currentIndex);

    setFlowState(prev => ({
      ...prev,
      currentStep,
      completedSteps,
      progressPercentage: stepProgressMap[currentStep]
    }));
  }, [currentStep]);

  // AI分析リクエスト処理
  const handleAnalysisRequest = useCallback(async (data: Record<string, string>) => {
    setFormData(data);
    setIsAnalyzing(true);
    setCurrentStep('analyzing');
    
    const startTime = Date.now();
    setFlowState(prev => ({ ...prev, startTime: new Date() }));

    // AbortController for timeout and cancellation
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 35000); // 35 second timeout

    try {
      // Claude APIへのリクエスト
      const response = await fetch('/api/chat-claude', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
        body: JSON.stringify({
          messages: [
            {
              role: 'user',
              content: `以下の情報に基づいてLPコンセプトを提案してください：
              
商品・サービス概要: ${data.productOverview}
ターゲット層: ${data.targetAudience}
業界・分野: ${data.industry || '指定なし'}
予算規模: ${data.budget || '指定なし'}
主要競合他社: ${data.competitors || '指定なし'}

以下のJSON形式で回答してください：
{
  "headline": "メインヘッドライン",
  "subheadline": "サブヘッドライン", 
  "valueProposition": "価値提案",
  "targetJustification": "ターゲット分析",
  "marketAnalysis": "市場分析",
  "reasoning": "AI推論",
  "confidenceScore": 85,
  "expectedConversionLift": "20-25%",
  "keyDifferentiators": ["差別化ポイント1", "差別化ポイント2", "差別化ポイント3"],
  "actionableInsights": ["アクション1", "アクション2", "アクション3"]
}`
            }
          ]
        }),
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`AI分析に失敗しました: ${response.status} ${response.statusText}`);
      }

      // ストリームレスポンスの処理
      if (!response.body) {
   throw new Error('Empty response body from AI API');
 }
 const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let fullResponse = '';

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          const chunk = decoder.decode(value);
          fullResponse += chunk;
        }
      }

      // JSONレスポンスの抽出とパース（より安全な実装）
      const jsonMatch = fullResponse.trim().match(/({[\s\S]*})\s*$/);
      if (jsonMatch) {
        const conceptData = JSON.parse(jsonMatch[1]);
        setConcept(conceptData);
        setCurrentStep('concept-result');
        
        const analysisTime = Date.now() - startTime;
        setFlowState(prev => ({ ...prev, analysisTime }));
      } else {
        throw new Error('AI分析結果の解析に失敗しました');
      }
    } catch (error) {
      console.error('AI分析エラー:', error);
      clearTimeout(timeoutId);
      
      // AbortError handling
      if (error instanceof DOMException && error.name === 'AbortError') {
        throw new Error('AIリクエストがタイムアウトしました。もう一度お試しください。');
      }
      
      // エラー処理：分析状態をリセット
      setCurrentStep('concept-input');
      throw error;
    } finally {
      setIsAnalyzing(false);
    }
  }, []);

  // コンセプト選択処理
  const handleConceptSelect = useCallback(async (conceptData: ConceptData) => {
    setConcept(conceptData);
    setCurrentStep('lp-generation');
    
    const startTime = Date.now();

    try {
      // LP生成処理
      await handleLPGeneration(conceptData);
      
      const lpGenerationTime = Date.now() - startTime;
      setFlowState(prev => ({ ...prev, lpGenerationTime }));
      
      setCurrentStep('lp-completed');
    } catch (error) {
      console.error('LP生成エラー:', error);
      throw error;
    }
  }, []);

  // LP生成処理（新統合版）
  const handleLPGeneration = useCallback(async (conceptData: ConceptData) => {
    try {
      // 新しい統合ツール generateCompleteLandingPage を使用
      const response = await fetch('/api/chat-claude', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages: [{
            role: 'user',
            content: `以下のコンセプトデータに基づいて、完全なランディングページを生成してください：

商品・サービス概要: ${formData.productOverview || 'IT・デジタルサービス'}
ターゲット層: ${formData.targetAudience || '30-40代ビジネスパーソン'}
ヘッドライン: ${conceptData.headline}
価値提案: ${conceptData.valueProposition}
差別化ポイント: ${conceptData.keyDifferentiators.join(', ')}

generateCompleteLandingPage ツールを使用して、V0やClaude Artifactsのような統合された高品質なLPを生成してください。`
          }]
        })
      });

      if (!response.ok) {
        throw new Error(`統合LP生成に失敗しました: ${response.status} ${response.statusText}`);
      }

      // ストリーミングレスポンスの処理
      if (!response.body) {
        throw new Error('Empty response body from AI API');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let fullResponse = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        fullResponse += chunk;
      }

      // レスポンスからLP構造データを抽出
      const lpData = parseLPGenerationResponse(fullResponse);
      setLpContent(lpData);
      
      // 統合LP構造をDOMに反映
      await renderIntegratedLPContent(lpData);
      
      return lpData;
    } catch (error) {
      console.error('統合LP生成処理エラー:', error);
      throw error;
    }
  }, [formData]);

  // ヒーローセクション生成
  const generateHeroSection = async (concept: ConceptData) => {
    const response = await fetch('/api/chat-claude', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        messages: [{
          role: 'user',
          content: `以下のコンセプトに基づいてヒーローセクションを生成してください：
          
ヘッドライン: ${concept.headline}
サブヘッドライン: ${concept.subheadline}
価値提案: ${concept.valueProposition}

createHeroSectionツールを使用してください。`
        }]
      })
    });

    if (!response.ok) {
      throw new Error(`Hero section generation failed: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  };

  // 特徴セクション生成
  const generateFeaturesSection = async (concept: ConceptData) => {
    const response = await fetch('/api/chat-claude', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        messages: [{
          role: 'user',
          content: `差別化ポイントに基づいて特徴セクションを生成してください：
          
差別化ポイント: ${concept.keyDifferentiators.join(', ')}
価値提案: ${concept.valueProposition}

createFeaturesSectionツールを使用してください。`
        }]
      })
    });

    if (!response.ok) {
      throw new Error(`Features section generation failed: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  };

  // お客様の声セクション生成
  const generateTestimonialsSection = async (concept: ConceptData) => {
    const response = await fetch('/api/chat-claude', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        messages: [{
          role: 'user',
          content: `ターゲット層に合わせたお客様の声を生成してください：
          
ターゲット分析: ${concept.targetJustification}
期待効果: ${concept.expectedConversionLift}

createTestimonialsSectionツールを使用してください。`
        }]
      })
    });

    if (!response.ok) {
      throw new Error(`Testimonials section generation failed: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  };

  // 価格表セクション生成
  const generatePricingSection = async (concept: ConceptData) => {
    const response = await fetch('/api/chat-claude', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        messages: [{
          role: 'user',
          content: `実装アクションに基づいて価格表セクションを生成してください：
          
実装アクション: ${concept.actionableInsights.join(', ')}
価値提案: ${concept.valueProposition}

createPricingSectionツールを使用してください。`
        }]
      })
    });

    if (!response.ok) {
      throw new Error(`Pricing section generation failed: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  };

  // LP生成レスポンス解析（統合版）
  const parseLPGenerationResponse = (fullResponse: string): LPContent => {
    try {
      // AI レスポンスから完全なLP構造を抽出
      const jsonMatch = fullResponse.match(/("landingPage":\s*{[\s\S]*?})\s*(?:,|}|\])/);
      if (jsonMatch) {
        const lpStructure = JSON.parse(`{${jsonMatch[1]}}`);
        return {
          integratedLP: lpStructure.landingPage,
          type: 'complete-landing-page'
        };
      }
      
      // フォールバック：従来形式の個別セクション
      return {
        heroSection: { 
          title: 'ヒーローセクション', 
          subtitle: '統合LP生成中...', 
          ctaText: 'お試しください' 
        },
        featuresSection: { 
          title: '特徴セクション', 
          features: [{ title: '生成中', description: '統合LP生成中...' }] 
        },
        testimonialsSection: { 
          title: 'お客様の声', 
          testimonials: [{ name: '生成中', role: 'ユーザー', content: '統合LP生成中...' }] 
        },
        pricingSection: { 
          title: '価格表', 
          plans: [{ name: '生成中', price: '---', features: ['統合LP生成中...'] }] 
        }
      };
    } catch (error) {
      console.error('LP構造解析エラー:', error);
      return {
        heroSection: { 
          title: 'LP生成完了', 
          subtitle: '統合されたLPが生成されました', 
          ctaText: '確認する' 
        }
      };
    }
  };

  // 統合LP構造をDOMに反映（新版）
  const renderIntegratedLPContent = async (lpData: LPContent) => {
    try {
      // メインコンテナを取得または作成
      let container = document.getElementById('lp-content-container');
      if (!container) {
        container = document.createElement('div');
        container.id = 'lp-content-container';
        container.style.cssText = `
          max-width: 1200px;
          margin: 40px auto;
          padding: 0 20px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;
        
        // IntegratedFlowManagerの後に挿入
        const flowManager = document.querySelector('[data-component="IntegratedFlowManager"]');
        if (flowManager?.parentNode) {
          flowManager.parentNode.insertBefore(container, flowManager.nextSibling);
        } else {
          document.body.appendChild(container);
        }
      }

      container.innerHTML = ''; // 既存コンテンツをクリア

      // 統合LP構造の場合
      if (lpData.integratedLP && lpData.type === 'complete-landing-page') {
        const lp = lpData.integratedLP;
        
        // ヒーローセクション
        if (lp.hero) {
          const heroElement = createSectionElement(lp.hero, 'hero');
          container.appendChild(heroElement);
        }
        
        // 問題提起セクション
        if (lp.problem) {
          const problemElement = createSectionElement(lp.problem, 'problem');
          container.appendChild(problemElement);
        }
        
        // ソリューションセクション
        if (lp.solution) {
          const solutionElement = createSectionElement(lp.solution, 'solution');
          container.appendChild(solutionElement);
        }
        
        // 特徴セクション
        if (lp.features) {
          const featuresElement = createSectionElement(lp.features, 'features');
          container.appendChild(featuresElement);
        }
        
        // 社会的証明セクション
        if (lp.socialProof) {
          const socialProofElement = createSectionElement(lp.socialProof, 'testimonials');
          container.appendChild(socialProofElement);
        }
        
        // 価格表セクション
        if (lp.pricing) {
          const pricingElement = createSectionElement(lp.pricing, 'pricing');
          container.appendChild(pricingElement);
        }
        
        // 最終CTAセクション
        if (lp.finalCta) {
          const ctaElement = createSectionElement(lp.finalCta, 'cta');
          container.appendChild(ctaElement);
        }
      } else {
        // フォールバック：従来のセクション表示
        await renderLegacyLPContent(lpData);
      }

    } catch (error) {
      console.error('統合LP content rendering error:', error);
    }
  };

  // 従来型LPコンテンツのレンダリング（フォールバック）
  const renderLegacyLPContent = async (lpData: LPContent) => {
    const container = document.getElementById('lp-content-container');
    if (!container) return;

    if (lpData.heroSection) {
      const heroElement = createSectionElement(lpData.heroSection, 'hero');
      container.appendChild(heroElement);
    }
    
    if (lpData.featuresSection) {
      const featuresElement = createSectionElement(lpData.featuresSection, 'features');
      container.appendChild(featuresElement);
    }
    
    if (lpData.testimonialsSection) {
      const testimonialsElement = createSectionElement(lpData.testimonialsSection, 'testimonials');
      container.appendChild(testimonialsElement);
    }
    
    if (lpData.pricingSection) {
      const pricingElement = createSectionElement(lpData.pricingSection, 'pricing');
      container.appendChild(pricingElement);
    }
  };

  // セクション要素の作成
  const createSectionElement = (sectionData: any, sectionType: string): HTMLElement => {
    const section = document.createElement('section');
    section.className = `lp-section lp-${sectionType}`;
    section.setAttribute('data-section-type', sectionType);
    
    // セクションの基本スタイル
    section.style.cssText = `
      margin: 40px 0;
      padding: 60px 40px;
      border-radius: 12px;
      background: white;
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    `;

    // セクションタイプに応じたコンテンツ生成
    section.innerHTML = generateSectionHTML(sectionData, sectionType);
    
    return section;
  };

  // セクションHTMLの生成（安全な実装）
  const generateSectionHTML = (data: any, type: string): string => {
    const safeTitle = sanitizeText(data.title || 'セクションタイトル');
    const safeContent = sanitizeText(data.content || 'セクションコンテンツ');

    return `
      <div class="section-content">
        <h2 data-editable="true" data-testid="${type}-title">${safeTitle}</h2>
        <p data-editable="true" data-testid="${type}-content">${safeContent}</p>
      </div>
    `;
  };

  // 編集モードへの切り替え
  const enableEditMode = useCallback(() => {
    setCurrentStep('edit-mode');
    
    // カスタムイベントを発行して編集モードの有効化を通知
    document.dispatchEvent(new CustomEvent('flowEditModeEnabled'));
  }, []);

  // AbortController参照（リセット時のクリーンアップ用）
  const [controller, setController] = useState<AbortController | null>(null);

  // フロー全体のリセット
  const resetFlow = useCallback(() => {
    // 進行中のリクエストをキャンセル
    if (controller) {
      controller.abort();
      setController(null);
    }

    setCurrentStep('concept-input');
    setIsAnalyzing(false);
    setConcept(null);
    setLpContent(null);
    setFormData({});
    setFlowState({
      currentStep: 'concept-input',
      completedSteps: [],
      progressPercentage: 0
    });

    // 生成されたLPコンテンツを削除
    const container = document.getElementById('lp-content-container');
    if (container) {
      container.remove();
    }
  }, [controller]);

  // 編集モード有効化イベントのリスナー
  useEffect(() => {
    const handleEnableEditMode = () => {
      enableEditMode();
    };

    document.addEventListener('enableEditMode', handleEnableEditMode);
    
    return () => {
      document.removeEventListener('enableEditMode', handleEnableEditMode);
      // コンポーネントアンマウント時のクリーンアップ
      if (controller) {
        controller.abort();
      }
    };
  }, [enableEditMode, controller]);

  return {
    // 状態
    currentStep,
    isAnalyzing,
    concept,
    lpContent,
    flowState,
    
    // アクション
    setCurrentStep,
    handleAnalysisRequest,
    handleConceptSelect,
    handleLPGeneration,
    resetFlow,
    enableEditMode,
    
    // ユーティリティ
    isFlowComplete: currentStep === 'edit-mode',
    canProceedToNextStep: !isAnalyzing && currentStep !== 'analyzing',
    
    // パフォーマンス情報
    getAnalysisTime: () => flowState.analysisTime,
    getLPGenerationTime: () => flowState.lpGenerationTime,
    getTotalFlowTime: () => {
      if (!flowState.startTime) return null;
      return Date.now() - flowState.startTime.getTime();
    }
  };
}