// app/hooks/useMastraFlags.ts
import { useState, useEffect } from 'react';
import { FeatureFlags } from '../mastra/config/feature-flags';
import { shouldUseMastra, getExperimentGroup } from '../mastra/utils/ab-test';
import { useSessionId } from './useSessionId';

interface MastraFlagsState {
  flags: FeatureFlags | null;
  loading: boolean;
  error: string | null;
  shouldUseMastra: boolean;
  experimentGroup: 'control' | 'mastra';
}

export const useMastraFlags = (): MastraFlagsState => {
  const [flags, setFlags] = useState<FeatureFlags | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const sessionId = useSessionId();

  useEffect(() => {
    if (!sessionId) return; // sessionIdが準備できるまで待つ

    const fetchFlags = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch('/api/feature-flags');
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const flagsData = await response.json();
        setFlags(flagsData);
      } catch (err) {
        console.error('Failed to fetch feature flags:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        
        // エラー時はデフォルト値を設定
        setFlags({
          ENABLE_MASTRA: false,
          MASTRA_AGENT_TOOLS: false,
          MASTRA_MEMORY_SYSTEM: false,
          MASTRA_WORKFLOW_ENGINE: false,
          MASTRA_AB_TEST: false
        });
      } finally {
        setLoading(false);
      }
    };

    fetchFlags();
  }, [sessionId]);

  return {
    flags,
    loading,
    error,
    shouldUseMastra: sessionId ? shouldUseMastra(sessionId) : false,
    experimentGroup: sessionId ? getExperimentGroup(sessionId) : 'control'
  };
};