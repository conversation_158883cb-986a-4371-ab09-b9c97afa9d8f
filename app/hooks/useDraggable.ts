import { useState, useEffect, useCallback, useRef } from 'react';
import { ImageData, DragState, TouchState } from '../types/image';
import { DRAGGABLE_CONSTANTS, TOUCH } from '../constants/draggable';

interface UseDraggableProps {
  imageId: string;
  currentImageData: ImageData | null;
  isEditMode: boolean;
  onImageChange?: (imageData: ImageData) => void;
  updateImage: (imageId: string, imageData: ImageData) => void;
  setSelectedImageId: (id: string | null) => void;
  setIsDragging: (dragging: boolean) => void;
  setDraggedImageId: (id: string | null) => void;
}

export function useDraggable({
  imageId,
  currentImageData,
  isEditMode,
  onImageChange,
  updateImage,
  setSelectedImageId,
  setIsDragging,
  setDraggedImageId,
}: UseDraggableProps) {
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    offset: { x: 0, y: 0 },
  });
  
  const [touchState, setTouchState] = useState<TouchState>({
    isTouching: false,
    startPosition: { x: 0, y: 0 },
    currentPosition: { x: 0, y: 0 },
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const rafRef = useRef<number | null>(null);
  const touchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 境界制限の計算
  const constrainPosition = useCallback((x: number, y: number, size: { width: number; height: number }) => {
    const container = containerRef.current?.parentElement || document.body;
    const containerRect = container.getBoundingClientRect();
    
    const maxX = containerRect.width - size.width;
    const maxY = containerRect.height - size.height;
    
    return {
      x: Math.max(0, Math.min(x, maxX)),
      y: Math.max(0, Math.min(y, maxY)),
    };
  }, []);

  // デバウンス付きの位置更新
  const updatePosition = useCallback((newPosition: { x: number; y: number }) => {
    if (!currentImageData) return;

    if (rafRef.current) {
      cancelAnimationFrame(rafRef.current);
    }

    rafRef.current = requestAnimationFrame(() => {
      const updatedImageData = {
        ...currentImageData,
        position: newPosition,
      };
      
      updateImage(imageId, updatedImageData);
      onImageChange?.(updatedImageData);
    });
  }, [currentImageData, imageId, updateImage, onImageChange]);

  // マウスドラッグ開始
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!isEditMode || !currentImageData || !containerRef.current) return;
    
    e.preventDefault();
    e.stopPropagation();

    const rect = containerRef.current.getBoundingClientRect();
    const offset = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };

    setDragState({ isDragging: true, offset });
    setSelectedImageId(imageId);
    setIsDragging(true);
    setDraggedImageId(imageId);
  }, [isEditMode, currentImageData, imageId, setSelectedImageId, setIsDragging, setDraggedImageId]);

  // タッチドラッグ開始
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (!isEditMode || !currentImageData || !containerRef.current) return;
    
    e.preventDefault();
    
    const touch = e.touches[0];
    const rect = containerRef.current.getBoundingClientRect();
    const startPosition = {
      x: touch.clientX,
      y: touch.clientY,
    };

    const offset = {
      x: touch.clientX - rect.left,
      y: touch.clientY - rect.top,
    };

    setTouchState({
      isTouching: true,
      startPosition,
      currentPosition: startPosition,
    });

    // 長押し判定のタイマー
    touchTimeoutRef.current = setTimeout(() => {
      setDragState({ isDragging: true, offset });
      setSelectedImageId(imageId);
      setIsDragging(true);
      setDraggedImageId(imageId);
    }, TOUCH.LONG_PRESS_DURATION);
  }, [isEditMode, currentImageData, imageId, setSelectedImageId, setIsDragging, setDraggedImageId]);

  // マウス移動処理
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!dragState.isDragging || !currentImageData) return;

      const container = document.body;
      const containerRect = container.getBoundingClientRect();
      
      const newX = e.clientX - containerRect.left - dragState.offset.x;
      const newY = e.clientY - containerRect.top - dragState.offset.y;

      const size = currentImageData.size || DRAGGABLE_CONSTANTS.DEFAULT_SIZE;
      const constrainedPosition = constrainPosition(newX, newY, size);
      
      updatePosition(constrainedPosition);
    };

    const handleMouseUp = () => {
      if (dragState.isDragging) {
        setDragState({ isDragging: false, offset: { x: 0, y: 0 } });
        setIsDragging(false);
        setDraggedImageId(null);
      }
    };

    if (dragState.isDragging) {
      document.addEventListener('mousemove', handleMouseMove, { passive: false });
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [dragState.isDragging, dragState.offset, currentImageData, constrainPosition, updatePosition, setIsDragging, setDraggedImageId]);

  // タッチ移動処理
  useEffect(() => {
    const handleTouchMove = (e: TouchEvent) => {
      if (!touchState.isTouching) return;

      const touch = e.touches[0];
      const currentPosition = { x: touch.clientX, y: touch.clientY };
      
      setTouchState(prev => ({ ...prev, currentPosition }));

      // 最小移動距離をチェック
      const distance = Math.sqrt(
        Math.pow(currentPosition.x - touchState.startPosition.x, 2) +
        Math.pow(currentPosition.y - touchState.startPosition.y, 2)
      );

      if (distance > TOUCH.MIN_MOVE_DISTANCE && touchTimeoutRef.current) {
        clearTimeout(touchTimeoutRef.current);
        touchTimeoutRef.current = null;
      }

      if (dragState.isDragging && currentImageData) {
        e.preventDefault();
        
        const container = document.body;
        const containerRect = container.getBoundingClientRect();
        
        const newX = touch.clientX - containerRect.left - dragState.offset.x;
        const newY = touch.clientY - containerRect.top - dragState.offset.y;

        const size = currentImageData.size || DRAGGABLE_CONSTANTS.DEFAULT_SIZE;
        const constrainedPosition = constrainPosition(newX, newY, size);
        
        updatePosition(constrainedPosition);
      }
    };

    const handleTouchEnd = () => {
      if (touchTimeoutRef.current) {
        clearTimeout(touchTimeoutRef.current);
        touchTimeoutRef.current = null;
      }

      if (dragState.isDragging) {
        setDragState({ isDragging: false, offset: { x: 0, y: 0 } });
        setIsDragging(false);
        setDraggedImageId(null);
      }

      setTouchState({
        isTouching: false,
        startPosition: { x: 0, y: 0 },
        currentPosition: { x: 0, y: 0 },
      });
    };

    if (touchState.isTouching) {
      document.addEventListener('touchmove', handleTouchMove, { passive: false });
      document.addEventListener('touchend', handleTouchEnd);
      
      return () => {
        document.removeEventListener('touchmove', handleTouchMove);
        document.removeEventListener('touchend', handleTouchEnd);
      };
    }
  }, [touchState.isTouching, touchState.startPosition, dragState.isDragging, dragState.offset, currentImageData, constrainPosition, updatePosition, setIsDragging, setDraggedImageId]);

  // クリーンアップ
  useEffect(() => {
    return () => {
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
      if (touchTimeoutRef.current) {
        clearTimeout(touchTimeoutRef.current);
      }
    };
  }, []);

  return {
    dragState,
    touchState,
    containerRef,
    handleMouseDown,
    handleTouchStart,
  };
} 