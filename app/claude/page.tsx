'use client';
import ChatPanelClaude from '../components/ChatPanelClaude';
import { UnifiedLPViewer } from '../components/UnifiedLPViewer';
import { EditModeProvider, useEditMode } from '../contexts/EditModeContext';
import { useState, useEffect } from 'react';
import type { Message, ToolInvocation } from 'ai';
import Link from 'next/link';

function ClaudePageContent() {
  const [messages, setMessages] = useState<Message[]>([]);
  const { 
    isEditMode, 
    toggleEditMode,
    isTextEditMode,
    toggleTextEditMode
  } = useEditMode();

  const handleMessagesUpdate = (newMessages: Message[]) => {
    setMessages(newMessages);
  };

  // キーボードショートカットの処理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.key === 't') {
        event.preventDefault();
        if (isEditMode) {
          toggleTextEditMode();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isEditMode, toggleTextEditMode]);

  const renderToolResult = (inv: ToolInvocation) => {
    if (inv.state !== 'result') {
      return (
        <div key={inv.toolCallId} style={{ 
          padding: '12px', 
          backgroundColor: '#f3e8ff', 
          borderRadius: '8px',
          margin: '8px 0',
          fontSize: '14px',
          color: '#7c3aed'
        }}>
          🔄 Claude が {inv.toolName} を実行中...
        </div>
      );
    }

    switch (inv.toolName) {
      case 'generateUnifiedLP':
        return (
          <UnifiedLPViewer 
            key={inv.toolCallId} 
            {...inv.result}
            isEditMode={isEditMode}
            isTextEditMode={isTextEditMode}
          />
        );
      default:
        return (
          <div key={inv.toolCallId} style={{ 
            padding: '12px', 
            backgroundColor: '#f0f0f0', 
            borderRadius: '8px',
            margin: '8px 0',
            color: '#333'
          }}>
            <pre style={{ color: '#333', fontSize: '12px' }}>
              {JSON.stringify(inv.result, null, 2)}
            </pre>
          </div>
        );
    }
  };

  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column',
      height: '100vh', 
      backgroundColor: '#f8f9fa',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      {/* ナビゲーションバー */}
      <div style={{
        padding: '12px 16px',
        backgroundColor: 'white',
        borderBottom: '1px solid #e0e0e0',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <h1 style={{ 
          margin: 0, 
          fontSize: '18px', 
          fontWeight: '600',
          color: '#333'
        }}>
          🚀 LP Creator
        </h1>
        <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
          {/* 編集モード切り替えボタン */}
          {messages.length > 0 && (
            <>
              <button
                onClick={toggleEditMode}
                style={{
                  padding: '6px 12px',
                  backgroundColor: isEditMode ? '#ff6b6b' : '#4caf50',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'background-color 0.2s'
                }}
              >
                {isEditMode ? '🔒 編集終了' : '✏️ 編集モード'}
              </button>
              
              {/* テキスト編集モード切り替えボタン */}
              {isEditMode && (
                <button
                  onClick={toggleTextEditMode}
                  style={{
                    padding: '6px 12px',
                    backgroundColor: isTextEditMode ? '#ffa726' : '#757575',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    fontSize: '14px',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'background-color 0.2s'
                  }}
                  title="Ctrl+T でも切り替え可能"
                >
                  {isTextEditMode ? '📝 テキスト編集中' : '📝 テキスト編集'}
                </button>
              )}
            </>
          )}
          
          <Link 
            href="/"
            style={{
              padding: '6px 12px',
              backgroundColor: '#2196f3',
              color: 'white',
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              textDecoration: 'none',
              transition: 'background-color 0.2s'
            }}
          >
            🤖 OpenAI版に戻る
          </Link>
          <div style={{
            padding: '6px 12px',
            backgroundColor: '#7c3aed',
            color: 'white',
            borderRadius: '6px',
            fontSize: '14px',
            fontWeight: '500'
          }}>
            Claude 3.5 Sonnet
          </div>
        </div>
      </div>

      {/* メインコンテンツ */}
      <div style={{ 
        display: 'flex', 
        flex: 1,
        minHeight: 0
      }}>
        {/* 左パネル：Claude チャット */}
        <div style={{ 
          flex: 1, 
          padding: 16, 
          backgroundColor: 'white',
          borderRight: '1px solid #e0e0e0',
          display: 'flex',
          flexDirection: 'column'
        }}>
          <h2 style={{ 
            margin: '0 0 16px 0', 
            fontSize: '20px', 
            fontWeight: '600',
            color: '#333',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            🤖 Claude LP生成チャット
            <span style={{ 
              fontSize: '12px', 
              backgroundColor: '#7c3aed', 
              color: 'white', 
              padding: '2px 8px', 
              borderRadius: '12px' 
            }}>
              LP生成
            </span>
          </h2>
          <ChatPanelClaude onMessagesUpdate={handleMessagesUpdate} />
        </div>
        
        {/* 右パネル：Claude LPプレビュー */}
        <div style={{ 
          flex: 2, 
          padding: 16,
          backgroundColor: '#f8f9fa',
          overflowY: 'auto'
        }}>
          <h2 style={{ 
            margin: '0 0 16px 0', 
            fontSize: '20px', 
            fontWeight: '600',
            color: '#333'
          }}>
            🎨 Claude ランディングページプレビュー
          </h2>
          
          {messages.length === 0 ? (
            <div style={{ 
              textAlign: 'center', 
              padding: '40px 20px',
              color: '#333',
              fontSize: '16px'
            }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>🤖</div>
              <p style={{ color: '#333', margin: '0 0 20px 0' }}>
                <strong>🔥 V0/Claude Artifacts風の統合LP生成機能</strong><br/>
                Claude と自然言語で完全なLPを一気に生成！
              </p>
              <div style={{ 
                marginTop: '20px', 
                fontSize: '14px',
                backgroundColor: 'white',
                padding: '16px',
                borderRadius: '8px',
                textAlign: 'left',
                color: '#333'
              }}>
                <strong style={{ color: '#333' }}>🚀 Claude 統合LP生成の使用例</strong>
                <ul style={{ 
                  margin: '8px 0', 
                  paddingLeft: '20px',
                  color: '#333'
                }}>
                  <li>「フィットネスアプリの完全なLPを作って」</li>
                  <li>「カフェのコーポレートスタイルでLPを生成」</li>
                  <li>「コンサルティング会社のダークテーマLPを作成」</li>
                  <li>「オンライン教育サービスの緑色系LPを作成」</li>
                  <li>「ミニマルなデザインで不動産LPを作成」</li>
                </ul>
                <div style={{ 
                  marginTop: '12px', 
                  padding: '8px', 
                  backgroundColor: '#f3e8ff', 
                  borderRadius: '4px',
                  fontSize: '12px'
                }}>
                  🤖 <strong>Claude活用ポイント:</strong> より詳細な要求や複雑なコンセプトも理解し、最適なLPを生成します
                </div>
              </div>
            </div>
          ) : (
            <div>
              {messages.map((msg: Message) => (
                <div key={msg.id}>
                  {msg.toolInvocations?.map(renderToolResult)}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>


    </div>
  );
}

export default function ClaudePage() {
  return (
    <EditModeProvider>
      <ClaudePageContent />
    </EditModeProvider>
  );
} 