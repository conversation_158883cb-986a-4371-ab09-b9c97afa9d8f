{"name": "lp-creator", "version": "0.1.0", "private": true, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "scripts": {"dev": "next dev", "dev:mastra": "mastra dev --dir mastra", "build": "next build", "build:mastra": "cd mastra && npx tsc --noEmit", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage --watchAll=false", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "ci:lint": "npm run lint && npx tsc --noEmit && npm run build:mastra", "ci:test": "npm run test:coverage", "ci:build": "npm run build", "ci:all": "npm run ci:lint && npm run ci:test && npm run ci:build", "validate:ci": "./scripts/validate-ci.sh", "setup:ci": "./scripts/setup-ci.sh"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@anthropic-ai/sdk": "^0.52.0", "@mastra/core": "^0.10.0", "@mastra/libsql": "^0.10.0", "ai": "^4.3.16", "mastra": "^0.1.57-alpha.58", "next": "15.1.8", "react": "^19.0.0", "react-dom": "^19.0.0", "zod": "^3.25.28"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.42.1", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.8", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.8", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}