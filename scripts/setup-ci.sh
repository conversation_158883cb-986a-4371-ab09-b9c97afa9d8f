#!/bin/bash

# LP Creator CI/CD セットアップスクリプト
# このスクリプトは CI/CD パイプラインのセットアップを支援します

set -e

echo "🚀 LP Creator CI/CD セットアップを開始します..."

# 色の定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# ヘルパー関数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 必要なディレクトリの確認
check_directories() {
    log_info "必要なディレクトリの確認中..."
    
    if [ ! -d ".github" ]; then
        log_warning ".githubディレクトリが存在しません。作成します..."
        mkdir -p .github/workflows
    fi
    
    if [ ! -d ".github/workflows" ]; then
        log_warning ".github/workflowsディレクトリが存在しません。作成します..."
        mkdir -p .github/workflows
    fi
    
    log_success "ディレクトリチェック完了"
}

# ワークフローファイルのコピー
copy_workflows() {
    log_info "ワークフローファイルをコピー中..."
    
    if [ -d "github-workflows" ]; then
        cp github-workflows/*.yml .github/workflows/
        log_success "ワークフローファイルをコピーしました"
    else
        log_error "github-workflowsディレクトリが見つかりません"
        exit 1
    fi
}

# 依存関係の確認
check_dependencies() {
    log_info "必要な依存関係の確認中..."
    
    # Node.js バージョン確認
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        log_success "Node.js: $NODE_VERSION"
    else
        log_error "Node.jsがインストールされていません"
        exit 1
    fi
    
    # npm 確認
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        log_success "npm: $NPM_VERSION"
    else
        log_error "npmがインストールされていません"
        exit 1
    fi
    
    # package.jsonの確認
    if [ -f "package.json" ]; then
        log_success "package.jsonが存在します"
    else
        log_error "package.jsonが見つかりません"
        exit 1
    fi
}

# 必要なパッケージのインストール確認
check_packages() {
    log_info "必要なパッケージの確認中..."
    
    REQUIRED_PACKAGES=(
        "@playwright/test"
        "jest"
        "@testing-library/react"
        "wait-on"
    )
    
    for package in "${REQUIRED_PACKAGES[@]}"; do
        if npm list "$package" &> /dev/null; then
            log_success "$package: インストール済み"
        else
            log_warning "$package: 未インストール"
        fi
    done
}

# CI用の追加パッケージのインストール
install_ci_packages() {
    log_info "CI用追加パッケージのインストール..."
    
    # wait-on (E2E テスト用)
    if ! npm list wait-on &> /dev/null; then
        log_info "wait-on をインストール中..."
        npm install --save-dev wait-on
    fi
    
    # next-bundle-analyzer (バンドルサイズ分析用)
    if ! npm list @next/bundle-analyzer &> /dev/null; then
        log_info "@next/bundle-analyzer をインストール中..."
        npm install --save-dev @next/bundle-analyzer
    fi
    
    log_success "CI用パッケージのインストール完了"
}

# 環境変数テンプレートの確認
check_env_template() {
    log_info "環境変数テンプレートの確認中..."
    
    if [ -f ".env.example" ]; then
        log_success ".env.example が存在します"
    else
        log_warning ".env.example が見つかりません"
    fi
    
    if [ -f ".env.local" ]; then
        log_warning ".env.local は .gitignore に含まれていることを確認してください"
    fi
}

# GitHub Secrets の確認リスト
show_secrets_checklist() {
    log_info "GitHub Secrets の設定が必要です:"
    
    echo ""
    echo "🔐 必須のSecrets:"
    echo "  - ANTHROPIC_API_KEY: Anthropic APIキー"
    echo "  - OPENAI_API_KEY: OpenAI APIキー"
    echo ""
    echo "🚀 デプロイ用のSecrets (Vercel):"
    echo "  - VERCEL_TOKEN: Vercel トークン"
    echo "  - VERCEL_ORG_ID: Vercel 組織ID"
    echo "  - VERCEL_PROJECT_ID: Vercel プロジェクトID"
    echo ""
    echo "🔍 セキュリティスキャン:"
    echo "  - npm audit と CodeQL が自動実行されます（追加設定不要）"
    echo ""
    echo "設定方法: GitHub リポジトリ > Settings > Secrets and variables > Actions"
    echo ""
}

# テストコマンドの実行確認
test_commands() {
    log_info "利用可能なテストコマンドの確認..."
    
    # パッケージインストール
    log_info "依存関係をインストール中..."
    npm ci
    
    # リントの確認
    if npm run lint &> /dev/null; then
        log_success "npm run lint: 実行可能"
    else
        log_warning "npm run lint: エラーがあります"
    fi
    
    # TypeScript チェック
    if npx tsc --noEmit &> /dev/null; then
        log_success "TypeScript チェック: 問題なし"
    else
        log_warning "TypeScript チェック: エラーがあります"
    fi
    
    # Mastra TypeScript チェック
    if npm run build:mastra &> /dev/null; then
        log_success "Mastra TypeScript チェック: 問題なし"
    else
        log_warning "Mastra TypeScript チェック: エラーがあります"
    fi
    
    # ビルドテスト
    log_info "ビルドテストを実行中..."
    if npm run build &> /dev/null; then
        log_success "ビルド: 成功"
    else
        log_warning "ビルド: エラーがあります"
    fi
}

# メイン関数
main() {
    echo ""
    echo "🎯 LP Creator CI/CD セットアップ"
    echo "================================"
    echo ""
    
    check_directories
    echo ""
    
    check_dependencies
    echo ""
    
    check_packages
    echo ""
    
    install_ci_packages
    echo ""
    
    copy_workflows
    echo ""
    
    check_env_template
    echo ""
    
    show_secrets_checklist
    echo ""
    
    # テストの実行は時間がかかるので、オプションにする
    read -p "テストコマンドの動作確認を実行しますか？ (y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        test_commands
        echo ""
    fi
    
    log_success "CI/CD セットアップが完了しました！"
    echo ""
    echo "📋 次のステップ:"
    echo "1. GitHub Secrets を設定してください"
    echo "2. .github/workflows/ ディレクトリのファイルをコミットしてください"
    echo "3. プルリクエストを作成してCI/CDをテストしてください"
    echo ""
    echo "🔗 参考リンク:"
    echo "  - GitHub Actions: https://docs.github.com/en/actions"
    echo "  - Vercel デプロイ: https://vercel.com/docs"
    echo ""
}

# スクリプト実行
main "$@"