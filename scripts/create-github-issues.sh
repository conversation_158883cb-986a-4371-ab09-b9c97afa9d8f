#!/bin/bash

# LP Creator GitHub Issues 一括作成スクリプト
# 使用方法: ./scripts/create-github-issues.sh

set -e

echo "🚀 LP Creator GitHub Issues 作成開始..."

# 必要な環境確認
if ! command -v gh &> /dev/null; then
    echo "❌ GitHub CLI (gh) がインストールされていません"
    echo "インストール: brew install gh"
    exit 1
fi

# GitHub認証確認
if ! gh auth status &> /dev/null; then
    echo "❌ GitHub CLI にログインしていません"
    echo "ログイン: gh auth login"
    exit 1
fi

echo "✅ GitHub CLI 認証確認完了"

# プロジェクト情報
REPO_OWNER=$(gh repo view --json owner --jq .owner.login)
REPO_NAME=$(gh repo view --json name --jq .name)

echo "📁 リポジトリ: $REPO_OWNER/$REPO_NAME"

# Milestones作成
echo "📅 Milestones 作成中..."
gh api repos/$REPO_OWNER/$REPO_NAME/milestones --method POST --field title="Sprint 0 - 基盤構築" --field description="環境&基盤構築" --field due_on="2024-12-20T00:00:00Z" || true
gh api repos/$REPO_OWNER/$REPO_NAME/milestones --method POST --field title="Sprint 1 - コア体験" --field description="左チャット×右プレビューの基本動作完成" --field due_on="2024-12-27T00:00:00Z" || true
gh api repos/$REPO_OWNER/$REPO_NAME/milestones --method POST --field title="Sprint 2 - 編集&保存" --field description="編集機能とクラウド保存の実装" --field due_on="2025-01-03T00:00:00Z" || true
gh api repos/$REPO_OWNER/$REPO_NAME/milestones --method POST --field title="Sprint 3 - 高度機能" --field description="レター作成、AI対話修正、画像挿入" --field due_on="2025-01-17T00:00:00Z" || true
gh api repos/$REPO_OWNER/$REPO_NAME/milestones --method POST --field title="Sprint 4 - 本格運用" --field description="ホスティング、コンセプト理論、エンタープライズ" --field due_on="2025-02-07T00:00:00Z" || true

echo "✅ Milestones 作成完了"

# ラベル作成関数
create_labels() {
    echo "🏷️ ラベル作成中..."
    
    # 種類ラベル
    gh label create "feat" --description "新機能" --color "0052cc" || true
    gh label create "bug" --description "バグ修正" --color "d73a4a" || true
    gh label create "chore" --description "雑務・環境整備" --color "0e8a16" || true
    gh label create "test" --description "テスト関連" --color "ffd33d" || true
    gh label create "docs" --description "ドキュメント" --color "0075ca" || true
    gh label create "design" --description "デザイン・UI" --color "a2eeef" || true
    gh label create "epic" --description "大きな機能群" --color "5319e7" || true
    
    # プロバイダーラベル
    gh label create "provider-claude" --description "Claude AI関連" --color "f9d71c" || true
    gh label create "provider-gemini" --description "Gemini AI関連" --color "1d76db" || true
    gh label create "provider-openai" --description "OpenAI関連" --color "ff6b6b" || true
    
    # 優先度ラベル
    gh label create "priority-high" --description "高優先度" --color "b60205" || true
    gh label create "priority-medium" --description "中優先度" --color "fbca04" || true
    gh label create "priority-low" --description "低優先度" --color "0e8a16" || true
    
    # コンポーネントラベル
    gh label create "ui" --description "UI・フロントエンド" --color "bfdadc" || true
    gh label create "api" --description "API・バックエンド" --color "c5def5" || true
    gh label create "infra" --description "インフラ・DevOps" --color "d4c5f9" || true
    gh label create "dx" --description "開発者体験" --color "e99695" || true
    
    # 新機能ラベル
    gh label create "letter-creation" --description "レター文章作成機能" --color "ff9500" || true
    gh label create "ai-modification" --description "AI対話修正機能" --color "7057ff" || true
    gh label create "image-insertion" --description "画像挿入機能" --color "006b75" || true
    gh label create "hosting" --description "ホスティング機能" --color "84b6eb" || true
    
    echo "✅ ラベル作成完了"
}

create_labels

# Issue作成関数
create_issue() {
    local title="$1"
    local body="$2"
    local labels="$3"
    local milestone="$4"
    local estimate="$5"
    
    echo "📝 Issue作成: $title"
    
    # 受け入れ条件を含むIssue本文を作成
    issue_body="## 📋 概要
$body

## ⏱️ 見積もり時間
$estimate

## ✅ 受け入れ条件 (Definition of Done)
- [ ] ユニットテスト作成・通過
- [ ] 機能動作確認
- [ ] コードレビュー完了
- [ ] ドキュメント更新

## 🔗 関連情報
- Sprint: $milestone
- ラベル: $labels"

    gh issue create \
        --title "$title" \
        --body "$issue_body" \
        --label "$labels" \
        --milestone "$milestone" || echo "⚠️ Issue作成失敗: $title"
}

# Sprint 0 Issues
echo "🌱 Sprint 0 Issues 作成中..."

create_issue \
    "[epic] Project Bootstrap" \
    "Next.js 15 + TypeScript環境の完全セットアップとテスト環境構築" \
    "epic,chore,priority-high" \
    "Sprint 0 - 基盤構築" \
    "4h"

create_issue \
    "ESLint / Prettier / Husky 強化" \
    "Lintルール強化、pre-commitフック設定、CI連携でLintエラー時のビルド失敗" \
    "chore,dx,priority-medium" \
    "Sprint 0 - 基盤構築" \
    "2h"

create_issue \
    "Jest + RTL テスト環境拡充" \
    "React Testing Libraryサンプルテスト作成、npm run test:ci対応" \
    "test,chore,priority-medium" \
    "Sprint 0 - 基盤構築" \
    "3h"

create_issue \
    "GitHub Actions CI 完全版" \
    "lint→test→buildの完全ワークフロー作成、push時自動実行" \
    "chore,infra,priority-high" \
    "Sprint 0 - 基盤構築" \
    "4h"

create_issue \
    "Vercel Preview Deploy Hook" \
    "main pushでVercel Preview URLのPR自動コメント機能追加" \
    "chore,infra,priority-low" \
    "Sprint 0 - 基盤構築" \
    "2h"

create_issue \
    "AI Provider Interface 設計" \
    "src/llm/provider.tsに共通ILLMインターフェイス定義&テスト作成" \
    "feat,api,priority-high" \
    "Sprint 0 - 基盤構築" \
    "4h"

create_issue \
    "Claude Provider stub 実装" \
    "ClaudeProvider.complete()のmock返信実装、ユニットテスト通過" \
    "feat,provider-claude,priority-medium" \
    "Sprint 0 - 基盤構築" \
    "3h"

# Sprint 1 Issues
echo "🌟 Sprint 1 Issues 作成中..."

create_issue \
    "ChatPanel UI V0風リニューアル" \
    "V0風デザインに刷新、ユーザー入力→onSubmit発火のRTLテスト作成" \
    "feat,design,ui,priority-high" \
    "Sprint 1 - コア体験" \
    "6h"

create_issue \
    "/api/generate Edge Route 新規作成" \
    "統一的なLP生成エンドポイント、POST product情報→200 + {html,css}返却" \
    "feat,api,priority-high" \
    "Sprint 1 - コア体験" \
    "4h"

create_issue \
    "Prompt Schema & バリデーション" \
    "Zodスキーマ定義、不正JSON入力に400返却機能" \
    "test,feat,priority-medium" \
    "Sprint 1 - コア体験" \
    "3h"

create_issue \
    "PreviewPane with iframe srcdoc" \
    "iframe srcdoc方式でのプレビュー表示、DOM更新Jestテスト" \
    "feat,design,ui,priority-high" \
    "Sprint 1 - コア体験" \
    "5h"

create_issue \
    "react-split-pane 幅調整導入" \
    "左右分割レイアウト、ドラッグでmin 200px制限のe2eテスト" \
    "feat,design,ui,priority-medium" \
    "Sprint 1 - コア体験" \
    "3h"

create_issue \
    "Full-Screen / New-Tab ボタン" \
    "クリックで/preview/[id]が開き、200返却機能" \
    "feat,ui,priority-medium" \
    "Sprint 1 - コア体験" \
    "2h"

create_issue \
    "Claude streaming 集約" \
    "LLMが3 Chunk以上で届き、最終DOMと一致する機能" \
    "feat,provider-claude,priority-medium" \
    "Sprint 1 - コア体験" \
    "4h"

create_issue \
    "Gemini Provider behind flag" \
    ".env NEXT_PUBLIC_LLM=geminiでprovider切替テスト通過" \
    "feat,provider-gemini,priority-low" \
    "Sprint 1 - コア体験" \
    "5h"

create_issue \
    "E2E Smoke (Playwright)" \
    "商品名入力→LP表示までの自動確認E2Eテスト" \
    "test,e2e,priority-medium" \
    "Sprint 1 - コア体験" \
    "4h"

# Sprint 2 Issues
echo "🛠️ Sprint 2 Issues 作成中..."

create_issue \
    "Image Upload (UploadThing)" \
    "D&Dで画像URLがimgタグに入る機能実装" \
    "feat,ui,priority-medium" \
    "Sprint 2 - 編集&保存" \
    "6h"

create_issue \
    "Tiptap WYSIWYG 導入" \
    "クリック編集→保存でHTML反映するリッチテキスト編集器" \
    "feat,ui,priority-high" \
    "Sprint 2 - 編集&保存" \
    "8h"

create_issue \
    "Undo / Redo ヒストリ機能" \
    "Ctrl+Z / Ctrl+Y でDOM巻き戻しテスト通過" \
    "feat,ui,priority-medium" \
    "Sprint 2 - 編集&保存" \
    "4h"

create_issue \
    "KV Save API (/api/save)" \
    "POST保存→GET /preview/[id]で同内容取得機能" \
    "feat,api,priority-high" \
    "Sprint 2 - 編集&保存" \
    "5h"

create_issue \
    "Preview Route ページ" \
    "/preview/[id] SSR→正しいHTML/CSS出力" \
    "feat,api,priority-high" \
    "Sprint 2 - 編集&保存" \
    "4h"

create_issue \
    "Metadata JSON 保存" \
    "KVに{id, title, createdAt}レコード作成機能" \
    "feat,api,priority-medium" \
    "Sprint 2 - 編集&保存" \
    "3h"

create_issue \
    "CI – Codecov 連携" \
    "PRにカバレッジ%表示機能追加" \
    "chore,dx,priority-low" \
    "Sprint 2 - 編集&保存" \
    "2h"

create_issue \
    "Accessibility Audit (axe)" \
    "npm run test:a11y緑化、axe-core統合" \
    "feat,test,priority-medium" \
    "Sprint 2 - 編集&保存" \
    "3h"

create_issue \
    "README + Storybook 初版" \
    "npm run storybook起動、主要コンポーネントdocs表示" \
    "docs,dx,priority-low" \
    "Sprint 2 - 編集&保存" \
    "4h"

# Sprint 3 Issues (新機能)
echo "✨ Sprint 3 Issues 作成中..."

create_issue \
    "[epic] レター文章作成フロー" \
    "ヒアリング→レター生成→承認→LP作成の完全フロー実装" \
    "epic,letter-creation,priority-high" \
    "Sprint 3 - 高度機能" \
    "12h"

create_issue \
    "チャットベースビジネスヒアリング設計" \
    "対話式での商品/サービス情報収集、進捗表示機能" \
    "feat,ui,letter-creation,priority-high" \
    "Sprint 3 - 高度機能" \
    "4h"

create_issue \
    "レター文章生成API (/api/letter/generate)" \
    "ヒアリング情報→コンセプト提案文章生成API実装" \
    "feat,api,letter-creation,priority-high" \
    "Sprint 3 - 高度機能" \
    "6h"

create_issue \
    "レター承認UI (OK/修正ボタン)" \
    "レター表示→承認→LP生成開始フロー実装" \
    "feat,ui,letter-creation,priority-medium" \
    "Sprint 3 - 高度機能" \
    "3h"

create_issue \
    "[epic] AI対話修正機能" \
    "完成LP→対話修正→リアルタイム反映の完全実装" \
    "epic,ai-modification,priority-high" \
    "Sprint 3 - 高度機能" \
    "16h"

create_issue \
    "修正チャットパネル実装" \
    "LP表示状態での修正専用チャット、差分表示機能" \
    "feat,ui,ai-modification,priority-high" \
    "Sprint 3 - 高度機能" \
    "5h"

create_issue \
    "差分適用API (/api/modify)" \
    "修正指示→HTML/CSS差分適用→プレビュー更新機能" \
    "feat,api,ai-modification,priority-high" \
    "Sprint 3 - 高度機能" \
    "6h"

create_issue \
    "修正履歴管理システム" \
    "修正ステップ保存、Undo/Redo対応機能実装" \
    "feat,api,ai-modification,priority-medium" \
    "Sprint 3 - 高度機能" \
    "5h"

create_issue \
    "[epic] ノーコード画像挿入" \
    "マウスUI画像挿入、D&D、リサイズ機能実装" \
    "epic,image-insertion,priority-medium" \
    "Sprint 3 - 高度機能" \
    "10h"

create_issue \
    "画像ドロップゾーンUI" \
    "LP上への画像D&D、プレースホルダー表示機能" \
    "feat,ui,image-insertion,priority-medium" \
    "Sprint 3 - 高度機能" \
    "4h"

create_issue \
    "画像操作ツールバー実装" \
    "リサイズ、位置調整、削除ボタン、マウス操作機能" \
    "feat,ui,image-insertion,priority-low" \
    "Sprint 3 - 高度機能" \
    "6h"

# Sprint 4 Issues (本格運用)
echo "🌊 Sprint 4 Issues 作成中..."

create_issue \
    "[epic] ツール内ホスティング" \
    "LP公開→独自URL生成→即アクセス可能な機能実装" \
    "epic,hosting,priority-medium" \
    "Sprint 4 - 本格運用" \
    "20h"

create_issue \
    "公開設定UI (パブリック/プライベート)" \
    "公開範囲設定、パスワード保護オプション実装" \
    "feat,ui,hosting,priority-medium" \
    "Sprint 4 - 本格運用" \
    "4h"

create_issue \
    "独自ドメイン対応システム" \
    "カスタムドメイン設定、DNS管理機能実装" \
    "feat,api,hosting,priority-low" \
    "Sprint 4 - 本格運用" \
    "8h"

create_issue \
    "アクセス解析ダッシュボード" \
    "PV、CV、デバイス別統計表示機能実装" \
    "feat,ui,hosting,priority-low" \
    "Sprint 4 - 本格運用" \
    "8h"

create_issue \
    "[epic] 複数コンセプト提案" \
    "オセロ理論、逆張り理論に基づく多角度コンセプト生成" \
    "epic,letter-creation,priority-low" \
    "Sprint 4 - 本格運用" \
    "16h"

create_issue \
    "JPRO向けコンセプト理論統合" \
    "専門的コンセプト生成、差別化戦略提案機能" \
    "feat,api,priority-low" \
    "Sprint 4 - 本格運用" \
    "12h"

create_issue \
    "チーム管理機能 (Auth + RLS)" \
    "組織アカウント、権限管理、コラボレーション機能" \
    "feat,api,priority-low" \
    "Sprint 4 - 本格運用" \
    "16h"

create_issue \
    "エンタープライズAPI料金制限" \
    "使用量ベース課金、クレジット制限、API Rate Limiting" \
    "feat,api,priority-low" \
    "Sprint 4 - 本格運用" \
    "8h"

echo "🎉 全 Issues 作成完了！"
echo "📊 GitHub Project Board: https://github.com/$REPO_OWNER/$REPO_NAME/projects"
echo "📝 Issues一覧: https://github.com/$REPO_OWNER/$REPO_NAME/issues"

echo ""
echo "🚀 次のステップ:"
echo "1. GitHub Project Boardでカラム設定 (Backlog, Sprint X-Todo, In Progress, Review, Done)"
echo "2. 緊急度の高いIssueをSprint 0-Todoに移動"
echo "3. 開発開始!" 