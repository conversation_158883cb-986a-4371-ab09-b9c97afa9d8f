# 📋 GitHub Issues 管理スクリプト

LP Creator プロジェクトのGitHub Issues自動作成・管理用スクリプト集です。

## 🚀 クイックスタート

### 1. 環境準備

```bash
# GitHub CLI インストール (macOS)
brew install gh

# GitHub CLI ログイン
gh auth login

# スクリプト実行権限付与
chmod +x scripts/*.sh
```

### 2. 緊急対応Issues作成（即座実行推奨）

```bash
# 緊急度HIGHのIssues（4件）のみ作成
./scripts/create-urgent-issues.sh
```

**作成されるIssues（13時間）**:
- 🚨 Layout.tsx V0風Grid変更 (2h)
- 🚨 API統合 - /api/generate作成 (4h)  
- 🚨 PreviewPane iframe化 (3h)
- 🚨 GitHub Actions CI完全版 (4h)

### 3. 全スプリントIssues作成

```bash
# Sprint 0-4 の全Issues（約40件）を作成
./scripts/create-github-issues.sh
```

## 📊 作成されるIssue構成

### 🌱 Sprint 0 - 基盤構築（7 Issues）
- 環境&基盤構築
- 期限: 2024-12-20

### 🌟 Sprint 1 - コア体験（9 Issues）  
- 左チャット×右プレビューの基本動作
- 期限: 2024-12-27

### 🛠️ Sprint 2 - 編集&保存（9 Issues）
- 編集機能とクラウド保存
- 期限: 2025-01-03

### ✨ Sprint 3 - 高度機能（11 Issues）
- レター作成、AI対話修正、画像挿入
- 期限: 2025-01-17

### 🌊 Sprint 4 - 本格運用（8 Issues）
- ホスティング、コンセプト理論、エンタープライズ
- 期限: 2025-02-07

## 🏷️ 自動作成されるラベル

### 種類ラベル
- `feat` (新機能) - #0052cc
- `bug` (バグ修正) - #d73a4a  
- `chore` (雑務・環境整備) - #0e8a16
- `test` (テスト関連) - #ffd33d
- `docs` (ドキュメント) - #0075ca
- `design` (デザイン・UI) - #a2eeef
- `epic` (大きな機能群) - #5319e7

### プロバイダーラベル
- `provider-claude` (Claude AI関連) - #f9d71c
- `provider-gemini` (Gemini AI関連) - #1d76db
- `provider-openai` (OpenAI関連) - #ff6b6b

### 優先度ラベル
- `priority-high` (高優先度) - #b60205
- `priority-medium` (中優先度) - #fbca04
- `priority-low` (低優先度) - #0e8a16

### 新機能ラベル
- `letter-creation` (レター文章作成機能) - #ff9500
- `ai-modification` (AI対話修正機能) - #7057ff
- `image-insertion` (画像挿入機能) - #006b75
- `hosting` (ホスティング機能) - #84b6eb

## 📅 自動作成されるMilestones

1. **Sprint 0 - 基盤構築** (期限: 2024-12-20)
2. **Sprint 1 - コア体験** (期限: 2024-12-27)
3. **Sprint 2 - 編集&保存** (期限: 2025-01-03)
4. **Sprint 3 - 高度機能** (期限: 2025-01-17)
5. **Sprint 4 - 本格運用** (期限: 2025-02-07)

## 🎯 GitHub Project セットアップ

### 1. プロジェクトボード作成

```bash
# GitHub WebUIで以下を実行:
# 1. Repository → Projects → New project
# 2. Board view 選択
# 3. Project名: "LP Creator Development"
```

### 2. カラム設定

以下のカラムを作成してください：

| Column | 説明 |
|--------|------|
| **Backlog** | 全Issue初期投入場所 |
| **Sprint 0 - Todo** | 基盤構築の未着手 |
| **Sprint 1 - Todo** | コア体験の未着手 |
| **Sprint 2 - Todo** | 編集&保存の未着手 |
| **Sprint 3 - Todo** | 高度機能の未着手 |
| **Sprint 4 - Todo** | 本格運用の未着手 |
| **In Progress** | 現在作業中 |
| **Review / Test** | PRレビュー・テスト実行中 |
| **Done** | 完了 |

### 3. Issue移動

```bash
# 緊急Issues を Sprint 0-Todo に移動
# 各Sprint の Issues を対応する Todo カラムに移動
```

## 🛠️ 個別Issue作成

### 個別Issue作成例

```bash
# 新しいIssueを手動作成
gh issue create \
  --title "[feat] 新機能タイトル" \
  --body "機能概要と受け入れ条件" \
  --label "feat,priority-medium" \
  --milestone "Sprint 1 - コア体験"
```

### Issue一覧確認

```bash
# 全Issue一覧
gh issue list

# Milestone別Issue確認
gh issue list --milestone "Sprint 0 - 基盤構築"

# ラベル別Issue確認  
gh issue list --label "priority-high"
```

## 📊 進捗管理

### Issue統計確認

```bash
# スプリント進捗確認
gh issue list --milestone "Sprint 0 - 基盤構築" --state closed | wc -l
gh issue list --milestone "Sprint 0 - 基盤構築" | wc -l

# 優先度別未完了Issue確認
gh issue list --label "priority-high" --state open
```

### Project Board確認

```bash
# Project一覧
gh project list

# Project詳細表示
gh project view --owner USERNAME --number 1
```

## 🔄 日次運用

### Daily Standup 準備

```bash
# 昨日完了したIssue
gh issue list --state closed --limit 10

# 今日の予定Issue（In Progress）
gh issue list --assignee @me --state open

# ブロックされているIssue
gh issue list --label "blocked" --state open
```

### Issue移動コマンド例

```bash
# IssueをIn Progressに移動（GitHub CLI v2.20以降）
gh project item-edit --id ITEM_ID --field-name "Status" --text "In Progress"

# IssueをDoneに移動
gh project item-edit --id ITEM_ID --field-name "Status" --text "Done"
```

## 🚨 トラブルシューティング

### 権限エラー

```bash
# GitHub CLI 認証状況確認
gh auth status

# 再認証
gh auth refresh
```

### Issue作成失敗

```bash
# Milestone存在確認
gh api repos/OWNER/REPO/milestones

# Label存在確認  
gh label list
```

### 重複Issue確認

```bash
# タイトル部分一致でIssue検索
gh issue list --search "Layout.tsx" 
```

## 📚 関連ドキュメント

- [プロジェクト管理ガイド](../docs/10-PROJECT-MANAGEMENT-GUIDE.md)
- [実装状況分析](../docs/11-IMPLEMENTATION-STATUS-ANALYSIS.md)
- [アジャイル実行ガイド](../docs/12-AGILE-EXECUTION-GUIDE.md)
- [新機能仕様書](../docs/13-NEW-FEATURES-SPECIFICATION.md)

---

## 🎯 次のアクション

1. ✅ **緊急Issues作成**: `./scripts/create-urgent-issues.sh`
2. ✅ **Project Board作成**: GitHub WebUIで設定
3. ✅ **Sprint 0開始**: Layout.tsx修正から着手
4. 📝 **Daily Progress記録**: 進捗をプロジェクトボードで管理

**🚀 開発開始準備完了！** 