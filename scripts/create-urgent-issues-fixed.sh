#!/bin/bash

# マイルストーン名修正版 High Priority Issues 作成スクリプト

echo "🚨 High Priority Issues 作成開始..."

# Issue #1: ChatPanel UI改善
gh issue create \
  --title "[feat] ChatPanel UI/UX改善" \
  --label "feat,design,ui,priority-medium" \
  --milestone "Sprint 1: コア機能" \
  --body "## 📋 概要

チャットパネルのUI/UXを改善し、ユーザーがより効率的にLP生成指示を出せるようにします。

## 🎯 ユーザーストーリー
As a **LP Creator ユーザー**,  
I want **直感的で使いやすいチャットインターフェース**  
so that **効率的にLP生成の指示を出せる**.

## 🛠️ 現在の問題
- チャット入力エリアが小さい
- 送信ボタンが分かりにくい  
- 過去の会話履歴が見づらい
- キーボードショートカットが不足

## ✅ 提案する解決策
- 入力エリアを拡大（複数行対応）
- 送信ボタンのデザイン改善
- 会話履歴のスクロール改善
- Ctrl+Enter送信対応

## 📝 受け入れ条件 (Definition of Done)
- [ ] 入力エリアが自動拡張する
- [ ] Ctrl+Enterで送信可能
- [ ] 過去の会話が見やすく表示される
- [ ] レスポンシブ対応（モバイルでも使いやすい）
- [ ] ローディング状態の表示改善

## ⏱️ 見積もり時間
3-4時間

## 🔗 関連資料
- [09-V0-UIUX-SPECIFICATION.md](./docs/09-V0-UIUX-SPECIFICATION.md)"

# Issue #2: プレビュー拡大機能
gh issue create \
  --title "[feat] プレビュー拡大表示機能（3パターン）" \
  --label "feat,ui,priority-high" \
  --milestone "Sprint 1: コア機能" \
  --body "## 📋 概要

生成されたLPを詳細確認するため、3つのプレビュー拡大パターンを実装します。

## 🎯 ユーザーストーリー
As a **LP Creator ユーザー**,  
I want **生成されたLPを大きな画面で確認する機能**  
so that **細部まで正確にチェックできる**.

## 🛠️ 実装パターン
1. **ペイン拡大**: 右ペインを一時的に全幅表示
2. **モーダル全画面**: ダイアログで全画面プレビュー  
3. **別タブ表示**: 新しいタブでLP表示

## 📝 受け入れ条件 (Definition of Done)
- [ ] ペイン拡大ボタンで右ペイン全幅表示
- [ ] 全画面モーダルでプレビュー表示
- [ ] 別タブで独立したLP表示
- [ ] 各モードでの戻るボタン実装
- [ ] キーボードショートカット対応（ESCで戻る等）

## ⏱️ 見積もり時間
5-8時間

## 🔗 関連資料
- [09-V0-UIUX-SPECIFICATION.md](./docs/09-V0-UIUX-SPECIFICATION.md)"

# Issue #3: プロバイダー切り替え機能
gh issue create \
  --title "[feat] AIプロバイダー切り替えUI" \
  --label "feat,ui,provider-claude,provider-openai,priority-medium" \
  --milestone "Sprint 2: 編集保存" \
  --body "## 📋 概要

OpenAI、Claude、Gemini等のAIプロバイダーを簡単に切り替えられるUIを実装します。

## 🎯 ユーザーストーリー
As a **LP Creator ユーザー**,  
I want **異なるAIプロバイダーを簡単に切り替える機能**  
so that **最適なAIでLP生成できる**.

## 🛠️ 実装内容
- チャットパネルにプロバイダー選択ドロップダウン
- 各プロバイダーのアイコン・説明表示
- 設定の永続化（LocalStorage）

## 📝 受け入れ条件 (Definition of Done)
- [ ] プロバイダー選択ドロップダウン実装
- [ ] OpenAI/Claude/Gemini切り替え動作
- [ ] 選択状態の永続化
- [ ] 各プロバイダーのアイコン表示
- [ ] エラーハンドリング（APIキー未設定等）

## ⏱️ 見積もり時間
3-4時間"

echo "✅ 追加 Issues 作成完了！"