#!/bin/bash

# LP Creator 緊急対応 Issues 作成スクリプト
# 使用方法: ./scripts/create-urgent-issues.sh

set -e

echo "🚨 LP Creator 緊急対応 Issues 作成開始..."

# 必要な環境確認
if ! command -v gh &> /dev/null; then
    echo "❌ GitHub CLI (gh) がインストールされていません"
    echo "インストール: brew install gh"
    exit 1
fi

# GitHub認証確認
if ! gh auth status &> /dev/null; then
    echo "❌ GitHub CLI にログインしていません"
    echo "ログイン: gh auth login"
    exit 1
fi

echo "✅ GitHub CLI 認証確認完了"

# プロジェクト情報
REPO_OWNER=$(gh repo view --json owner --jq .owner.login)
REPO_NAME=$(gh repo view --json name --jq .name)

echo "📁 リポジトリ: $REPO_OWNER/$REPO_NAME"

# Issue作成関数
create_urgent_issue() {
    local title="$1"
    local body="$2"
    local labels="$3"
    local estimate="$4"
    
    echo "🔴 緊急Issue作成: $title"
    
    issue_body="## 🚨 緊急対応必要
$body

## ⏱️ 見積もり時間
$estimate

## 🔴 緊急度: HIGH
この問題は以下の理由で緊急対応が必要です：
- 基本機能の動作に必要
- 他の開発作業をブロックする可能性
- ユーザー体験に直接影響

## ✅ 受け入れ条件 (Definition of Done)
- [ ] 機能動作確認
- [ ] 基本テスト通過
- [ ] 緊急リリース準備完了

## 🔗 関連ドキュメント
- [実装状況分析](./docs/11-IMPLEMENTATION-STATUS-ANALYSIS.md)
- [V0 UI/UX仕様](./docs/09-V0-UIUX-SPECIFICATION.md)"

    gh issue create \
        --title "🚨 $title" \
        --body "$issue_body" \
        --label "$labels" || echo "⚠️ Issue作成失敗: $title"
}

echo "🔴 緊急対応 Issues 作成中..."

# URGENT-01: Layout.tsx V0風Grid変更
create_urgent_issue \
    "Layout.tsx V0風Grid変更" \
    "現在の通常レイアウトをV0風のGrid 2-columnに変更が必要

**現在の問題**:
\`\`\`typescript
// app/layout.tsx (現在)
<body className={\`\${geistSans.variable} \${geistMono.variable} antialiased\`}>
  {children}
</body>
\`\`\`

**必要な修正**:
\`\`\`typescript
// app/layout.tsx (修正後)
<body className=\"grid h-screen grid-cols-[1fr_1.2fr] overflow-hidden\">
  {children}
</body>
\`\`\`

**期待される結果**:
- 左ペイン45%、右ペイン55%で表示
- 画面全体がh-screenで高さ固定
- overflow-hiddenでスクロール制御" \
    "feat,design,ui,priority-high" \
    "2h"

# URGENT-02: API統合 - /api/generate作成
create_urgent_issue \
    "API統合 - /api/generate作成" \
    "複数のAPI（chat, claude等）を統一的な/api/generateエンドポイントに集約

**現在の問題**:
- \`app/api/chat/\`, \`app/api/claude/\` 等が分散
- 統一的なLP生成エンドポイントが存在しない

**必要な作業**:
\`\`\`typescript
// app/api/generate/route.ts (新規作成)
export async function POST(req: Request) {
  const { prompt, provider = 'openai' } = await req.json();
  // 統一的なLP生成ロジック
  return Response.json({ html, css, metadata });
}
\`\`\`

**期待される結果**:
- POST \`/api/generate\` でLP生成
- provider切り替え対応（openai/claude/gemini）
- \`{html, css, metadata}\` 形式で返却" \
    "feat,api,priority-high" \
    "4h"

# URGENT-03: PreviewPane iframe化
create_urgent_issue \
    "PreviewPane iframe化" \
    "現在のHTML直接表示をiframe srcdoc方式に変更

**現在の問題**:
\`\`\`typescript
// app/components/UnifiedLPViewer.tsx (現在)
<div dangerouslySetInnerHTML={{ __html: lpHtml }} />
\`\`\`

**必要な修正**:
\`\`\`typescript
// app/components/PreviewPane.tsx (新規作成)
<iframe 
  srcDoc={\`<style>\${css}</style>\${html}\`}
  className=\"w-full h-full border-0\"
  sandbox=\"allow-scripts allow-same-origin\"
/>
\`\`\`

**期待される結果**:
- iframe sandboxで安全な表示
- CSS/HTMLの動的更新対応
- フルサイズ表示（w-full h-full）" \
    "feat,ui,priority-high" \
    "3h"

# URGENT-04: GitHub Actions CI完全版
create_urgent_issue \
    "GitHub Actions CI完全版" \
    "現在のClaude Actionsのみから、lint→test→buildの完全CIに拡張

**現在の状況**:
- Claude Actionsのみ存在
- 完全なCI/CDパイプラインが未実装

**必要な作業**:
\`\`\`yaml
# .github/workflows/ci.yml (新規作成)
name: CI
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
      - run: npm ci
      - run: npm run lint
      - run: npm run test
      - run: npm run build
\`\`\`

**期待される結果**:
- push時に自動でlint→test→build実行
- 失敗時の適切なエラー表示
- PRでのCI状況確認" \
    "chore,infra,priority-high" \
    "4h"

echo "🎉 緊急対応 Issues 作成完了！"
echo ""
echo "📋 作成されたIssues:"
echo "1. 🚨 Layout.tsx V0風Grid変更 (2h) - UI基盤修正"
echo "2. 🚨 API統合 - /api/generate作成 (4h) - API統一化" 
echo "3. 🚨 PreviewPane iframe化 (3h) - 安全なプレビュー"
echo "4. 🚨 GitHub Actions CI完全版 (4h) - CI/CD基盤"
echo ""
echo "⏱️ 合計見積もり時間: 13時間"
echo ""
echo "🚀 推奨対応順序:"
echo "1. Layout.tsx修正 (Day 1) - 基盤UI"
echo "2. API統合 (Day 2-3) - バックエンド統一"
echo "3. PreviewPane修正 (Day 4) - 安全表示"
echo "4. CI/CD追加 (Day 5) - 開発効率化"
echo ""
echo "📝 Issues確認: https://github.com/$REPO_OWNER/$REPO_NAME/issues" 