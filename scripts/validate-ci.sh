#!/bin/bash

# LP Creator CI/CD 検証スクリプト
# CI/CD パイプラインの動作を検証します

set -e

# 色の定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# ヘルパー関数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# カウンタ
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# チェック関数
run_check() {
    local description="$1"
    local command="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    log_info "実行中: $description"
    
    if eval "$command" &> /dev/null; then
        log_success "$description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        log_error "$description"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

# 詳細チェック関数（エラー出力あり）
run_check_verbose() {
    local description="$1"
    local command="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    log_info "実行中: $description"
    
    if eval "$command"; then
        log_success "$description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        log_error "$description"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

# 基本的な前提条件チェック
check_prerequisites() {
    echo "🔍 前提条件のチェック"
    echo "===================="
    
    run_check "Node.js インストール確認" "command -v node"
    run_check "npm インストール確認" "command -v npm"
    run_check "package.json 存在確認" "test -f package.json"
    run_check "ワークフローファイル存在確認" "test -d .github/workflows"
    
    echo ""
}

# 依存関係のインストールと確認
check_dependencies() {
    echo "📦 依存関係のチェック"
    echo "==================="
    
    log_info "依存関係をインストール中..."
    run_check "npm ci 実行" "npm ci"
    
    # 重要なパッケージの確認
    run_check "Jest インストール確認" "npm list jest"
    run_check "Playwright インストール確認" "npm list @playwright/test"
    run_check "ESLint インストール確認" "npm list eslint"
    run_check "TypeScript インストール確認" "npm list typescript"
    
    echo ""
}

# リンターとフォーマッタのチェック
check_linting() {
    echo "🧹 コード品質チェック"
    echo "===================="
    
    run_check_verbose "ESLint 実行" "npm run lint"
    run_check_verbose "TypeScript 型チェック" "npx tsc --noEmit"
    run_check_verbose "Mastra TypeScript チェック" "npm run build:mastra"
    
    echo ""
}

# テストの実行
check_tests() {
    echo "🧪 テスト実行"
    echo "============"
    
    # 単体テストの確認
    if [ -d "tests/unit" ] && [ "$(ls -A tests/unit)" ]; then
        run_check_verbose "Jest 単体テスト実行" "npm test -- --watchAll=false"
    else
        log_warning "単体テストファイルが見つかりません (tests/unit/)"
        TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    fi
    
    # E2E テストの準備確認
    run_check "Playwright ブラウザインストール確認" "npx playwright install --dry-run"
    
    echo ""
}

# ビルドの確認
check_build() {
    echo "🏗️  ビルド確認"
    echo "============="
    
    run_check_verbose "Next.js ビルド実行" "npm run build"
    
    # ビルド成果物の確認
    run_check ".next ディレクトリ作成確認" "test -d .next"
    
    echo ""
}

# ワークフローファイルの構文チェック
check_workflows() {
    echo "⚙️  ワークフロー構文チェック"
    echo "========================="
    
    # YAMLファイルの構文チェック（yamlファイルがあれば）
    if command -v yamllint &> /dev/null; then
        for workflow in .github/workflows/*.yml; do
            if [ -f "$workflow" ]; then
                run_check "$(basename $workflow) 構文チェック" "yamllint $workflow"
            fi
        done
    else
        log_warning "yamllint がインストールされていません。手動でYAML構文を確認してください。"
    fi
    
    # 基本的なワークフローファイルの存在確認
    run_check "CI ワークフロー存在確認" "test -f .github/workflows/ci.yml"
    run_check "CD ワークフロー存在確認" "test -f .github/workflows/cd.yml"
    run_check "Code Quality ワークフロー存在確認" "test -f .github/workflows/code-quality.yml"
    run_check "PR Validation ワークフロー存在確認" "test -f .github/workflows/pr-validation.yml"
    
    echo ""
}

# 設定ファイルの確認
check_config_files() {
    echo "📁 設定ファイル確認"
    echo "=================="
    
    run_check "Jest 設定ファイル確認" "test -f jest.config.js"
    run_check "Playwright 設定ファイル確認" "test -f playwright.config.ts"
    run_check "ESLint 設定ファイル確認" "test -f eslint.config.mjs"
    run_check "TypeScript 設定ファイル確認" "test -f tsconfig.json"
    run_check "Lighthouse 設定ファイル確認" "test -f lighthouse.config.js"
    run_check "環境変数テンプレート確認" "test -f .env.example"
    run_check "PR テンプレート確認" "test -f .github/pull_request_template.md"
    
    echo ""
}

# パフォーマンステスト (オプション)
check_performance() {
    echo "🚀 パフォーマンステスト (オプション)"
    echo "================================"
    
    read -p "パフォーマンステストを実行しますか？(時間がかかります) (y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "アプリケーションをビルド・起動中..."
        
        # バックグラウンドでアプリ起動
        npm start &
        SERVER_PID=$!
        
        # サーバーの起動を待つ
        if command -v wait-on &> /dev/null; then
            run_check "アプリケーション起動待機" "npx wait-on http://localhost:3000"
            
            # Lighthouse テスト (lighthouse CLI があれば)
            if command -v lighthouse &> /dev/null; then
                run_check "Lighthouse パフォーマンステスト" "lighthouse http://localhost:3000 --chrome-flags='--headless' --quiet --output=json --output-path=/tmp/lighthouse-report.json"
            else
                log_warning "Lighthouse CLI がインストールされていません"
            fi
        else
            log_warning "wait-on がインストールされていません"
        fi
        
        # サーバーを停止
        kill $SERVER_PID 2>/dev/null || true
        wait $SERVER_PID 2>/dev/null || true
    else
        log_info "パフォーマンステストをスキップしました"
    fi
    
    echo ""
}

# セキュリティチェック
check_security() {
    echo "🔒 セキュリティチェック"
    echo "===================="
    
    run_check_verbose "npm audit 実行" "npm audit --audit-level=moderate"
    
    # .env ファイルの確認
    if [ -f ".env" ] || [ -f ".env.local" ]; then
        log_warning ".env または .env.local ファイルが存在します。.gitignore に含まれていることを確認してください。"
    fi
    
    # gitignore の確認
    if [ -f ".gitignore" ]; then
        if grep -q "\.env" .gitignore; then
            log_success ".env ファイルが .gitignore に含まれています"
        else
            log_warning ".env ファイルが .gitignore に含まれていません"
        fi
    fi
    
    echo ""
}

# 結果サマリーの表示
show_summary() {
    echo "📊 検証結果サマリー"
    echo "=================="
    echo ""
    echo "総チェック数: $TOTAL_CHECKS"
    echo "成功: $PASSED_CHECKS"
    echo "失敗: $FAILED_CHECKS"
    echo ""
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        log_success "すべてのチェックが通りました！ CI/CD パイプラインの準備が整っています。"
        echo ""
        echo "🎉 次のステップ:"
        echo "1. GitHub にコミット・プッシュして CI/CD をテスト"
        echo "2. プルリクエストを作成して自動検証を確認"
        echo "3. GitHub Secrets の設定 (Vercel, APIキーなど)"
        return 0
    else
        log_error "$FAILED_CHECKS 個のチェックが失敗しました。修正してから再実行してください。"
        echo ""
        echo "🔧 推奨アクション:"
        echo "1. 失敗したチェックを確認・修正"
        echo "2. 依存関係の再インストール: npm ci"
        echo "3. 個別にコマンドを実行して詳細エラーを確認"
        return 1
    fi
}

# メイン関数
main() {
    echo ""
    echo "🔍 LP Creator CI/CD 検証"
    echo "========================"
    echo ""
    echo "このスクリプトは CI/CD パイプラインの動作を検証します。"
    echo "完了まで数分かかる場合があります。"
    echo ""
    
    check_prerequisites
    check_dependencies
    check_config_files
    check_workflows
    check_linting
    check_tests
    check_build
    check_security
    check_performance
    
    echo ""
    show_summary
}

# Ctrl+C でのクリーンアップ
cleanup() {
    echo ""
    log_warning "検証が中断されました"
    
    # バックグラウンドプロセスの停止
    jobs -p | xargs -r kill 2>/dev/null || true
    
    exit 1
}

trap cleanup INT

# スクリプト実行
main "$@"