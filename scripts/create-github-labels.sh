#!/bin/bash

# GitHub Labels 一括作成スクリプト
# 使用方法: ./scripts/create-github-labels.sh

echo "🏷️  GitHub Labels 作成開始..."

# 機能種別ラベル
gh label create "feat" --description "新機能" --color "0052cc"
gh label create "bug" --description "バグ修正" --color "d73a4a"
gh label create "chore" --description "雑務・メンテナンス" --color "fef2c0"
gh label create "test" --description "テスト関連" --color "0e8a16"
gh label create "docs" --description "ドキュメント" --color "1d76db"
gh label create "design" --description "UI/UXデザイン" --color "e99695"
gh label create "epic" --description "大きな機能セット" --color "3e4b9e"

# プロバイダー関連
gh label create "provider-claude" --description "Claude AI関連" --color "7b2cbf"
gh label create "provider-gemini" --description "Gemini AI関連" --color "4285f4"
gh label create "provider-openai" --description "OpenAI関連" --color "00a67e"

# 優先度
gh label create "priority-high" --description "高優先度（今スプリント）" --color "b60205"
gh label create "priority-medium" --description "中優先度（次スプリント）" --color "fbca04"
gh label create "priority-low" --description "低優先度（将来）" --color "0e8a16"

# コンポーネント
gh label create "ui" --description "UIコンポーネント" --color "c2e0c6"
gh label create "api" --description "API・バックエンド" --color "bfd4f2"
gh label create "infra" --description "インフラ・CI/CD" --color "d4c5f9"
gh label create "dx" --description "開発者体験" --color "f9d0c4"

echo "✅ GitHub Labels 作成完了！" 