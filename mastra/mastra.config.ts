const isValidLogLevel = (level: string): level is 'debug' | 'info' | 'warn' | 'error' => {
  return ['debug', 'info', 'warn', 'error'].includes(level);
};

const getLogLevel = (): 'debug' | 'info' | 'warn' | 'error' => {
  const level = process.env.MASTRA_LOG_LEVEL;
  return level && isValidLogLevel(level) ? level : 'debug';
};

// Validate required environment variables
if (!process.env.ANTHROPIC_API_KEY) {
  console.warn('Warning: ANTHROPIC_API_KEY is not set. Mastra LLM functionality may not work properly.');
}

export const config = {
  name: 'lp-creator-mastra',
  port: 4000,
  logLevel: getLogLevel(),
  database: {
    url: process.env.DATABASE_URL || 'file:./.mastra/memory.db',
  },
  llm: {
    provider: 'anthropic',
    apiKey: process.env.ANTHROPIC_API_KEY,
  },
};