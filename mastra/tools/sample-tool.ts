import { Tool } from '@mastra/core/tools';
import { z } from 'zod';

export const sampleTool = new Tool({
  id: 'sample-tool',
  description: 'A sample tool for LP Creator integration',
  inputSchema: z.object({
    message: z.string().describe('The message to process'),
  }),
  execute: async (context) => {
    try {
      // ToolExecutionContextのcontextプロパティから入力データを取得
      const { message } = context.context;
      return {
        result: `Processed message: ${message}`,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(`Failed to process message: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});