import { createHeroSection, createFeaturesSection, generateOptimalConcept, createTestimonialsSection, createPricingSection } from '../../ai/tools';
import { createLegacyWrapper } from '../utils/mastrafy';

export const legacyCreateHeroSection = createLegacyWrapper(
  createHeroSection,
  'legacy_create_hero_section',
  {
    description: '既存createHeroSectionのMastraラッパー - ヒーローセクション生成',
    preserveSchema: true,
    addLearning: true,
  }
);

export const legacyCreateFeaturesSection = createLegacyWrapper(
  createFeaturesSection,
  'legacy_create_features_section',
  {
    description: '既存createFeaturesSectionのMastraラッパー - 特徴・機能セクション生成',
    preserveSchema: true,
    addLearning: true,
  }
);

export const legacyGenerateOptimalConcept = createLegacyWrapper(
  generateOptimalConcept,
  'legacy_generate_optimal_concept',
  {
    description: '既存generateOptimalConceptのMastraラッパー - AI最適コンセプト提案',
    preserveSchema: true,
    addLearning: true,
  }
);

export const legacyCreateTestimonialsSection = createLegacyWrapper(
  createTestimonialsSection,
  'legacy_create_testimonials_section',
  {
    description: '既存createTestimonialsSectionのMastraラッパー - お客様の声セクション生成',
    preserveSchema: true,
    addLearning: true,
  }
);

export const legacyCreatePricingSection = createLegacyWrapper(
  createPricingSection,
  'legacy_create_pricing_section',
  {
    description: '既存createPricingSectionのMastraラッパー - 価格表セクション生成',
    preserveSchema: true,
    addLearning: true,
  }
);

export const legacyTools = {
  legacyCreateHeroSection,
  legacyCreateFeaturesSection,
  legacyGenerateOptimalConcept,
  legacyCreateTestimonialsSection,
  legacyCreatePricingSection,
};