import { createTool } from '@mastra/core';
import { z } from 'zod';
import { ProjectMemoryManager, RequirementAnalysis } from '../memory/project-memory';

const analyzeBusinessContext = async (params: {
  businessType: string;
  targetAudience: string;
  goals: string[];
  existingBrand?: string;
  competitors?: string;
}): Promise<RequirementAnalysis> => {
  const memoryManager = new ProjectMemoryManager();
  
  // Find similar successful patterns
  const similarPatterns = await memoryManager.findSimilarPatterns(
    { businessType: params.businessType, targetAudience: params.targetAudience },
    0.8
  );

  // Analyze business type
  let strategy = '';
  let designDirection = '';
  let contentPriorities: string[] = [];

  switch (params.businessType.toLowerCase()) {
    case 'saas':
      strategy = 'B2B重視、ROI・効率性訴求、無料トライアル提供';
      designDirection = 'プロフェッショナル、清潔感、データ視覚化';
      contentPriorities = ['Trust building', 'ROI証明', '機能説明'];
      break;
    case 'e-commerce':
      strategy = 'D2C、購買体験最適化、社会的証明活用';
      designDirection = 'モダン、ビジュアル重視、ユーザビリティ';
      contentPriorities = ['ビジュアル重視', '商品価値', '購入プロセス'];
      break;
    case 'consulting':
      strategy = 'B2B、権威性・専門性アピール、事例重視';
      designDirection = 'エグゼクティブ向け、信頼感、プロフェッショナル';
      contentPriorities = ['権威性', '実績証明', '専門知識'];
      break;
    case 'health & wellness':
      strategy = 'B2C、感情訴求、コミュニティ形成';
      designDirection = '暖かみ、自然感、ライフスタイル';
      contentPriorities = ['健康効果', 'ライフスタイル', '安全性'];
      break;
    default:
      strategy = '汎用的アプローチ、ターゲット特化戦略';
      designDirection = 'バランス型、アクセシブル';
      contentPriorities = ['価値提案', 'ベネフィット', '信頼性'];
  }

  // Generate insights based on target audience
  const insights: string[] = [];
  
  if (params.targetAudience.toLowerCase().includes('business') || 
      params.targetAudience.toLowerCase().includes('企業')) {
    insights.push('B2B顧客は論理的決定を重視、ROI・効率性を強調');
    insights.push('複数の決定者が関与、合意形成プロセスを考慮');
  }
  
  if (params.targetAudience.toLowerCase().includes('young') || 
      params.targetAudience.toLowerCase().includes('若')) {
    insights.push('若年層はモバイルファースト、スピード重視');
    insights.push('ソーシャル要素・共有機能が効果的');
  }
  
  if (params.targetAudience.toLowerCase().includes('small') || 
      params.targetAudience.toLowerCase().includes('小規模')) {
    insights.push('小規模事業者はコスト意識が高い、投資対効果を重視');
    insights.push('簡単・手軽さをアピールポイントに');
  }

  if (params.targetAudience.toLowerCase().includes('professional') || 
      params.targetAudience.toLowerCase().includes('専門')) {
    insights.push('専門職は品質・信頼性を重視');
    insights.push('業界特化機能・専門用語を効果的に活用');
  }

  // Add insights from similar patterns
  if (similarPatterns.length > 0) {
    const bestPattern = similarPatterns.reduce((best, current) => 
      current.outputQuality > best.outputQuality ? current : best
    );
    
    insights.push(`過去の成功パターン: ${params.businessType}分野で${(bestPattern.conversionMetrics?.conversionRate ?? 0) * 100}%のコンバージョン率を達成`);
    
    if (bestPattern.generatedContent.headline) {
      insights.push(`「${bestPattern.generatedContent.headline}」のようなヘッドラインが効果的`);
    }
  }

  // Generate recommendations
  const recommendations: string[] = [
    'ターゲットの痛点に直接訴えかけるヘッドラインを作成',
    '競合との差別化要素を強調したベネフィット表現',
    '社会的証拠を活用した信頼性の構築',
  ];

  if (params.competitors) {
    insights.push(`競合分析: ${params.competitors}との差別化ポイントを特定`);
    recommendations.push('競合優位性を明確に表現したメッセージング');
  }

  if (params.existingBrand) {
    insights.push(`既存ブランド「${params.existingBrand}」の認知度を活用`);
    recommendations.push('ブランド一貫性を保ちながら新規価値を訴求');
  }

  // Calculate confidence score
  let confidenceScore = 0.5;
  
  // Bonus for specific business type
  if (['saas', 'e-commerce', 'consulting', 'health & wellness'].includes(params.businessType.toLowerCase())) {
    confidenceScore += 0.1;
  }
  
  // Bonus for detailed target audience
  if (params.targetAudience.length > 20) confidenceScore += 0.1;
  
  // Bonus for having competitors info
  if (params.competitors) confidenceScore += 0.1;
  
  // Bonus for having brand info
  if (params.existingBrand) confidenceScore += 0.1;
  
  // Bonus for multiple goals
  if (params.goals.length > 1) confidenceScore += 0.1;
  
  // Bonus for similar patterns
  if (similarPatterns.length > 0) confidenceScore += 0.1;

  return {
    businessType: params.businessType,
    targetAudience: params.targetAudience,
    strategy,
    designDirection,
    priorities: contentPriorities,
    insights,
    confidenceScore: Math.min(confidenceScore, 1.0),
  };
};

export const analyzeRequirements = createTool({
  id: 'analyzeRequirements',
  description: 'ユーザー要件を分析し、最適なLP生成戦略を決定',
  inputSchema: z.object({
    businessType: z.string().describe('事業・商材の種類'),
    targetAudience: z.string().describe('ターゲット層の詳細'),
    goals: z.array(z.string()).describe('ビジネス目標のリスト'),
    existingBrand: z.string().optional().describe('既存ブランド名'),
    competitors: z.string().optional().describe('主要競合他社'),
  }),
  execute: async (context: { sessionId: string; parameters: any }) => {
    const params = context.parameters;
    const memoryManager = new ProjectMemoryManager();
    
    try {
      // Business context analysis
      const analysis = await analyzeBusinessContext(params);
      
      // Save analysis to memory
      await memoryManager.saveRequirementAnalysis(analysis, context.sessionId);
      
      // Create market analysis
      const marketAnalysis = {
        competitorAnalysis: params.competitors 
          ? `主要競合 (${params.competitors}) との差別化ポイントを特定しました。`
          : '競合情報が提供されていません。一般的な市場アプローチを推奨します。',
        targetAudiencePain: [
          'コスト効率の改善',
          '時間効率の向上',
          '品質・信頼性の確保',
        ],
        opportunities: [
          'デジタル化のトレンド活用',
          'パーソナライゼーション強化',
          'モバイル体験最適化',
        ],
      };
      
      return {
        strategy: analysis.strategy,
        designDirection: analysis.designDirection,
        contentPriorities: analysis.priorities,
        insights: analysis.insights,
        recommendations: [
          'ターゲットの痛点に直接訴えかける証拠ベースのメッセージング',
          '競合との明確な差別化要素の前面押し出し',
          '段階的信頼構築のための社会的証明活用',
          'モバイルファーストのデザインアプローチ',
        ],
        confidenceScore: analysis.confidenceScore,
        marketAnalysis,
      };
    } catch (error) {
      console.error('Analysis error:', error);
      
      // Fallback analysis if memory operations fail
      const fallbackAnalysis = await analyzeBusinessContext(params);
      
      return {
        strategy: fallbackAnalysis.strategy,
        designDirection: fallbackAnalysis.designDirection,
        contentPriorities: fallbackAnalysis.priorities,
        insights: fallbackAnalysis.insights,
        recommendations: [
          'ターゲットの痛点に直接訴えかけるメッセージング',
          '信頼性を高める社会的証明の活用',
        ],
        confidenceScore: fallbackAnalysis.confidenceScore,
        marketAnalysis: {
          competitorAnalysis: 'Analysis completed with limited context',
          targetAudiencePain: ['General market challenges'],
          opportunities: ['Standard optimization approaches'],
        },
      };
    }
  },
});