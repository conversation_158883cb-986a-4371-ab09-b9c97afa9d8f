import { Memory } from '@mastra/core';
import { LibSQLStore } from '@mastra/libsql';

export interface ProjectSnapshot {
  sessionId: string;
  projectData: LPGenerationData;
  userActions: UserAction[];
  generatedSections: Section[];
  timestamp: Date;
  metadata: ProjectMetadata;
}

export interface LPGenerationData {
  productName?: string;
  targetAudience?: string;
  businessType?: string;
  mainBenefit?: string;
  callToAction?: string;
  industry?: string;
  budget?: string;
  competitors?: string;
}

export interface UserAction {
  type: 'generation' | 'edit' | 'feedback' | 'navigation';
  timestamp: Date;
  details: Record<string, any>;
  sessionId: string;
}

export interface Section {
  id: string;
  type: 'hero' | 'features' | 'testimonials' | 'pricing' | 'concept';
  content: Record<string, any>;
  quality?: number;
  feedback?: string;
  timestamp: Date;
}

export interface ProjectMetadata {
  version: string;
  source: string;
  userId?: string;
  tags?: string[];
  quality?: number;
}

export interface GenerationPattern {
  inputParameters: LPGenerationData;
  outputQuality: number;
  userFeedback: 'positive' | 'negative' | 'neutral';
  generatedContent: Record<string, any>;
  conversionMetrics?: {
    conversionRate?: number;
    engagementRate?: number;
    bounceRate?: number;
  };
}

export interface RequirementAnalysis {
  businessType: string;
  targetAudience: string;
  strategy: string;
  designDirection: string;
  priorities: string[];
  insights: string[];
  confidenceScore?: number;
}

const createProjectMemory = () => {
  const store = new LibSQLStore({
    config: {
      url: process.env.DATABASE_URL || 'file:./.mastra/memory.db',
    },
  });

  return new Memory({
    storage: store,
  });
};

export const lpMemory = createProjectMemory();

export class ProjectMemoryManager {
  private memory: Memory;

  constructor(memory: Memory = lpMemory) {
    this.memory = memory;
  }

  async saveProjectSnapshot(snapshot: ProjectSnapshot): Promise<{ id: string; success: boolean }> {
    try {
      const result = await this.memory.save({
        type: 'project_snapshot',
        data: snapshot,
        sessionId: snapshot.sessionId,
      });
      return result;
    } catch (error) {
      console.error('Failed to save project snapshot:', error);
      throw error;
    }
  }

  async getProjectSnapshots(sessionId: string): Promise<ProjectSnapshot[]> {
    try {
      const results = await this.memory.search({
        type: 'project_snapshot',
        sessionId,
      });
      return results.map(r => r.data as ProjectSnapshot);
    } catch (error) {
      console.error('Failed to retrieve project snapshots:', error);
      throw error;
    }
  }

  async saveRequirementAnalysis(
    analysis: RequirementAnalysis,
    sessionId: string
  ): Promise<{ id: string; success: boolean }> {
    try {
      const result = await this.memory.save({
        type: 'requirement_analysis',
        data: analysis,
        sessionId,
      });
      return result;
    } catch (error) {
      console.error('Failed to save requirement analysis:', error);
      throw error;
    }
  }

  async getRequirementAnalysis(sessionId: string): Promise<RequirementAnalysis | null> {
    try {
      const results = await this.memory.search({
        type: 'requirement_analysis',
        sessionId,
      });
      return results.length > 0 ? (results[0].data as RequirementAnalysis) : null;
    } catch (error) {
      console.error('Failed to retrieve requirement analysis:', error);
      throw error;
    }
  }

  async saveGenerationPattern(
    pattern: GenerationPattern,
    sessionId: string
  ): Promise<{ id: string; success: boolean }> {
    try {
      const result = await this.memory.save({
        type: 'generation_pattern',
        data: pattern,
        sessionId,
      });
      return result;
    } catch (error) {
      console.error('Failed to save generation pattern:', error);
      throw error;
    }
  }

  async findSimilarPatterns(
    parameters: Partial<LPGenerationData>,
    minQuality: number = 0.8
  ): Promise<GenerationPattern[]> {
    try {
      const results = await this.memory.search({
        type: 'generation_pattern',
        filters: {
          'data.outputQuality': { $gte: minQuality },
        },
      });

      return results
        .map(r => r.data as GenerationPattern)
        .filter(pattern => {
          if (parameters.businessType && pattern.inputParameters.businessType === parameters.businessType) {
            return true;
          }
          if (parameters.targetAudience && pattern.inputParameters.targetAudience === parameters.targetAudience) {
            return true;
          }
          if (parameters.industry && pattern.inputParameters.industry === parameters.industry) {
            return true;
          }
          return false;
        });
    } catch (error) {
      console.error('Failed to find similar patterns:', error);
      throw error;
    }
  }

  async saveUserAction(action: UserAction): Promise<{ id: string; success: boolean }> {
    try {
      const result = await this.memory.save({
        type: 'user_action',
        data: action,
        sessionId: action.sessionId,
      });
      return result;
    } catch (error) {
      console.error('Failed to save user action:', error);
      throw error;
    }
  }

  async getUserActions(sessionId: string): Promise<UserAction[]> {
    try {
      const results = await this.memory.search({
        type: 'user_action',
        sessionId,
      });
      return results.map(r => r.data as UserAction);
    } catch (error) {
      console.error('Failed to retrieve user actions:', error);
      throw error;
    }
  }
}