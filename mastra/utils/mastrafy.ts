import { Tool } from '@mastra/core';
import { LPGenerationData, GenerationPattern } from '../memory/project-memory';

export interface MastrafyOptions {
  description: string;
  preserveSchema: boolean;
  addLearning: boolean;
}

export interface MastrafyContext {
  sessionId: string;
  parameters: Record<string, any>;
}

export interface MastrafyResult<T = any> {
  originalResult: T;
  sessionId: string;
  metadata: {
    wrappedAt: Date;
    preserveSchema: boolean;
    addLearning: boolean;
  };
  learningData?: GenerationPattern;
}

export function mastrafy<T = any>(
  originalTool: any,
  options: MastrafyOptions
): Tool {
  const wrappedTool: Tool = {
    id: `legacy_${originalTool.description?.toLowerCase().replace(/\s+/g, '_') || 'tool'}`,
    description: options.description,
    parameters: options.preserveSchema ? originalTool.parameters : undefined,
    
    execute: async (context: MastrafyContext): Promise<MastrafyResult<T>> => {
      try {
        // Execute the original tool
        const originalResult = await originalTool.execute(context.parameters);
        
        // Prepare the wrapped result
        const wrappedResult: MastrafyResult<T> = {
          originalResult,
          sessionId: context.sessionId,
          metadata: {
            wrappedAt: new Date(),
            preserveSchema: options.preserveSchema,
            addLearning: options.addLearning,
          },
        };

        // Add learning data if enabled
        if (options.addLearning) {
          wrappedResult.learningData = {
            inputParameters: context.parameters as LPGenerationData,
            outputQuality: calculateOutputQuality(originalResult),
            userFeedback: 'neutral', // Default, can be updated later
            generatedContent: originalResult,
          };
        }

        return wrappedResult;
      } catch (error) {
        console.error(`Error executing wrapped tool ${originalTool.description}:`, error);
        throw error;
      }
    },
  };

  return wrappedTool;
}

function calculateOutputQuality(result: any): number {
  if (!result) return 0;

  let qualityScore = 0.5; // Base score

  // Check for required fields
  if (typeof result === 'object') {
    const fields = Object.keys(result);
    
    // Bonus for having content
    if (fields.length > 0) qualityScore += 0.1;
    
    // Bonus for having structured content
    if (result.headline || result.title) qualityScore += 0.1;
    if (result.description || result.content) qualityScore += 0.1;
    if (result.features && Array.isArray(result.features)) qualityScore += 0.1;
    
    // Bonus for having complete content
    if (fields.length >= 3) qualityScore += 0.1;
    
    // Check content quality (basic heuristics)
    const textContent = JSON.stringify(result);
    if (textContent.length > 100) qualityScore += 0.05;
    if (textContent.length > 500) qualityScore += 0.05;
  }

  return Math.min(qualityScore, 1.0);
}

export function createLegacyWrapper<T = any>(
  originalTool: any,
  wrapperId: string,
  options: Partial<MastrafyOptions> = {}
): Tool {
  const defaultOptions: MastrafyOptions = {
    description: `Legacy wrapper for ${originalTool.description || wrapperId}`,
    preserveSchema: true,
    addLearning: true,
    ...options,
  };

  return mastrafy<T>(originalTool, defaultOptions);
}