import { <PERSON><PERSON> } from '@mastra/core';
import { LibSQLStore } from '@mastra/libsql';
import { existsSync, mkdirSync } from 'fs';
import { dirname } from 'path';

// Sample agent
import { sampleAgent } from './agents/sample-agent';

// データベースファイルのパスを設定
const dbPath = process.env.DATABASE_URL || 'file:./.mastra/memory.db';
const dbFilePath = dbPath.replace('file:', '');

// データベースディレクトリが存在しない場合は作成
if (!existsSync(dirname(dbFilePath))) {
  mkdirSync(dirname(dbFilePath), { recursive: true });
}

const mastra = new Mastra({
  storage: new LibSQLStore({
    url: dbPath,
  }),
  agents: {
    sampleAgent,
  },
});

export default mastra;