import { Agent } from '@mastra/core';
import { anthropic } from '@ai-sdk/anthropic';
import { sampleTool } from '../tools/sample-tool';

// Validate required environment variables
const validateConfig = () => {
  if (!process.env.ANTHROPIC_API_KEY) {
    throw new Error('ANTHROPIC_API_KEY environment variable is required for the sample agent');
  }
};

export const sampleAgent = new Agent({
  name: 'sample-agent',
  instructions: 'You are a helpful assistant for LP Creator. You can help with landing page creation and content generation.',
  model: anthropic('claude-3-5-sonnet-20241022'),
  tools: {
    sampleTool,
  },
});

// Validate configuration only if <PERSON><PERSON> is enabled
if (process.env.ENABLE_MASTRA === 'true') {
  validateConfig();
}