---
description: 
globs: 
alwaysApply: false
---
# UIコンポーネント スタイリングルール

## 基本方針
すべてのUIコンポーネントは**可読性**と**アクセシビリティ**を最優先とする。

## 1. 文字色（テキストカラー）

### 必須ルール
- メインテキスト: `#333` または `#000`
- サブテキスト: `#555` 以上
- エラー時以外で `#999` の使用禁止
- 詳細は [text-color-accessibility.mdc](mdc:.cursor/rules/text-color-accessibility.mdc) を参照

### インラインスタイルでの指定例
```tsx
// ✅ 推奨
style={{ color: '#333' }}  // メインテキスト
style={{ color: '#555' }}  // サブテキスト
style={{ color: '#2196f3' }}  // アクセント

// ❌ 禁止
style={{ color: '#999' }}  // 薄すぎ
style={{ color: '#666' }}  // 要検討（用途限定）
```

## 2. レイアウトとスペーシング

### パディング・マージンの統一
```tsx
// 推奨するスペーシング値（8pxベース）
const SPACING = {
  xs: '4px',
  sm: '8px', 
  md: '16px',
  lg: '24px',
  xl: '32px',
  xxl: '48px'
}

// 使用例
style={{ padding: '16px' }}      // md
style={{ margin: '8px 16px' }}   // sm md
```

### コンテナのボーダーと角丸
```tsx
// 推奨値
borderRadius: '8px'      // 標準的な角丸
borderRadius: '12px'     // 大きめのコンテナ
border: '1px solid #eee' // 薄いボーダー
border: '1px solid #ddd' // 少し濃いボーダー
```

## 3. ボタンスタイリング

### プライマリボタン
```tsx
style={{
  backgroundColor: '#2196f3',
  color: 'white',
  border: 'none',
  padding: '12px 24px',
  borderRadius: '8px',
  fontSize: '14px',
  fontWeight: '500',
  cursor: 'pointer'
}}
```

### セカンダリボタン
```tsx
style={{
  backgroundColor: 'transparent',
  color: '#2196f3',
  border: '1px solid #2196f3',
  padding: '12px 24px',
  borderRadius: '8px',
  fontSize: '14px',
  cursor: 'pointer'
}}
```

### 無効状態（この場合のみ薄い色OK）
```tsx
style={{
  backgroundColor: '#f5f5f5',
  color: '#999',
  border: '1px solid #ddd',
  cursor: 'not-allowed'
}}
```

## 4. フォーム要素

### 入力フィールド
```tsx
style={{
  padding: '8px 12px',
  border: '1px solid #ddd',
  borderRadius: '6px',
  fontSize: '14px',
  color: '#333',        // 入力テキストは濃く
  backgroundColor: 'white'
}}
```

### ラベル
```tsx
style={{
  display: 'block',
  marginBottom: '8px',
  fontWeight: '500',
  color: '#333'         // ラベルは必ず濃い色
}}
```

### プレースホルダー（CSS-in-JSの場合）
```tsx
'::placeholder': {
  color: '#666'         // プレースホルダーのみ薄い色許可
}
```

## 5. カードコンポーネント

### 基本カードスタイル
```tsx
style={{
  backgroundColor: 'white',
  borderRadius: '8px',
  border: '1px solid #eee',
  padding: '16px',
  boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
}}
```

### カード内テキスト
```tsx
// タイトル
style={{ fontSize: '16px', fontWeight: '600', color: '#333' }}

// 本文
style={{ fontSize: '14px', color: '#333' }}

// サブテキスト
style={{ fontSize: '12px', color: '#555' }}
```

## 6. モーダル・ダイアログ

### オーバーレイ
```tsx
style={{
  position: 'fixed',
  top: 0, left: 0, right: 0, bottom: 0,
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  zIndex: 1000
}}
```

### モーダルボディ
```tsx
style={{
  backgroundColor: 'white',
  borderRadius: '12px',
  maxWidth: '90%',
  maxHeight: '80vh',
  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)'
}}
```

## 7. ステート管理

### ホバー効果
```tsx
onMouseOver={(e) => {
  e.currentTarget.style.backgroundColor = '#f0f8ff';
}}
onMouseOut={(e) => {
  e.currentTarget.style.backgroundColor = 'white';
}}
```

### フォーカス状態
```tsx
onFocus={(e) => {
  e.currentTarget.style.outline = '2px solid #2196f3';
}}
```

## 8. レスポンシブ対応

### グリッドレイアウト
```tsx
style={{
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
  gap: '12px'
}}
```

### フレックスレイアウト
```tsx
style={{
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  gap: '8px'
}}
```

## 9. チェックリスト

### 新規コンポーネント作成時
- [ ] 文字色は `#333` 以上の濃さか
- [ ] ボタンの無効状態は適切に実装されているか
- [ ] ホバー・フォーカス状態が定義されているか
- [ ] レスポンシブ対応がされているか
- [ ] アクセシビリティ（aria-label等）が考慮されているか

### 既存コンポーネント修正時
- [ ] 薄い文字色（`#999`, `#666`）の使用を見直したか
- [ ] 統一されたスペーシングを使用しているか
- [ ] 適切なコントラスト比を保っているか

## 10. 関連ファイル
- [text-color-accessibility.mdc](mdc:.cursor/rules/text-color-accessibility.mdc)

