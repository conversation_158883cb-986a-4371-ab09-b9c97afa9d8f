---
description: 
globs: 
alwaysApply: false
---
# 文字色・アクセシビリティルール

## 基本原則

### 1. 文字色のコントラスト基準
- **メインテキスト**: `#333` または `#000` を使用（グレー系 `#999`, `#666` は避ける）
- **サブテキスト**: 最低でも `#555` 以上の濃さを保つ
- **プレースホルダー**: `#666` 以上の濃さを使用
- **無効状態**: `#999` は無効状態のみに使用

### 2. 禁止事項
- **絶対に避ける色**:
  - `color: '#999'` - 薄すぎて読みにくい
  - `color: '#ccc'` - 極めて薄い
  - `color: '#ddd'` - ほぼ背景色
- **警告が必要な色**:
  - `color: '#666'` - 使用する場合は慎重に（サブテキストのみ）

### 3. 推奨色パレット
```typescript
const TEXT_COLORS = {
  primary: '#333',      // メインテキスト
  secondary: '#555',    // サブテキスト
  placeholder: '#666',  // プレースホルダー（最低限）
  disabled: '#999',     // 無効状態のみ
  accent: '#2196f3',    // アクセントテキスト
  error: '#d32f2f',     // エラーテキスト
  success: '#388e3c',   // 成功テキスト
}
```

## 実装ガイドライン

### 1. React コンポーネントでの文字色指定
```tsx
// ❌ 悪い例
<div style={{ color: '#999' }}>読みにくいテキスト</div>
<p style={{ color: '#666' }}>薄すぎるテキスト</p>

// ✅ 良い例
<div style={{ color: '#333' }}>読みやすいメインテキスト</div>
<p style={{ color: '#555' }}>読みやすいサブテキスト</p>
<span style={{ color: '#666' }}>プレースホルダー（最低限）</span>
```

### 2. 階層別の文字色使用例
```tsx
// ヘッダー・タイトル
<h1 style={{ color: '#333' }}>メインタイトル</h1>
<h2 style={{ color: '#333' }}>サブタイトル</h2>

// ボディテキスト
<p style={{ color: '#333' }}>メイン文章</p>
<span style={{ color: '#555' }}>補足説明</span>

// フォーム要素
<label style={{ color: '#333' }}>ラベル</label>
<input style={{ color: '#333' }} placeholder="プレースホルダー" />

// ボタン内テキスト
<button style={{ color: 'white', backgroundColor: '#2196f3' }}>
  アクションボタン
</button>
```

### 3. 特別な用途での文字色
```tsx
// リンク
<a style={{ color: '#2196f3' }}>リンクテキスト</a>

// エラーメッセージ
<span style={{ color: '#d32f2f' }}>エラーメッセージ</span>

// 成功メッセージ
<span style={{ color: '#388e3c' }}>成功メッセージ</span>

// 無効状態（この場合のみ#999使用可）
<button disabled style={{ color: '#999' }}>無効ボタン</button>
```

## チェックリスト

### コードレビュー時の確認事項
- [ ] `color: '#999'` または `color: '#666'` の使用箇所を特定
- [ ] 読みやすさの観点から適切な文字色か確認
- [ ] プレースホルダー以外で薄い色を使用していないか
- [ ] 無効状態以外で `#999` を使用していないか
- [ ] アクセシビリティ基準（WCAG AA）を満たしているか

### 修正時の対応
1. **即座に修正が必要**: `#999`, `#ccc`, `#ddd`
2. **検討が必要**: `#666`（用途によっては`#555`または`#333`に変更）
3. **適切**: `#333`, `#555`, `#2196f3`（アクセント）


## 参考資料
- WCAG 2.1 コントラスト基準（AA）: 最低4.5:1
- Material Design テキストカラーガイドライン
- アクセシビリティベストプラクティス
