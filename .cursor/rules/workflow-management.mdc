---
description:
globs:
alwaysApply: false
---
# GitHub Workflow Management Rules

## Workflow Directory Structure

All GitHub Actions workflows are located in [.github/workflows](mdc:.github/workflows):

```
.github/workflows/
├── ci.yml              # Continuous Integration
├── cd.yml              # Continuous Deployment  
├── code-quality.yml    # Code Quality & Security
├── pr-validation.yml   # Pull Request Validation
└── claude.yml          # Claude Code Integration
```

## Node.js Version Management

### Current Standard
- **Node.js Version**: 20
- **NPM Version**: >=10.0.0
- **Engine Requirements**: Defined in [package.json](mdc:package.json)

### Workflow Configuration
All workflows must use Node.js 20:
```yaml
env:
  NODE_VERSION: '20'

steps:
  - name: Setup Node.js
    uses: actions/setup-node@v4
    with:
      node-version: ${{ env.NODE_VERSION }}
      cache: 'npm'
```

## Dependency Installation Pattern

### Standard Installation Process
```yaml
- name: Install dependencies
  run: |
    # Check if package-lock.json is in sync
    if ! npm ci --dry-run > /dev/null 2>&1; then
      echo "⚠️ package-lock.json is out of sync, regenerating..."
      rm -f package-lock.json
      npm install --package-lock-only
    fi
    npm ci
```

## Secret Management

### Required Secrets
- `VERCEL_TOKEN` - Vercel deployment token
- `VERCEL_ORG_ID` - Vercel organization ID
- `VERCEL_PROJECT_ID` - Vercel project ID
- `ANTHROPIC_API_KEY` - Claude API key

### Optional Secrets
- `SNYK_TOKEN` - Snyk security scanning (conditional)

### Conditional Secret Usage
```yaml
- name: Run Snyk security scan
  if: env.SNYK_TOKEN != ''
  uses: snyk/actions/node@master
  env:
    SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
```

## Environment Configuration

### GitHub Environments
- `staging` - For staging deployments
- `production` - For production deployments (optional)

### Environment Variables
```yaml
env:
  NODE_ENV: production
  NODE_VERSION: '20'
  CACHE_KEY_SUFFIX: v1
```

## Workflow Triggers

### CI Workflow ([ci.yml](mdc:.github/workflows/ci.yml))
```yaml
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
```

### CD Workflow ([cd.yml](mdc:.github/workflows/cd.yml))
```yaml
on:
  push:
    branches: [ main ]
  release:
    types: [ published ]
```

## Error Handling Best Practices

### Common Issues and Solutions
1. **Node.js Version Mismatch**: Update all workflows to use Node.js 20
2. **Package Lock Sync**: Use the standard installation pattern
3. **Missing Secrets**: Use conditional execution for optional secrets
4. **Environment Names**: Avoid using reserved names like 'production'

### Debugging Commands
```bash
# Validate workflow syntax
yamllint .github/workflows/

# Check Node.js version requirements
grep -r "node.*>=" package.json

# List workflow files
ls -la .github/workflows/
```

## Related Documentation
- [Workflow Fix Report](mdc:docs/REPORT/WORKFLOW_FIX.md)
- [Lint Fix Report](mdc:docs/REPORT/WORKFLOW_LINT_FIX.md)
- [Package Configuration](mdc:package.json)
