# Code Quality & Security Workflow
# Comprehensive CI/CD pipeline for code quality assurance
name: Code Quality & Security

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run weekly security scans
    - cron: '0 2 * * 1'

jobs:
  security-scan:
    name: Security Vulnerability Scan
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      contents: read
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run comprehensive npm audit
        run: |
          echo "🔍 Running comprehensive npm audit..."
          
          # 基本的なaudit
          echo "📋 Basic audit results:"
          npm audit --audit-level=low || echo "⚠️ Vulnerabilities found"
          
          # 詳細なaudit結果をJSON形式で取得
          echo "📊 Detailed audit analysis:"
          npm audit --json > audit-results.json || true
          
          # 脆弱性の統計を表示
          if [ -f audit-results.json ]; then
            echo "📈 Vulnerability summary:"
            cat audit-results.json | jq -r '
              if .vulnerabilities then
                "Critical: " + (.vulnerabilities | map(select(.severity == "critical")) | length | tostring) +
                ", High: " + (.vulnerabilities | map(select(.severity == "high")) | length | tostring) +
                ", Moderate: " + (.vulnerabilities | map(select(.severity == "moderate")) | length | tostring) +
                ", Low: " + (.vulnerabilities | map(select(.severity == "low")) | length | tostring)
              else
                "No vulnerabilities found or audit format changed"
              end
            ' || echo "Could not parse audit results"
          fi
          
          # 修正可能な脆弱性をチェック
          echo "🔧 Checking for fixable vulnerabilities:"
          npm audit fix --dry-run || echo "No automatic fixes available"

      - name: Check for outdated packages
        run: |
          echo "📦 Checking for outdated packages..."
          npm outdated || echo "Some packages may be outdated"

  codeql-analysis:
    name: CodeQL Security Analysis
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      contents: read
    strategy:
      matrix:
        language: [ 'javascript', 'typescript' ]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: ${{ matrix.language }}
          queries: security-extended,security-and-quality

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application for analysis
        run: npm run build

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2

  dependency-review:
    name: Dependency Review
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Dependency Review
        uses: actions/dependency-review-action@v3
        with:
          fail-on-severity: moderate
          allow-licenses: MIT, Apache-2.0, BSD-3-Clause, ISC

  code-coverage:
    name: Code Coverage Analysis
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests with coverage
        run: npm test -- --coverage --watchAll=false

      - name: Check coverage thresholds
        run: |
          echo "🔍 Checking coverage thresholds..."
          
          if [ -f coverage/coverage-summary.json ]; then
            COVERAGE=$(cat coverage/coverage-summary.json | jq '.total.lines.pct')
            echo "Current line coverage: $COVERAGE%"
            
            BRANCH_COVERAGE=$(cat coverage/coverage-summary.json | jq '.total.branches.pct')
            echo "Current branch coverage: $BRANCH_COVERAGE%"
            
            FUNCTION_COVERAGE=$(cat coverage/coverage-summary.json | jq '.total.functions.pct')
            echo "Current function coverage: $FUNCTION_COVERAGE%"
            
            if (( $(echo "$COVERAGE < 70" | bc -l) )); then
              echo "❌ Line coverage below 70% threshold: $COVERAGE%"
              exit 1
            else
              echo "✅ Line coverage meets 70% threshold: $COVERAGE%"
            fi
          else
            echo "⚠️ Coverage summary not found, skipping threshold check"
          fi

      - name: Generate coverage report
        run: |
          echo "📊 Coverage Report Summary:"
          if [ -f coverage/coverage-summary.json ]; then
            cat coverage/coverage-summary.json | jq -r '
              "Lines: " + (.total.lines.pct | tostring) + "% (" + (.total.lines.covered | tostring) + "/" + (.total.lines.total | tostring) + ")" +
              "\nBranches: " + (.total.branches.pct | tostring) + "% (" + (.total.branches.covered | tostring) + "/" + (.total.branches.total | tostring) + ")" +
              "\nFunctions: " + (.total.functions.pct | tostring) + "% (" + (.total.functions.covered | tostring) + "/" + (.total.functions.total | tostring) + ")" +
              "\nStatements: " + (.total.statements.pct | tostring) + "% (" + (.total.statements.covered | tostring) + "/" + (.total.statements.total | tostring) + ")"
            '
          fi

  performance-audit:
    name: Performance Audit
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Start application
        run: npm start &
        env:
          NODE_ENV: production

      - name: Wait for application
        run: npx wait-on http://localhost:3000

      - name: Run Lighthouse CI
        uses: treosh/lighthouse-ci-action@v9
        with:
          configPath: './lighthouse.config.js'
          uploadArtifacts: true
          temporaryPublicStorage: true

  bundle-analysis:
    name: Bundle Size Analysis
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build and analyze bundle
        run: |
          npm run build
          echo "📦 Bundle analysis completed"
          
          # Next.js bundle analyzerが利用可能かチェック
          if npm list @next/bundle-analyzer > /dev/null 2>&1; then
            echo "Running Next.js bundle analyzer..."
            npx next-bundle-analyzer
          else
            echo "⚠️ @next/bundle-analyzer not installed, skipping detailed analysis"
          fi

      - name: Bundle size check
        uses: andresz1/size-limit-action@v1
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          skip_step: install

  security-headers:
    name: Security Headers Check
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build and start application
        run: |
          npm run build
          npm start &
          npx wait-on http://localhost:3000

      - name: Test security headers
        run: |
          echo "🔍 Testing security headers..."
          
          # Check CSP header
          CSP=$(curl -s -D - http://localhost:3000 | grep -i "content-security-policy" || echo "missing")
          if [[ "$CSP" != "missing" ]]; then
            echo "✅ CSP header found: $CSP"
          else
            echo "⚠️ CSP header missing"
          fi
          
          # Check X-Frame-Options
          XFO=$(curl -s -D - http://localhost:3000 | grep -i "x-frame-options" || echo "missing")
          if [[ "$XFO" != "missing" ]]; then
            echo "✅ X-Frame-Options header found: $XFO"
          else
            echo "⚠️ X-Frame-Options header missing"
          fi
          
          # Check X-Content-Type-Options
          XCTO=$(curl -s -D - http://localhost:3000 | grep -i "x-content-type-options" || echo "missing")
          if [[ "$XCTO" != "missing" ]]; then
            echo "✅ X-Content-Type-Options header found: $XCTO"
          else
            echo "⚠️ X-Content-Type-Options header missing"
          fi
          
          # Check Strict-Transport-Security
          HSTS=$(curl -s -D - http://localhost:3000 | grep -i "strict-transport-security" || echo "missing")
          if [[ "$HSTS" != "missing" ]]; then
            echo "✅ HSTS header found: $HSTS"
          else
            echo "⚠️ HSTS header missing (normal for localhost)"
          fi