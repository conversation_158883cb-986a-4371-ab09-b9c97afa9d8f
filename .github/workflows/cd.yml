# このワークフローは一時的に無効化されています
# Vercel UI連携による自動デプロイを使用するため

name: Continuous Deployment (Disabled)

# ワークフローを無効化するため、トリガーを削除
on:
  workflow_dispatch:
    inputs:
      force_enable:
        description: 'Force enable this workflow (for emergency use only)'
        required: false
        default: 'false'

jobs:
  disabled_notice:
    runs-on: ubuntu-latest
    if: false  # このワークフローを無効化
    steps:
      - name: Workflow Disabled Notice
        run: |
          echo "⚠️ このワークフローは無効化されています"
          echo "Vercel UI連携による自動デプロイを使用してください"