name: Continuous Integration

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '20'
  CACHE_KEY_SUFFIX: v1

jobs:
  setup:
    name: Setup and Cache Dependencies
    runs-on: ubuntu-latest
    outputs:
      cache-hit: ${{ steps.cache.outputs.cache-hit }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Cache node_modules
        id: cache
        uses: actions/cache@v3
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ env.NODE_VERSION }}-${{ hashFiles('package-lock.json') }}-${{ env.CACHE_KEY_SUFFIX }}
          restore-keys: |
            ${{ runner.os }}-node-${{ env.NODE_VERSION }}-

      - name: Install dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: |
          # Check if package-lock.json is in sync
          if ! npm ci --dry-run > /dev/null 2>&1; then
            echo "⚠️ package-lock.json is out of sync, regenerating..."
            rm -f package-lock.json
            npm install --package-lock-only
          fi
          npm ci

  lint:
    name: Code Linting
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Restore node_modules
        uses: actions/cache@v3
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ env.NODE_VERSION }}-${{ hashFiles('package-lock.json') }}-${{ env.CACHE_KEY_SUFFIX }}
          restore-keys: |
            ${{ runner.os }}-node-${{ env.NODE_VERSION }}-

      - name: Install dependencies (if needed)
        if: needs.setup.outputs.cache-hit != 'true'
        run: |
          # Check if package-lock.json is in sync
          if ! npm ci --dry-run > /dev/null 2>&1; then
            echo "⚠️ package-lock.json is out of sync, regenerating..."
            rm -f package-lock.json
            npm install --package-lock-only
          fi
          npm ci

      - name: Run ESLint
        run: npm run lint

      - name: Check TypeScript
        run: npx tsc --noEmit

      - name: Check Mastra TypeScript
        run: npm run build:mastra

  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Restore node_modules
        uses: actions/cache@v3
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ env.NODE_VERSION }}-${{ hashFiles('package-lock.json') }}-${{ env.CACHE_KEY_SUFFIX }}
          restore-keys: |
            ${{ runner.os }}-node-${{ env.NODE_VERSION }}-

      - name: Install dependencies (if needed)
        if: needs.setup.outputs.cache-hit != 'true'
        run: npm ci

      - name: Run Jest tests
        run: npm test -- --coverage --watchAll=false

      - name: Display coverage summary
        run: |
          echo "📊 Test Coverage Summary:"
          if [ -f coverage/coverage-summary.json ]; then
            cat coverage/coverage-summary.json | jq -r '
              "Lines: " + (.total.lines.pct | tostring) + "% (" + (.total.lines.covered | tostring) + "/" + (.total.lines.total | tostring) + ")" +
              "\nBranches: " + (.total.branches.pct | tostring) + "% (" + (.total.branches.covered | tostring) + "/" + (.total.branches.total | tostring) + ")" +
              "\nFunctions: " + (.total.functions.pct | tostring) + "% (" + (.total.functions.covered | tostring) + "/" + (.total.functions.total | tostring) + ")" +
              "\nStatements: " + (.total.statements.pct | tostring) + "% (" + (.total.statements.covered | tostring) + "/" + (.total.statements.total | tostring) + ")"
            '
          else
            echo "⚠️ Coverage summary not found"
          fi

  e2e-tests:
    name: End-to-End Tests
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Restore node_modules
        uses: actions/cache@v3
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ env.NODE_VERSION }}-${{ hashFiles('package-lock.json') }}-${{ env.CACHE_KEY_SUFFIX }}
          restore-keys: |
            ${{ runner.os }}-node-${{ env.NODE_VERSION }}-

      - name: Install dependencies (if needed)
        if: needs.setup.outputs.cache-hit != 'true'
        run: npm ci

      - name: Install Playwright Browsers
        run: npx playwright install --with-deps

      - name: Build application
        run: npm run build
        env:
          NODE_ENV: production

      - name: Start application
        run: npm start &
        env:
          NODE_ENV: production

      - name: Wait for application
        run: npx wait-on http://localhost:3000

      - name: Run Playwright tests
        run: npm run test:e2e

      - name: Upload Playwright Report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30

  build:
    name: Build Verification
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Restore node_modules
        uses: actions/cache@v3
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ env.NODE_VERSION }}-${{ hashFiles('package-lock.json') }}-${{ env.CACHE_KEY_SUFFIX }}
          restore-keys: |
            ${{ runner.os }}-node-${{ env.NODE_VERSION }}-

      - name: Install dependencies (if needed)
        if: needs.setup.outputs.cache-hit != 'true'
        run: npm ci

      - name: Build Next.js application
        run: npm run build
        env:
          NODE_ENV: production

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-artifacts
          path: .next/
          retention-days: 7

  quality-gate:
    name: Quality Gate
    runs-on: ubuntu-latest
    needs: [lint, unit-tests, e2e-tests, build]
    if: always()
    steps:
      - name: Check job results
        run: |
          if [[ "${{ needs.lint.result }}" == "failure" || "${{ needs.unit-tests.result }}" == "failure" || "${{ needs.e2e-tests.result }}" == "failure" || "${{ needs.build.result }}" == "failure" ]]; then
            echo "❌ Quality gate failed"
            exit 1
          else
            echo "✅ Quality gate passed"
          fi