name: Pull Request Validation

on:
  pull_request:
    types: [ opened, synchronize, reopened ]
    branches: [ main, develop ]

jobs:
  pr-info:
    name: PR Information Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Check PR title format
        uses: amannn/action-semantic-pull-request@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          types: |
            feat
            fix
            docs
            style
            refactor
            perf
            test
            build
            ci
            chore
            revert
          requireScope: false
          disallowScopes: |
            release
          subjectPattern: ^(?![A-Z]).+$
          subjectPatternError: |
            The subject "{subject}" found in the pull request title "{title}"
            didn't match the configured pattern. Please ensure that the subject
            doesn't start with an uppercase character.

      - name: Check PR description
        uses: actions/github-script@v6
        with:
          script: |
            const pr = context.payload.pull_request;
            if (!pr.body || pr.body.trim().length < 10) {
              core.setFailed('PR description is required and must be at least 10 characters long');
            }

  auto-label:
    name: Auto Label PR
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Label based on changed files
        uses: actions/github-script@v6
        with:
          script: |
            const { data: files } = await github.rest.pulls.listFiles({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.issue.number,
            });

            const labels = new Set();

            for (const file of files) {
              const path = file.filename;
              
              // Frontend changes
              if (path.startsWith('app/') || path.includes('.tsx') || path.includes('.jsx')) {
                labels.add('frontend');
              }
              
              // Backend/API changes
              if (path.startsWith('app/api/') || path.startsWith('mastra/')) {
                labels.add('backend');
              }
              
              // Documentation changes
              if (path.startsWith('docs/') || path.includes('README') || path.includes('.md')) {
                labels.add('documentation');
              }
              
              // Test changes
              if (path.startsWith('tests/') || path.includes('.test.') || path.includes('.spec.')) {
                labels.add('tests');
              }
              
              // CI/CD changes
              if (path.startsWith('.github/') || path.includes('package.json')) {
                labels.add('ci/cd');
              }
              
              // AI/Mastra changes
              if (path.startsWith('ai/') || path.startsWith('mastra/') || path.includes('ai-sdk')) {
                labels.add('ai');
              }
              
              // Dependencies
              if (path === 'package.json' || path === 'package-lock.json') {
                labels.add('dependencies');
              }
            }

            // Add size labels
            const totalChanges = files.reduce((sum, file) => sum + file.changes, 0);
            if (totalChanges > 500) {
              labels.add('size/XL');
            } else if (totalChanges > 100) {
              labels.add('size/L');
            } else if (totalChanges > 30) {
              labels.add('size/M');
            } else {
              labels.add('size/S');
            }

            // Apply labels
            if (labels.size > 0) {
              await github.rest.issues.addLabels({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.issue.number,
                labels: Array.from(labels),
              });
            }

  commit-validation:
    name: Commit Message Validation
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Validate commit messages
        run: |
          echo "🔍 Validating commit messages..."
          
          # Get commits in this PR
          git log --oneline origin/main..HEAD | while read commit; do
            message=$(echo "$commit" | cut -d' ' -f2-)
            
            # Check conventional commit format
            if [[ ! "$message" =~ ^(feat|fix|docs|style|refactor|perf|test|build|ci|chore|revert)(\(.+\))?: .{1,}$ ]]; then
              echo "❌ Invalid commit message format: $message"
              echo "Expected format: type(scope): description"
              echo "Valid types: feat, fix, docs, style, refactor, perf, test, build, ci, chore, revert"
              exit 1
            fi
          done
          
          echo "✅ All commit messages are valid"

  changes-summary:
    name: Changes Summary
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Generate changes summary
        uses: actions/github-script@v6
        with:
          script: |
            const { data: files } = await github.rest.pulls.listFiles({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.issue.number,
            });

            const summary = {
              total: files.length,
              additions: files.reduce((sum, file) => sum + file.additions, 0),
              deletions: files.reduce((sum, file) => sum + file.deletions, 0),
              byType: {}
            };

            files.forEach(file => {
              const ext = file.filename.split('.').pop() || 'other';
              summary.byType[ext] = (summary.byType[ext] || 0) + 1;
            });

            const comment = `## 📊 Changes Summary

            - **Files changed:** ${summary.total}
            - **Lines added:** +${summary.additions}
            - **Lines deleted:** -${summary.deletions}

            ### Files by type:
            ${Object.entries(summary.byType)
              .map(([type, count]) => `- \`.${type}\`: ${count} files`)
              .join('\n')}

            ---
            *Auto-generated by PR validation workflow*`;

            // Check if comment already exists
            const { data: comments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
            });

            const existingComment = comments.find(c => 
              c.body.includes('## 📊 Changes Summary')
            );

            if (existingComment) {
              await github.rest.issues.updateComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: existingComment.id,
                body: comment,
              });
            } else {
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.issue.number,
                body: comment,
              });
            }

  conflict-check:
    name: Merge Conflict Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Check for merge conflicts
        run: |
          git fetch origin main
          if ! git merge-tree $(git merge-base HEAD origin/main) HEAD origin/main | grep -q '^<<<<<< '; then
            echo "✅ No merge conflicts detected"
          else
            echo "❌ Merge conflicts detected"
            echo "Please resolve conflicts before merging"
            exit 1
          fi

  assignee-check:
    name: Assignee and Reviewer Check
    runs-on: ubuntu-latest
    steps:
      - name: Check PR assignee and reviewers
        uses: actions/github-script@v6
        with:
          script: |
            const pr = context.payload.pull_request;
            
            // Check if PR has assignee
            if (!pr.assignee && pr.assignees.length === 0) {
              core.warning('PR has no assignee. Consider assigning someone for accountability.');
            }
            
            // Check if PR has reviewers (for non-draft PRs)
            if (!pr.draft && pr.requested_reviewers.length === 0 && pr.requested_teams.length === 0) {
              core.warning('PR has no requested reviewers. Consider adding reviewers for code quality.');
            }
            
            // Check if PR is a draft
            if (pr.draft) {
              core.notice('This is a draft PR. Remove draft status when ready for review.');
            }