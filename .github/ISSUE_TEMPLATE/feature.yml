name: "✨ Feature Request"
description: "新機能 / 改善要望"
title: "[feat] <機能名>"
labels: ["feat"]
body:
  - type: markdown
    attributes:
      value: |
        ## 🎯 新機能要望
        
        LP Creator の新機能・改善に関するご要望をお聞かせください。
        開発チームが効率的に対応できるよう、詳細な情報をご記入ください。

  - type: textarea
    id: user_story
    attributes:
      label: ユーザーストーリー
      description: |
        As a **<ユーザータイプ>**,  
        I want **<具体機能>**  
        so that **<価値>**.
      placeholder: |
        As a **LP Creator ユーザー**,
        I want **ドラッグ&ドロップで画像をアップロード**
        so that **簡単にLP内の画像を変更できる**.
    validations:
      required: true

  - type: textarea
    id: current_problem
    attributes:
      label: 現在の問題・課題
      description: 現在どのような問題や不便さがありますか？
      placeholder: |
        - 画像アップロードにファイル選択ダイアログが必要
        - 複数画像の一括処理ができない
        - プレビューに時間がかかる
    validations:
      required: true

  - type: textarea
    id: proposed_solution
    attributes:
      label: 提案する解決策
      description: どのような機能・改善を希望しますか？
      placeholder: |
        - ドラッグ&ドロップインターフェース実装
        - 複数ファイル同時アップロード対応
        - リアルタイムプレビュー機能
    validations:
      required: true

  - type: textarea
    id: acceptance_criteria
    attributes:
      label: 受け入れ条件 (Definition of Done)
      description: この機能が完成したと判断する条件を箇条書きで記載
      placeholder: |
        - [ ] D&Dで画像ファイルがアップロードできる
        - [ ] アップロード進捗が表示される
        - [ ] プレビューエリアに即座に反映される
        - [ ] エラーハンドリングが適切に動作する
        - [ ] テストコードが作成・通過している
    validations:
      required: true

  - type: dropdown
    id: priority
    attributes:
      label: 優先度
      description: この機能の緊急度・重要度を選択してください
      options:
        - "High（今スプリント必須）"
        - "Medium（次スプリント候補）"
        - "Low（将来検討）"
    validations:
      required: true

  - type: dropdown
    id: estimated_time
    attributes:
      label: 見積もり時間
      description: 実装にかかる予想時間
      options:
        - "1-2時間（小さな修正）"
        - "3-4時間（通常機能）"
        - "5-8時間（中規模機能）"
        - "1-2日（大規模機能）"
        - "3日以上（Epic級）"
    validations:
      required: true

  - type: checkboxes
    id: related_components
    attributes:
      label: 関連コンポーネント
      description: この機能に関係する部分を選択してください（複数選択可）
      options:
        - label: "UI（フロントエンド・コンポーネント）"
        - label: "API（バックエンド・エンドポイント）"
        - label: "インフラ（CI/CD・デプロイ）"
        - label: "DX（開発者体験・ツール）"
        - label: "テスト（ユニット・E2E）"
        - label: "ドキュメント"

  - type: textarea
    id: additional_context
    attributes:
      label: 追加情報・参考資料
      description: |
        スクリーンショット、参考URL、類似サービスの例など
        （画像はドラッグ&ドロップで添付可能）
      placeholder: |
        - 参考URL: https://example.com
        - 類似機能: V0のファイルアップロード
        - デザイン案: （画像を添付）
    validations:
      required: false 