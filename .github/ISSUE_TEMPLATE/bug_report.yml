name: "🐛 Bug Report"
description: "バグ報告"
title: "[bug] <問題の概要>"
labels: ["bug"]
body:
  - type: markdown
    attributes:
      value: |
        ## 🐛 バグ報告
        
        LP Creator で発生した問題を報告してください。
        迅速な修正のため、詳細な情報をご提供ください。

  - type: textarea
    id: bug_summary
    attributes:
      label: バグの概要
      description: 発生している問題を簡潔に説明してください
      placeholder: |
        プレビューエリアで生成されたHTMLが正しく表示されない
    validations:
      required: true

  - type: textarea
    id: reproduce_steps
    attributes:
      label: 再現手順
      description: バグを再現するための具体的なステップを記載してください
      placeholder: |
        1. チャットパネルで「ボタンを作って」と入力
        2. 生成完了後、プレビューエリアを確認
        3. ボタンが表示されず、エラーメッセージが出る
        4. ブラウザのコンソールに以下のエラーが表示：
           `TypeError: Cannot read property 'innerHTML' of null`
    validations:
      required: true

  - type: textarea
    id: expected_behavior
    attributes:
      label: 期待される動作
      description: 本来どのように動作するべきかを説明してください
      placeholder: |
        - プレビューエリアに生成されたボタンが正しく表示される
        - ボタンはクリック可能で、適切なスタイルが適用されている
        - エラーは発生しない
    validations:
      required: true

  - type: textarea
    id: actual_behavior
    attributes:
      label: 実際の動作
      description: 実際に何が起こっているかを説明してください
      placeholder: |
        - プレビューエリアが空白のまま
        - ブラウザコンソールにJavaScriptエラーが表示
        - 生成されたHTMLコードは正常に見える
    validations:
      required: true

  - type: textarea
    id: error_messages
    attributes:
      label: エラーメッセージ・ログ
      description: |
        エラーメッセージ、コンソールログ、スタックトレースなど
        （コードブロック ```で囲んで記載）
      placeholder: |
        ```
        TypeError: Cannot read property 'innerHTML' of null
            at PreviewPane.updateContent (PreviewPane.tsx:45:23)
            at PreviewPane.componentDidUpdate (PreviewPane.tsx:32:18)
        ```
    validations:
      required: false

  - type: dropdown
    id: severity
    attributes:
      label: 深刻度
      description: このバグの影響度を選択してください
      options:
        - "Critical（アプリが使用不可）"
        - "High（主要機能が動作しない）"
        - "Medium（一部機能に影響）"
        - "Low（軽微な問題）"
    validations:
      required: true

  - type: dropdown
    id: frequency
    attributes:
      label: 発生頻度
      description: このバグはどの程度の頻度で発生しますか？
      options:
        - "常に（100%）"
        - "頻繁に（50%以上）"
        - "時々（10-50%）"
        - "稀に（10%未満）"
    validations:
      required: true

  - type: textarea
    id: environment
    attributes:
      label: 環境情報
      description: |
        バグが発生した環境の詳細を記載してください
      placeholder: |
        - OS: macOS 13.4 / Windows 11 / Ubuntu 22.04
        - ブラウザ: Chrome 120.0 / Firefox 119.0 / Safari 17.0
        - デバイス: Desktop / Tablet / Mobile
        - 画面解像度: 1920x1080
        - Node.js: v18.17.0
        - アプリバージョン: v0.1.0
    validations:
      required: true

  - type: textarea
    id: workaround
    attributes:
      label: 回避策（もしあれば）
      description: 一時的にこの問題を回避する方法があれば教えてください
      placeholder: |
        - ページをリロードすると正常に動作する
        - 異なるブラウザを使用すると問題が発生しない
        - 特定の操作順序を避けると回避できる
    validations:
      required: false

  - type: textarea
    id: additional_context
    attributes:
      label: 追加情報
      description: |
        スクリーンショット、動画、関連する他のIssueなど
        （ファイルはドラッグ&ドロップで添付可能）
      placeholder: |
        - スクリーンショット: （ファイルを添付）
        - 関連Issue: #123
        - 最近の変更: PRがマージされた後に発生開始
    validations:
      required: false

  - type: checkboxes
    id: preflight_checklist
    attributes:
      label: 事前確認チェックリスト
      description: 報告前に以下を確認してください
      options:
        - label: "既存のIssueで同じ問題が報告されていないことを確認した"
          required: true
        - label: "最新版での動作確認を行った"
          required: true
        - label: "ブラウザのキャッシュクリアを試した"
          required: true
        - label: "再現手順を複数回確認した"
          required: true 