# GitHub Labels Configuration for LP Creator
# This file defines the labels used by the auto-labeling workflow

# Type Labels
- name: "frontend"
  color: "0075ca"
  description: "Changes to frontend components, UI, or client-side code"

- name: "backend"
  color: "d73a4a"  
  description: "Changes to API routes, server-side logic, or Mastra agents"

- name: "ai"
  color: "7057ff"
  description: "Changes to AI integration, agents, or AI-related functionality"

- name: "documentation"
  color: "0052cc"
  description: "Changes to documentation, README files, or guides"

- name: "tests"
  color: "008672"
  description: "Changes to test files, test configuration, or testing utilities"

- name: "ci/cd"
  color: "1d76db"
  description: "Changes to CI/CD workflows, build configuration, or deployment"

- name: "dependencies"
  color: "0366d6"
  description: "Changes to package.json, dependencies, or package updates"

# Size Labels
- name: "size/XS"
  color: "3cbf00"
  description: "Very small change (1-10 lines)"

- name: "size/S"
  color: "5cbf00"
  description: "Small change (11-30 lines)"

- name: "size/M"
  color: "fbca04"
  description: "Medium change (31-100 lines)"

- name: "size/L"
  color: "f7b100"
  description: "Large change (101-500 lines)"

- name: "size/XL"
  color: "d93f0b"
  description: "Extra large change (500+ lines)"

# Priority Labels
- name: "priority/low"
  color: "d4c5f9"
  description: "Low priority issue or PR"

- name: "priority/medium"
  color: "b60205"
  description: "Medium priority issue or PR"

- name: "priority/high"
  color: "d93f0b"
  description: "High priority issue or PR"

- name: "priority/critical"
  color: "b60205"
  description: "Critical priority requiring immediate attention"

# Status Labels
- name: "good first issue"
  color: "7057ff"
  description: "Good for newcomers to the project"

- name: "help wanted"
  color: "008672"
  description: "Extra attention is needed"

- name: "question"
  color: "d876e3"
  description: "Further information is requested"

- name: "wontfix"
  color: "ffffff"
  description: "This will not be worked on"

- name: "duplicate"
  color: "cfd3d7"
  description: "This issue or pull request already exists"

- name: "invalid"
  color: "e4e669"
  description: "This doesn't seem right"

# Feature Labels
- name: "enhancement"
  color: "a2eeef"
  description: "New feature or request"

- name: "bug"
  color: "d73a4a"
  description: "Something isn't working"

- name: "security"
  color: "d73a4a"
  description: "Security-related issue or improvement"

- name: "performance"
  color: "ff6b6b"
  description: "Performance improvement or optimization"

- name: "refactor"
  color: "54473f"
  description: "Code refactoring without changing functionality"

# Review Labels
- name: "needs review"
  color: "fbca04"
  description: "This PR needs to be reviewed"

- name: "review approved"
  color: "0e8a16"
  description: "This PR has been approved for merge"

- name: "changes requested"
  color: "d93f0b" 
  description: "Changes have been requested on this PR"

- name: "ready to merge"
  color: "0e8a16"
  description: "This PR is ready to be merged"