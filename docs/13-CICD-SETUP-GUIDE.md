# CI/CD セットアップガイド

LP Creator プロジェクトの包括的な CI/CD パイプライン構築ガイドです。

## 📋 目次

1. [概要](#概要)
2. [前提条件](#前提条件)
3. [セットアップ手順](#セットアップ手順)
4. [ワークフロー説明](#ワークフロー説明)
5. [GitHub Secrets 設定](#github-secrets-設定)
6. [トラブルシューティング](#トラブルシューティング)
7. [運用ガイド](#運用ガイド)

## 🎯 概要

### 実装済み CI/CD パイプライン

- **CI (継続的インテグレーション)**: テスト・リント・ビルド検証
- **CD (継続的デプロイメント)**: 自動デプロイメント（ステージング・本番）
- **Code Quality**: セキュリティスキャン・カバレッジ測定・パフォーマンス監視
- **PR Validation**: プルリクエスト自動検証・ラベル付け

### アーキテクチャ図

```mermaid
graph TD
    A[Developer Push] --> B[GitHub Actions]
    B --> C[CI Pipeline]
    B --> D[Code Quality]
    B --> E[PR Validation]
    
    C --> F[Lint & TypeScript]
    C --> G[Unit Tests]
    C --> H[E2E Tests]
    C --> I[Build]
    
    D --> J[Security Scan]
    D --> K[Coverage Check]
    D --> L[Performance Audit]
    
    I --> M[Deploy Staging]
    M --> N[Health Check]
    N --> O[Deploy Production]
```

## 🛠️ 前提条件

### 必要なツール・サービス

- **Node.js 18+**: JavaScript ランタイム
- **npm**: パッケージマネージャー
- **GitHub リポジトリ**: Actions 有効
- **Vercel アカウント**: デプロイメント用
- **Snyk アカウント**: セキュリティスキャン用（オプション）

### プロジェクト要件

- Next.js 15+ アプリケーション
- TypeScript 設定済み
- Jest + Playwright テスト環境
- ESLint 設定済み

## 🚀 セットアップ手順

### 1. 自動セットアップ（推奨）

```bash
# セットアップスクリプト実行
chmod +x scripts/setup-ci.sh
./scripts/setup-ci.sh
```

### 2. 手動セットアップ

#### Step 1: ワークフローファイルの配置

```bash
# ワークフローディレクトリ作成
mkdir -p .github/workflows

# ワークフローファイルコピー
cp github-workflows/*.yml .github/workflows/
```

#### Step 2: 依存関係のインストール

```bash
# CI用パッケージインストール
npm install --save-dev wait-on @next/bundle-analyzer
```

#### Step 3: 設定ファイルの確認

必要な設定ファイルが存在することを確認：

- `jest.config.js` - Jest テスト設定
- `playwright.config.ts` - E2E テスト設定  
- `eslint.config.mjs` - ESLint ルール
- `lighthouse.config.js` - パフォーマンス設定
- `.env.example` - 環境変数テンプレート

### 3. 検証実行

```bash
# CI/CD設定の検証
chmod +x scripts/validate-ci.sh
./scripts/validate-ci.sh
```

## ⚙️ ワークフロー説明

### 1. CI ワークフロー (`ci.yml`)

**トリガー**: Push・PR（main・develop ブランチ）

**ジョブ構成**:

```yaml
jobs:
  setup: # 依存関係キャッシュ
  lint: # ESLint + TypeScript チェック
  unit-tests: # Jest テスト + カバレッジ
  e2e-tests: # Playwright E2E テスト
  build: # Next.js ビルド検証
  quality-gate: # 全体の品質ゲート
```

**品質ゲート条件**:
- ESLint エラーなし
- TypeScript 型エラーなし
- Jest テスト通過
- E2E テスト通過
- ビルド成功

### 2. CD ワークフロー (`cd.yml`)

**トリガー**: 
- main ブランチへの Push → ステージング
- Release 作成 → 本番

**デプロイフロー**:

```mermaid
graph LR
    A[main Push] --> B[Full Test Suite]
    B --> C[Deploy Staging]
    C --> D[Health Check]
    
    E[Release] --> F[E2E Tests]
    F --> G[Deploy Production]
    G --> H[Production Check]
```

### 3. Code Quality ワークフロー (`code-quality.yml`)

**機能**:
- **セキュリティスキャン**: Snyk + npm audit + CodeQL
- **カバレッジ測定**: Jest カバレッジ（70%閾値）
- **パフォーマンス監視**: Lighthouse CI
- **バンドルサイズ分析**: Next.js Bundle Analyzer

### 4. PR Validation ワークフロー (`pr-validation.yml`)

**自動化機能**:
- **コミットメッセージ検証**: Conventional Commits 形式
- **自動ラベル付け**: 変更ファイルに基づく
- **変更サマリー**: ファイル変更統計
- **コンフリクト検出**: マージ競合の事前検出

## 🔐 GitHub Secrets 設定

### 必須 Secrets

```bash
# AI API Keys
ANTHROPIC_API_KEY=your-anthropic-api-key
OPENAI_API_KEY=your-openai-api-key

# Vercel デプロイ
VERCEL_TOKEN=your-vercel-token
VERCEL_ORG_ID=your-vercel-org-id  
VERCEL_PROJECT_ID=your-vercel-project-id
```

### オプション Secrets

```bash
# セキュリティスキャン
SNYK_TOKEN=your-snyk-token

# コードカバレッジ
CODECOV_TOKEN=your-codecov-token
```

### 設定手順

1. GitHub リポジトリ → **Settings**
2. **Secrets and variables** → **Actions**  
3. **New repository secret** でシークレット追加

### Vercel 設定取得

```bash
# Vercel CLI インストール
npm i -g vercel

# プロジェクト情報取得
vercel link
vercel env pull .env.vercel
```

## 🧪 テスト実行

### ローカル CI 実行

```bash
# 全 CI チェック実行
npm run lint              # ESLint
npx tsc --noEmit         # TypeScript
npm run build:mastra     # Mastra TS
npm test                 # Jest テスト
npm run build            # ビルド

# E2E テスト
npm run test:e2e         # Playwright
```

### パッケージスクリプト追加

`package.json` に便利スクリプトを追加：

```json
{
  "scripts": {
    "ci:lint": "npm run lint && npx tsc --noEmit && npm run build:mastra",
    "ci:test": "npm test -- --coverage --watchAll=false",
    "ci:e2e": "npm run build && npm start & npx wait-on http://localhost:3000 && npm run test:e2e",
    "ci:all": "npm run ci:lint && npm run ci:test && npm run ci:e2e",
    "validate:ci": "./scripts/validate-ci.sh"
  }
}
```

## 🐛 トラブルシューティング

### よくある問題

#### 1. Playwright ブラウザエラー

```bash
# ブラウザ再インストール
npx playwright install --with-deps
```

#### 2. TypeScript エラー

```bash
# 型定義更新
npm update @types/node @types/react @types/react-dom
```

#### 3. E2E テストタイムアウト

`playwright.config.ts` の設定調整：

```typescript
export default defineConfig({
  timeout: 60000, // 60秒に延長
  retries: 2,     // リトライ回数増加
});
```

#### 4. Jest メモリエラー

```bash
# メモリ制限解除
node --max-old-space-size=4096 node_modules/.bin/jest
```

### デバッグコマンド

```bash
# 詳細ログ出力
DEBUG=* npm run test:e2e

# GitHub Actions ローカル実行
act -j ci  # act CLI 使用

# Lighthouse デバッグ
lighthouse http://localhost:3000 --view
```

## 📊 運用ガイド

### モニタリングポイント

#### 1. CI メトリクス
- **ビルド時間**: 5分以内を目標
- **テスト成功率**: 95%以上
- **カバレッジ**: 70%以上維持

#### 2. デプロイメトリクス  
- **デプロイ頻度**: 日次デプロイ
- **平均復旧時間**: 30分以内
- **変更失敗率**: 5%以下

#### 3. パフォーマンスメトリクス
- **Lighthouse スコア**: 80点以上
- **バンドルサイズ**: 500KB以下
- **LCP**: 2.5秒以下

### アラート設定

#### GitHub Actions 通知

```yaml
# Slack 通知例
- name: Notify Slack
  if: failure()
  uses: 8398a7/action-slack@v3
  with:
    status: failure
    webhook_url: ${{ secrets.SLACK_WEBHOOK }}
```

#### Vercel デプロイ通知

Vercel 管理画面で：
1. **Settings** → **Git**
2. **Deploy Hooks** 設定
3. **Slack Integration** 有効化

### 定期メンテナンス

#### 週次タスク
- [ ] 依存関係脆弱性チェック
- [ ] テストカバレッジレビュー  
- [ ] パフォーマンスメトリクス確認

#### 月次タスク
- [ ] ワークフロー最適化
- [ ] 依存関係アップデート
- [ ] セキュリティ設定見直し

### セキュリティベストプラクティス

#### 1. シークレット管理
- 定期的なキーローテーション
- 最小権限原則の適用
- 環境別シークレット分離

#### 2. アクセス制御
- ブランチプロテクションルール
- 必須レビュー設定
- 管理者のみのマージ権限

#### 3. 監査ログ
- Actions 実行履歴の定期確認
- 異常なアクセスパターンの監視

## 📚 参考リンク

### 公式ドキュメント
- [GitHub Actions](https://docs.github.com/en/actions)
- [Vercel Deployment](https://vercel.com/docs/deployments)
- [Next.js CI/CD](https://nextjs.org/docs/deployment)

### ツール・サービス
- [Snyk Security](https://snyk.io/docs/)
- [Codecov](https://docs.codecov.com/)
- [Lighthouse CI](https://github.com/GoogleChrome/lighthouse-ci)

### 関連ドキュメント
- [04-DEVELOPMENT-GUIDE.md](./04-DEVELOPMENT-GUIDE.md) - 開発環境構築
- [06-SECURITY-CHECKLIST.md](./06-SECURITY-CHECKLIST.md) - セキュリティ要件
- [11-IMPLEMENTATION-STATUS-ANALYSIS.md](./11-IMPLEMENTATION-STATUS-ANALYSIS.md) - 実装状況

---

**🎯 このガイドで LP Creator の CI/CD 環境が完全に構築できます。**
**質問や追加要件があれば、プロジェクトチームまでお知らせください。**