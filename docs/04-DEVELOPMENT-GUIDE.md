# 🛠️ 開発・運用ガイド

## 📋 概要

LP Creatorの開発環境構築、開発フロー、デプロイメント、運用方法を説明します。

## 🚀 クイックスタート

### 1. 環境要件
```bash
Node.js: 18.17.0 以上
npm: 9.0.0 以上
```

### 2. リポジトリクローン
```bash
git clone https://github.com/your-username/lp-creator.git
cd lp-creator
```

### 3. 依存関係インストール
```bash
npm install
```

### 4. 環境変数設定
```bash
cp .env.example .env.local
```

#### 必要な環境変数
```bash
# AI SDK設定
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# Mastra設定
ENABLE_MASTRA=true
DATABASE_URL=file:./.mastra/memory.db
MASTRA_LOG_LEVEL=debug

# Next.js設定
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 5. 開発サーバー起動
```bash
# メイン開発サーバー
npm run dev

# Mastra開発サーバー（別ターミナル）
npm run dev:mastra
```

### 6. ブラウザアクセス
- **メインアプリ**: http://localhost:3000
- **Claude版**: http://localhost:3000/claude

## 📦 npm Scripts

```json
{
  "dev": "next dev",                    // 開発サーバー起動
  "dev:mastra": "mastra dev --dir mastra", // Mastra開発
  "build": "next build",                // 本番ビルド
  "build:mastra": "cd mastra && npx tsc --noEmit", // Mastra型チェック
  "start": "next start",                // 本番サーバー起動
  "lint": "next lint",                  // ESLint実行
  "test": "jest",                       // 単体テスト
  "test:watch": "jest --watch",         // テスト監視
  "test:e2e": "playwright test",        // E2Eテスト
  "test:e2e:ui": "playwright test --ui", // E2EテストUI
  "test:e2e:headed": "playwright test --headed" // E2Eヘッドモード
}
```

## 🏗️ 開発フロー

### 1. 機能開発
```bash
# フィーチャーブランチ作成
git checkout -b feature/統合LP生成機能改善

# 開発サーバー起動
npm run dev

# 変更をコミット
git add .
git commit -m "feat: 統合LP生成機能のパフォーマンス改善"

# プッシュ
git push origin feature/統合LP生成機能改善
```

### 2. テスト実行
```bash
# 型チェック
npm run build:mastra

# リント
npm run lint

# 単体テスト
npm run test

# E2Eテスト
npm run test:e2e
```

### 3. プルリクエスト
- GitHub UIでPR作成
- CI/CDパイプライン実行確認
- コードレビュー
- マージ

## 🧪 テスト戦略

### 単体テスト (Jest + React Testing Library)

#### テストファイル構成
```
tests/
├── components/           # コンポーネントテスト
│   ├── UnifiedLPViewer.test.tsx
│   ├── ChatPanel.test.tsx
│   └── EditableText.test.tsx
├── hooks/               # カスタムフックテスト
│   └── useEditMode.test.tsx
├── utils/               # ユーティリティテスト
│   └── api.test.ts
└── setup.ts             # テストセットアップ
```

#### 実行方法
```bash
# 全テスト実行
npm run test

# 監視モード
npm run test:watch

# カバレッジ表示
npm run test -- --coverage
```

### E2Eテスト (Playwright)

#### テストシナリオ
```typescript
// tests/e2e/lp-generation.spec.ts
import { test, expect } from '@playwright/test';

test('統合LP生成フロー', async ({ page }) => {
  await page.goto('/');
  
  // チャット入力
  await page.fill('[placeholder*="メッセージを入力"]', 'SaaSツールのLPを作って');
  await page.click('button[type="submit"]');
  
  // LP生成完了まで待機
  await expect(page.locator('[data-testid="unified-lp-viewer"]')).toBeVisible();
  
  // ダウンロードボタン確認
  await expect(page.locator('button:has-text("ダウンロード")')).toBeVisible();
});
```

#### 実行方法
```bash
# E2Eテスト実行
npm run test:e2e

# UIモード
npm run test:e2e:ui

# ヘッドモード（ブラウザ表示）
npm run test:e2e:headed
```

## 🔧 コード品質管理

### ESLint設定
```javascript
// eslint.config.mjs
export default [
  {
    files: ['**/*.{js,mjs,cjs,ts,jsx,tsx}'],
    rules: {
      '@typescript-eslint/no-unused-vars': 'error',
      'react-hooks/exhaustive-deps': 'warn',
      'prefer-const': 'error'
    }
  }
];
```

### TypeScript設定
```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "ES6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [{ "name": "next" }],
    "paths": {
      "@/*": ["./*"]
    }
  }
}
```

### Prettier設定
```json
// .prettierrc
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}
```

## 🚀 デプロイメント

### Vercel デプロイ

#### 1. プロジェクト設定
```bash
# Vercel CLI インストール
npm i -g vercel

# プロジェクト初期化
vercel

# 環境変数設定
vercel env add OPENAI_API_KEY
vercel env add ANTHROPIC_API_KEY
```

#### 2. 自動デプロイ
- `main` ブランチへのプッシュで自動デプロイ
- プレビューデプロイ: PRごとに自動作成
- ドメイン設定: Vercelダッシュボードで設定

### Docker デプロイ

#### Dockerfile
```dockerfile
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app
ENV NODE_ENV production
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
USER nextjs
EXPOSE 3000
ENV PORT 3000
CMD ["node", "server.js"]
```

#### ビルド・実行
```bash
# Dockerイメージビルド
docker build -t lp-creator .

# コンテナ実行
docker run -p 3000:3000 --env-file .env.local lp-creator
```

## 📊 監視・ログ

### アプリケーション監視

#### パフォーマンス監視
```typescript
// utils/performance.ts
export const measurePerformance = (name: string, fn: () => Promise<any>) => {
  return async (...args: any[]) => {
    const start = performance.now();
    const result = await fn(...args);
    const end = performance.now();
    
    console.log(`${name}: ${end - start}ms`);
    return result;
  };
};
```

#### エラー監視
```typescript
// utils/error-tracking.ts
export const trackError = (error: Error, context?: Record<string, any>) => {
  console.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    context,
    timestamp: new Date().toISOString()
  });
  
  // 本番環境では外部サービスに送信
  if (process.env.NODE_ENV === 'production') {
    // Sentry, DataDog等への送信
  }
};
```

### ログ管理
```typescript
// utils/logger.ts
export const logger = {
  info: (message: string, data?: any) => {
    console.log(`[INFO] ${message}`, data);
  },
  warn: (message: string, data?: any) => {
    console.warn(`[WARN] ${message}`, data);
  },
  error: (message: string, error?: Error) => {
    console.error(`[ERROR] ${message}`, error);
  }
};
```

## 🔒 セキュリティ

### 環境変数管理
```bash
# 本番環境変数（vercel env add で設定）
OPENAI_API_KEY=sk-...
ANTHROPIC_API_KEY=sk-ant-...
DATABASE_URL=postgresql://...
NEXTAUTH_SECRET=...
```

### セキュリティヘッダー
```typescript
// next.config.ts
const nextConfig = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-eval'; style-src 'self' 'unsafe-inline';"
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          }
        ]
      }
    ];
  }
};
```

## 🔄 CI/CD

### GitHub Actions

#### ワークフロー設定
```yaml
# .github/workflows/ci.yml
name: CI
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run lint
      - run: npm run test
      - run: npm run build
      
      - name: E2E Tests
        run: |
          npm run build
          npm run start &
          npx wait-on http://localhost:3000
          npm run test:e2e
```

### 品質ゲート
- **型チェック**: TypeScript エラーゼロ
- **リント**: ESLint エラーゼロ
- **テスト**: 単体テスト通過率100%
- **ビルド**: Next.js ビルド成功

---

最終更新: 2024年12月
対応環境: Node.js 18+, Next.js 15, React 19 