# 📊 プロジェクト管理ガイド ― GitHub Project + Issue 管理

## 📋 概要

LP Creator の開発を **GitHub Project（Kanban）+ Issue** で効率的に管理するためのガイドです。
**3スプリント（3週間）でMVP完成**を目標とし、各Issueは **0.5〜1開発日** で完了できる粒度に分解しています。

## 🔖 GitHub Project ボード構成

| Column              | 説明                         | 使用タイミング                    |
| ------------------- | -------------------------- | -------------------------- |
| **Backlog**         | 全Issue初期投入、Milestone未設定     | プロジェクト開始時に一括作成             |
| **Sprint n – Todo** | 今スプリントの未着手                 | スプリント計画時に移動               |
| **In Progress**     | 作業中（PR WIP）                | 開発開始時に移動                   |
| **Review / Test**   | PR Ready・テスト実行             | PR作成時に移動                   |
| **Done**            | main マージ＆CI通過             | マージ完了時に自動移動               |

### 推奨ラベル体系
- **種類**: `feat`, `bug`, `chore`, `test`, `docs`, `design`, `epic`
- **プロバイダー**: `provider-claude`, `provider-gemini`, `provider-openai`
- **優先度**: `priority-high`, `priority-medium`, `priority-low`
- **コンポーネント**: `ui`, `api`, `infra`, `dx`
- **新機能**: `letter-creation`, `ai-modification`, `image-insertion`, `hosting`

## 🌱 Sprint 0 ― 環境 & 基盤（7 Issue）

### 目標: 開発環境とCI/CD基盤の完成

| #   | タイトル                                   | ラベル                     | 目安時間 | 受け入れ条件 (TDD)                                      |
| --- | -------------------------------------- | ----------------------- | ---- | ------------------------------------------------- |
| 0-1 | **\[epic] Project Bootstrap**          | `epic, chore`           | 4h   | Repo に Next.js 15 + TypeScript が起動し、`npm test` が緑 |
| 0-2 | **ESLint / Prettier / Husky** 強化      | `chore, dx`             | 2h   | 1つでもLint Errorがあると CI失敗                        |
| 0-3 | **Jest + RTL** テスト環境拡充               | `test, chore`           | 3h   | `npm run test:ci` でサンプルテストが通る                   |
| 0-4 | **GitHub Actions CI** 完全版              | `chore, infra`          | 4h   | push時に *lint → test → build* が自動実行              |
| 0-5 | **Vercel Preview Deploy** Hook         | `chore, infra`          | 2h   | `main` push で Vercel Preview URL が PR にコメント      |
| 0-6 | **AI Provider Interface** 設計           | `feat, api`             | 4h   | `src/llm/provider.ts` に共通 ILLM インターフェイス定義 & テスト  |
| 0-7 | **Claude Provider** stub 実装            | `feat, provider-claude` | 3h   | `ClaudeProvider.complete()` が mock 返信し、ユニットテスト通過 |

**📝 Sprint 0 完了条件**: 全てのCI/CDが緑、テストカバレッジ80%以上

## 🌟 Sprint 1 ― コア体験縦串（9 Issue）

### 目標: 「左チャット × 右プレビュー」の基本動作完成

| #   | タイトル                                 | ラベル                     | 目安時間 | 受け入れ条件 (TDD)                                     |
| --- | ------------------------------------ | ----------------------- | ---- | ------------------------------------------------ |
| 1-1 | **ChatPanel UI** V0風リニューアル            | `feat, design, ui`      | 6h   | ユーザー入力→`onSubmit` が発火する RTL テスト                 |
| 1-2 | **/api/generate** Edge Route 新規作成    | `feat, api`             | 4h   | POSTに product 情報を渡すと **200 + `{html,css}`** を返す |
| 1-3 | **Prompt Schema** & バリデーション          | `test, feat`            | 3h   | 不正JSON入力に400返却、Zodスキーマ定義                       |
| 1-4 | **PreviewPane** with iframe `srcdoc` | `feat, design, ui`      | 5h   | 新しい `srcdoc` が流れ込むとDOM更新されるJestテスト             |
| 1-5 | **react-split-pane** 幅調整 導入          | `feat, design, ui`      | 3h   | ドラッグでmin 200px を下回らない e2e                      |
| 1-6 | **Full-Screen / New-Tab** ボタン        | `feat, ui`              | 2h   | クリックで `/preview/[id]` が開き、200を返す               |
| 1-7 | **Claude streaming** 集約              | `feat, provider-claude` | 4h   | LLMが3 Chunk以上で届き、最終DOMと一致                      |
| 1-8 | **Gemini Provider** behind flag      | `feat, provider-gemini` | 5h   | `.env NEXT_PUBLIC_LLM=gemini` で provider切替テスト通過  |
| 1-9 | **E2E Smoke** (Playwright)           | `test, e2e`             | 4h   | 商品名入力 → LP表示まで自動で確認                            |

**📝 Sprint 1 完了条件**: エンドツーエンドでLP生成〜プレビューが動作

## 🛠️ Sprint 2 ― 編集 & 保存（9 Issue）

### 目標: 編集機能とクラウド保存の実装

| #   | タイトル                           | ラベル          | 目安時間 | 受け入れ条件 (TDD)                                         |
| --- | ------------------------------ | ------------ | ---- | ---------------------------------------------------- |
| 2-1 | **Image Upload** (UploadThing) | `feat, ui`   | 6h   | D&Dで画像URLが `img` タグに入る                             |
| 2-2 | **Tiptap WYSIWYG** 導入          | `feat, ui`   | 8h   | クリック編集→保存でHTML反映                                   |
| 2-3 | **Undo / Redo** ヒストリ機能        | `feat, ui`   | 4h   | Ctrl+Z / Ctrl+Y でDOM巻き戻しテスト                        |
| 2-4 | **KV Save API** (`/api/save`)  | `feat, api`  | 5h   | POST保存→ GET `/preview/[id]` で同内容取得                  |
| 2-5 | **Preview Route** ページ          | `feat, api`  | 4h   | `/preview/[id]` SSR → 正しいHTML/CSS出力                |
| 2-6 | **Metadata JSON** 保存           | `feat, api`  | 3h   | KVに `{id, title, createdAt}` レコード作成                 |
| 2-7 | **CI – Codecov** 連携            | `chore, dx`  | 2h   | PRにカバレッジ%が表示                                      |
| 2-8 | **Accessibility Audit** (axe)  | `feat, test` | 3h   | `npm run test:a11y` 緑                                |
| 2-9 | **README + Storybook** 初版     | `docs, dx`   | 4h   | `npm run storybook` 起動、主要コンポーネントdocs表示           |

**📝 Sprint 2 完了条件**: 生成→編集→保存→共有のフル機能動作

## ✨ Sprint 3 ― 高度機能 & UX改善（新規追加）

### 目標: レター作成、AI対話修正、マウスUI編集の実装

| #   | タイトル                                       | ラベル                              | 目安時間 | 受け入れ条件 (TDD)                                          |
| --- | ------------------------------------------ | -------------------------------- | ---- | ----------------------------------------------------- |
| 3-1 | **\[epic] レター文章作成フロー**                      | `epic, letter-creation`          | 12h  | ヒアリング→レター生成→承認→LP作成の完全フロー                            |
| 3-2 | **ビジネスヒアリングフォーム** 設計                        | `feat, ui, letter-creation`      | 4h   | 商品/サービス情報入力フォーム、バリデーション                              |
| 3-3 | **レター文章生成API** (`/api/letter/generate`) | `feat, api, letter-creation`     | 6h   | ヒアリング情報→コンセプト提案文章生成                                 |
| 3-4 | **レター承認UI** (OK/修正ボタン)                     | `feat, ui, letter-creation`      | 3h   | レター表示→承認→LP生成開始フロー                                   |
| 3-5 | **\[epic] AI対話修正機能**                        | `epic, ai-modification`          | 16h  | 完成LP→対話修正→リアルタイム反映の完全実装                             |
| 3-6 | **修正チャットパネル** 実装                           | `feat, ui, ai-modification`      | 5h   | LP表示状態での修正専用チャット、差分表示                              |
| 3-7 | **差分適用API** (`/api/modify`)               | `feat, api, ai-modification`     | 6h   | 修正指示→HTML/CSS差分適用→プレビュー更新                           |
| 3-8 | **修正履歴管理** システム                            | `feat, api, ai-modification`     | 5h   | 修正ステップ保存、Undo/Redo対応                                |
| 3-9 | **\[epic] ノーコード画像挿入**                       | `epic, image-insertion`          | 10h  | マウスUI画像挿入、D&D、リサイズ機能                               |
| 3-10| **画像ドロップゾーン** UI                           | `feat, ui, image-insertion`      | 4h   | LP上への画像D&D、プレースホルダー表示                              |
| 3-11| **画像操作ツールバー** 実装                          | `feat, ui, image-insertion`      | 6h   | リサイズ、位置調整、削除ボタン、マウス操作                              |

**📝 Sprint 3 完了条件**: レター→LP生成→AI修正→画像挿入の完全フロー動作

## 🌊 Sprint 4 ― 本格運用機能（将来実装）

### 目標: ホスティング、コンセプト理論、エンタープライズ機能

| #   | タイトル                                      | ラベル                     | 目安時間 | 受け入れ条件 (TDD)                             |
| --- | ----------------------------------------- | ----------------------- | ---- | ---------------------------------------- |
| 4-1 | **\[epic] ツール内ホスティング**                    | `epic, hosting`         | 20h  | LP公開→独自URL生成→即アクセス可能                    |
| 4-2 | **公開設定UI** (パブリック/プライベート)                | `feat, ui, hosting`     | 4h   | 公開範囲設定、パスワード保護オプション                    |
| 4-3 | **独自ドメイン対応** システム                        | `feat, api, hosting`    | 8h   | カスタムドメイン設定、DNS管理                       |
| 4-4 | **アクセス解析** ダッシュボード                       | `feat, ui, hosting`     | 8h   | PV、CV、デバイス別統計表示                        |
| 4-5 | **\[epic] 複数コンセプト提案**                     | `epic, letter-creation` | 16h  | オセロ理論、逆張り理論に基づく多角度コンセプト生成             |
| 4-6 | **JPRO向けコンセプト理論** 統合                   | `feat, api`             | 12h  | 専門的コンセプト生成、差別化戦略提案                     |
| 4-7 | **チーム管理機能** (Auth + RLS)                | `feat, api`             | 16h  | 組織アカウント、権限管理、コラボレーション                  |
| 4-8 | **エンタープライズAPI** 料金制限                    | `feat, api`             | 8h   | 使用量ベース課金、クレジット制限、API Rate Limiting |

## 🚧 今後のバックログ（参考）

### Phase 3: 高度な機能
- 🌐 **i18n / 日本語UI** - 多言語対応
- 🧩 **Section Planner Agent** - Parallel-Chain構成
- 🎨 **テーマ切替（Light/Dark）** - デザインシステム
- ⌛ **Loading Skeleton / Progress Bar** - UX改善

### Phase 4: エンタープライズ機能
- 👥 **チームコラボ（Auth + RLS）** - 権限管理
- 🔗 **1クリック Vercel Production Deploy** - 本番デプロイ
- 📈 **A/B テスト自動生成** - Growth Loop
- 📊 **アナリティクス統合** - パフォーマンス計測

## 🎫 Issue テンプレート

### Feature Request Template
```yaml
name: "✨ Feature"
description: "新機能 / 改善要望"
title: "[feat] <機能名>"
labels: ["feat"]
body:
  - type: markdown
    attributes:
      value: |
        ### ユーザーストーリー
        As a **<ユーザータイプ>**,  
        I want **<具体機能>**  
        so that **<価値>**.

  - type: textarea
    id: ac
    attributes:
      label: 受け入れ条件 (Definition of Done)
      description: テスト観点、スクリーンショットなど
    validations:
      required: true

  - type: dropdown
    id: priority
    attributes:
      label: 優先度
      options:
        - High（今スプリント必須）
        - Medium（次スプリント候補）
        - Low（将来検討）
    validations:
      required: true
```

### Bug Report Template
```yaml
name: "🐛 Bug Report"
description: "バグ報告"
title: "[bug] <問題の概要>"
labels: ["bug"]
body:
  - type: textarea
    id: reproduce
    attributes:
      label: 再現手順
      description: バグを再現するためのステップ
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: 期待される動作
    validations:
      required: true

  - type: textarea
    id: actual
    attributes:
      label: 実際の動作
    validations:
      required: true
```

## 🏃‍♂️ 開発運用 Tips

### 1. TDD開発サイクル
```mermaid
graph LR
    A[Red] --> B[Green] --> C[Refactor] --> A
    A --> D[テスト失敗]
    B --> E[最小実装]
    C --> F[リファクタリング]
```

- **Red**: 失敗するテストを書く
- **Green**: テストを通す最小実装
- **Refactor**: コード品質向上

### 2. アジャイルイベント
- **Sprint Planning** (30分): Issue優先度決定
- **Daily Stand-up** (10分): 進捗共有・ブロッカー解決
- **Sprint Review** (30分): デモ・レトロスペクティブ

### 3. Definition of Ready
- ✅ **Acceptance Criteria 明記済み**
- ✅ **見積もり完了（1-8ポイント）**
- ✅ **デザインリンク添付（必要に応じて）**
- ✅ **依存関係特定済み**

### 4. Definition of Done
- ✅ **ユニットテスト作成・通過**
- ✅ **E2Eテスト作成・通過（必要に応じて）**
- ✅ **コードレビュー完了**
- ✅ **CI/CD通過**
- ✅ **ドキュメント更新**

## 🎯 実装チェックリスト

### GitHub CLI でのIssue一括作成コマンド例
```bash
# Sprint 0 Issues
gh issue create --title "[epic] Project Bootstrap" --label "epic,chore" --milestone "sprint-0"
gh issue create --title "ESLint / Prettier / Husky 強化" --label "chore,dx" --milestone "sprint-0"
gh issue create --title "Jest + RTL テスト環境拡充" --label "test,chore" --milestone "sprint-0"
# ... 続く
```

### プロジェクトボード作成手順
1. **GitHub Repository** → **Projects** → **New project**
2. **Board view** を選択
3. **Columns** 作成: Backlog, Sprint 0 Todo, In Progress, Review/Test, Done
4. **Milestones** 作成: `sprint-0`, `sprint-1`, `sprint-2`, `sprint-3`, `sprint-4`
5. **Labels** 設定: 上記ラベル体系に従って作成

---

**最終更新**: 2024年12月  
**関連ドキュメント**: [09-V0-UIUX-SPECIFICATION.md](./09-V0-UIUX-SPECIFICATION.md), [04-DEVELOPMENT-GUIDE.md](./04-DEVELOPMENT-GUIDE.md) 