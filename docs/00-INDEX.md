# 📚 LP Creator - ドキュメントインデックス

## 🎯 プロジェクト概要

**LP Creator**は、V0/Claude Artifacts風の統合LP生成機能を持つNext.js + Mastra AIアプリケーションです。
従来の複雑なセクション別ツールを統合し、**1つのツールで完全なランディングページを一気に生成**できます。

## 📋 ドキュメント一覧

### 📖 コア仕様書
- **[01-ARCHITECTURE.md](./01-ARCHITECTURE.md)** - 現在のシステム構成とアーキテクチャ
- **[02-UNIFIED-LP-GENERATOR.md](./02-UNIFIED-LP-GENERATOR.md)** - 統合LP生成ツールの仕様
- **[03-COMPONENT-GUIDE.md](./03-COMPONENT-GUIDE.md)** - 主要コンポーネントガイド
- **[09-V0-UIUX-SPECIFICATION.md](./09-V0-UIUX-SPECIFICATION.md)** - V0風UI/UX「左チャット×右プレビュー」仕様

### 🔧 開発・運用ガイド
- **[04-DEVELOPMENT-GUIDE.md](./04-DEVELOPMENT-GUIDE.md)** - 開発環境構築と運用方法
- **[05-MASTRA-INTEGRATION.md](./05-MASTRA-INTEGRATION.md)** - Mastra統合の現状と今後の計画
- **[06-SECURITY-CHECKLIST.md](./06-SECURITY-CHECKLIST.md)** - セキュリティチェックリスト
- **[10-PROJECT-MANAGEMENT-GUIDE.md](./10-PROJECT-MANAGEMENT-GUIDE.md)** - GitHub Project + Issue管理ガイド
- **[11-IMPLEMENTATION-STATUS-ANALYSIS.md](./11-IMPLEMENTATION-STATUS-ANALYSIS.md)** - 実装状況分析と修正事項

### 🗂️ 参考資料
- **[07-LEGACY-FEATURES.md](./07-LEGACY-FEATURES.md)** - 削除された従来機能の記録
- **[08-MIGRATION-HISTORY.md](./08-MIGRATION-HISTORY.md)** - システム統合履歴

### 🎨 UI/UX・機能仕様
- **[13-NEW-FEATURES-SPECIFICATION.md](./13-NEW-FEATURES-SPECIFICATION.md)** - 新機能仕様書（レター作成・AI修正・画像挿入・ホスティング）

## 🚀 クイックスタート

1. **開発環境起動**
   ```bash
   npm run dev
   ```

2. **基本的な使用方法**
   - 「SaaSツールの完全なLPを作って」と入力
   - デザインスタイルとカラーを指定
   - 生成されたHTMLをダウンロード

3. **詳細情報**
   - アーキテクチャ: [01-ARCHITECTURE.md](./01-ARCHITECTURE.md)
   - UI/UX仕様: [09-V0-UIUX-SPECIFICATION.md](./09-V0-UIUX-SPECIFICATION.md)
   - 開発ガイド: [04-DEVELOPMENT-GUIDE.md](./04-DEVELOPMENT-GUIDE.md)

## 📊 プロジェクト状況

### ✅ 完了項目
- 統合LP生成ツール実装
- V0/Claude Artifacts風UI
- 従来ツールの統合・削除
- ドキュメント整理

### 🔄 進行中
- Mastra統合の改善
- パフォーマンス最適化

### 📅 今後の予定
- 高度な学習機能
- プロジェクト永続化
- A/Bテストシステム

## 🎨 プロジェクトの特徴

- **🌟 シンプル**: 1つのツールで完全LP生成
- **🎯 高品質**: V0レベルのコード出力
- **⚡ 高速**: Next.js 15 + React 19
- **🤖 AI駆動**: OpenAI GPT-4 + Claude 3.5 Sonnet
- **📱 レスポンシブ**: モバイル完全対応

---

最終更新: 2024年12月
プロジェクト管理者: AI Development Team 