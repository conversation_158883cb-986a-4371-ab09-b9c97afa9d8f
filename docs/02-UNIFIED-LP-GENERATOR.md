# 🚀 統合LP生成ツール - 仕様書

## 📋 概要

`generateUnifiedLP`は、V0/Claude Artifacts風の完全なランディングページを一気に生成する統合ツールです。
従来の20個以上の個別ツールを統合し、1つのツールで完全なHTML/CSS/JSコードを出力します。

## 🎯 設計思想

### V0/Claude Artifacts風の体験
- **自然言語入力**: 複雑な指示も理解
- **完全コード出力**: HTML/CSS/JS一式生成
- **即座にプレビュー**: iframe で完全表示
- **ダウンロード可能**: 完成品をすぐ使用

### 従来システムからの進化
```
Before: 20+ツール → セクション別 → 手動組み合わせ → 複雑
After:  1ツール → 完全LP → 即座プレビュー → シンプル
```

## 🔧 技術仕様

### 入力パラメータ

```typescript
interface UnifiedLPParams {
  userRequest: string;        // 自然言語要求
  productName: string;        // 商品・サービス名
  targetAudience: string;     // ターゲット層
  keyValue: string;          // 主要価値提案
  style: 'modern' | 'corporate' | 'startup' | 'creative' | 'minimal' | 'bold';
  colorScheme: 'blue' | 'green' | 'purple' | 'orange' | 'dark' | 'gradient';
  sections: string[];        // 含めるセクション
}
```

### 出力データ

```typescript
interface UnifiedLPResult {
  html: string;              // 完全なHTMLコード
  productName: string;       // 商品名
  style: string;            // デザインスタイル
  colorScheme: string;      // カラースキーム
  sections: string[];       // 生成されたセクション
  userRequest: string;      // 元のリクエスト
}
```

## 🎨 デザインシステム

### 6つのスタイル

#### 1. **modern**
- **特徴**: 洗練されたミニマルデザイン
- **適用**: SaaS、テック系スタートアップ
- **要素**: シャープなエッジ、大きなタイポグラフィ

#### 2. **corporate**
- **特徴**: 信頼性重視のビジネスデザイン
- **適用**: B2B企業、コンサルティング
- **要素**: 伝統的レイアウト、安定感

#### 3. **startup**
- **特徴**: エネルギッシュで革新的
- **適用**: 新興企業、プロダクトローンチ
- **要素**: 動的要素、鮮やかな色彩

#### 4. **creative**
- **特徴**: アーティスティックで個性的
- **適用**: デザイン会社、クリエイティブ業界
- **要素**: 非対称レイアウト、独創的要素

#### 5. **minimal**
- **特徴**: 極限まで削ぎ落としたデザイン
- **適用**: 高級ブランド、ミニマリスト向け
- **要素**: 余白重視、タイポグラフィ中心

#### 6. **bold**
- **特徴**: インパクト重視の大胆デザイン
- **適用**: エンターテイメント、若年層向け
- **要素**: 大きなビジュアル、強いコントラスト

### 6つのカラースキーム

```css
/* blue */
primary: #2563eb, secondary: #1e40af, accent: #3b82f6

/* green */
primary: #059669, secondary: #047857, accent: #10b981

/* purple */
primary: #7c3aed, secondary: #6d28d9, accent: #8b5cf6

/* orange */
primary: #ea580c, secondary: #c2410c, accent: #f97316

/* dark */
primary: #1f2937, secondary: #111827, accent: #374151

/* gradient */
primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%)
```

## 📑 セクション構成

### 8種類のセクション

1. **hero** - ヒーローセクション
   - メインビジュアル、キャッチコピー、CTA

2. **problem** - 問題提起
   - ターゲットの課題、ペインポイント

3. **solution** - ソリューション
   - 解決策の提示、独自性アピール

4. **features** - 機能・特徴
   - 主要機能、メリット一覧

5. **testimonials** - お客様の声
   - 推薦文、実績、信頼性向上

6. **pricing** - 料金プラン
   - プラン比較、価格設定

7. **faq** - よくある質問
   - 疑問解消、不安解決

8. **contact** - お問い合わせ
   - CTA、フォーム、連絡先

## 🔄 生成フロー

### 1. 入力解析
```
自然言語 → キーワード抽出 → 構造化データ変換
```

### 2. コンテンツ生成
```
構造化データ → AI生成 → セクション別コンテンツ
```

### 3. デザイン適用
```
コンテンツ → スタイル適用 → レスポンシブCSS生成
```

### 4. HTML出力
```
CSS + コンテンツ → 完全HTML → プレビュー表示
```

## 💡 使用例

### 基本例
```
入力: 「SaaSツールの完全なLPを作って」
出力: modernスタイル + blueカラー + 全セクション
```

### 詳細指定例
```
入力: 「フィットネスアプリの緑色でミニマルなLPを作成」
出力: minimalスタイル + greenカラー + hero,features,pricing
```

### 業界特化例
```
入力: 「B2B企業向けコーポレートスタイルでダークテーマのLP」
出力: corporateスタイル + darkカラー + problem,solution,testimonials
```

## ⚡ パフォーマンス

### 生成速度
- **平均**: 15-30秒
- **最適化**: ストリーミング生成
- **キャッシュ**: 共通パターンのキャッシュ化

### 出力品質
- **コード**: W3C準拠HTML
- **レスポンシブ**: モバイルファースト
- **アクセシビリティ**: WCAG 2.1準拠

## 🛠️ 実装詳細

### コア関数
```typescript
// ai/unified-lp-generator.ts
export const generateUnifiedLP = createTool({
  description: `V0/Claude Artifacts風統合LP生成`,
  parameters: z.object({
    userRequest: z.string(),
    productName: z.string(),
    targetAudience: z.string(),
    keyValue: z.string(),
    style: z.enum(['modern', 'corporate', 'startup', 'creative', 'minimal', 'bold']),
    colorScheme: z.enum(['blue', 'green', 'purple', 'orange', 'dark', 'gradient']),
    sections: z.array(z.string())
  }),
  execute: async ({ userRequest, productName, ... }) => {
    // LP生成ロジック
  }
});
```

### 表示コンポーネント
```typescript
// app/components/UnifiedLPViewer.tsx
export function UnifiedLPViewer({ html, productName, ... }) {
  // プレビュー表示 + ダウンロード機能
}
```

## 🧪 テスト項目

### 機能テスト
- [ ] 全スタイル生成確認
- [ ] 全カラースキーム動作確認
- [ ] セクション組み合わせテスト
- [ ] レスポンシブ表示確認

### 品質テスト
- [ ] HTML Validation
- [ ] CSS Validation
- [ ] アクセシビリティチェック
- [ ] パフォーマンス測定

### UXテスト
- [ ] 生成速度計測
- [ ] プレビュー動作確認
- [ ] ダウンロード機能確認
- [ ] エラーハンドリング確認

---

最終更新: 2024年12月
実装ファイル: `ai/unified-lp-generator.ts` 