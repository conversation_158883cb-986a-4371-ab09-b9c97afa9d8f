# 🧩 主要コンポーネントガイド

## 📋 概要

LP Creatorの主要Reactコンポーネントの使用方法と実装詳細を説明します。

## 🎯 コンポーネント階層

```
App (layout.tsx)
├── Page (page.tsx) - OpenAI版
├── ClaudePage (claude/page.tsx) - Claude版
│
├── ChatPanel.tsx - OpenAI版チャット
├── ChatPanelClaude.tsx - Claude版チャット
├── UnifiedLPViewer.tsx - LP表示・プレビュー
│
├── 編集系コンポーネント
│   ├── EditableText.tsx - テキスト編集
│   ├── EditableImageContainer.tsx - 画像編集コンテナ
│   └── DraggableImage.tsx - ドラッグ&ドロップ画像
│
└── Context
    └── EditModeContext.tsx - 編集状態管理
```

## 🚀 主要コンポーネント

### 1. UnifiedLPViewer.tsx

**目的**: 統合LP生成ツールの出力を表示・プレビュー

#### Props
```typescript
interface UnifiedLPViewerProps {
  html: string;              // 生成されたHTMLコード
  productName: string;       // 商品名
  style: string;            // デザインスタイル
  colorScheme: string;      // カラースキーム
  sections: string[];       // セクション一覧
  userRequest: string;      // 元のリクエスト
}
```

#### 機能
- **iframeプレビュー**: 生成されたHTMLを安全に表示
- **HTMLダウンロード**: ファイルとして保存
- **新しいタブで開く**: フルスクリーン表示
- **情報パネル**: 生成情報の表示

#### 使用例
```typescript
<UnifiedLPViewer
  html={generatedHTML}
  productName="SaaSツール"
  style="modern"
  colorScheme="blue"
  sections={["hero", "features", "pricing"]}
  userRequest="SaaSツールのLPを作って"
/>
```

### 2. ChatPanel.tsx / ChatPanelClaude.tsx

**目的**: AI SDKを使用したチャット機能

#### Props
```typescript
interface ChatPanelProps {
  onMessagesUpdate: (messages: Message[]) => void;
}
```

#### 機能
- **ストリーミングチャット**: リアルタイム生成
- **ツール実行**: generateUnifiedLP等の実行
- **メッセージ履歴**: 会話履歴管理
- **エラーハンドリング**: 失敗時の適切な表示

#### 実装ポイント
```typescript
const { messages, input, handleInputChange, handleSubmit } = useChat({
  api: '/api/chat',
  maxToolRoundtrips: 3,
  onFinish: (message) => {
    onMessagesUpdate?.(messages);
  }
});
```

### 3. EditableText.tsx

**目的**: インライン編集可能なテキストコンポーネント

#### Props
```typescript
interface EditableTextProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  maxLength?: number;
  component?: 'h1' | 'h2' | 'p' | 'span';
  style?: React.CSSProperties;
}
```

#### 機能
- **編集モード**: クリックで編集開始
- **自動フォーカス**: 編集時の自動フォーカス
- **キーボード操作**: Enter/Escapeでの制御
- **文字数制限**: maxLengthでの制限

#### 使用例
```typescript
<EditableText
  value={headline}
  onChange={setHeadline}
  placeholder="ヘッドラインを入力"
  maxLength={100}
  component="h1"
  style={{ fontSize: '48px', fontWeight: '700' }}
/>
```

### 4. EditableImageContainer.tsx

**目的**: 画像の表示・編集・ドラッグ&ドロップ対応

#### Props
```typescript
interface EditableImageContainerProps {
  imageId: string;
  imageData?: ImageData;
  onImageChange?: (imageData: ImageData) => void;
  style?: React.CSSProperties;
  children?: React.ReactNode;
}
```

#### 機能
- **画像表示**: 背景画像として表示
- **編集モード**: 編集時のドラッグ&ドロップ対応
- **画像更新**: グローバル状態との連携
- **フォールバック**: 画像なしの場合の対応

### 5. DraggableImage.tsx

**目的**: ドラッグ&ドロップによる画像操作

#### 機能
- **ドラッグ検出**: ドラッグ開始の検出
- **ドロップゾーン**: ドロップ可能エリアの表示
- **視覚フィードバック**: ドラッグ中の視覚的表示
- **位置計算**: ドロップ位置の精密計算

#### 実装詳細
```typescript
const handleDragStart = (e: DragEvent<HTMLDivElement>) => {
  if (!isEditMode) return;
  
  e.dataTransfer.setData('text/plain', imageId);
  e.dataTransfer.effectAllowed = 'move';
  
  setIsDragging(true);
  onDragStart?.(imageId, { x: e.clientX, y: e.clientY });
};
```

## 🔧 Context & Hooks

### EditModeContext.tsx

**目的**: アプリ全体の編集状態管理

#### 提供する値
```typescript
interface EditModeContextValue {
  isEditMode: boolean;
  toggleEditMode: () => void;
  isTextEditMode: boolean;
  toggleTextEditMode: () => void;
  selectedImageId: string | null;
  setSelectedImageId: (id: string | null) => void;
  // ... その他の編集状態
}
```

#### 使用方法
```typescript
const { isEditMode, toggleEditMode } = useEditMode();
```

## 🎨 スタイリング戦略

### CSS-in-JS
- **React inline styles**: 動的スタイリング
- **CSS variables**: テーマ切り替え
- **レスポンシブ**: メディアクエリ対応

### デザインシステム
```typescript
const colors = {
  primary: '#2563eb',
  secondary: '#1e40af',
  accent: '#3b82f6',
  background: '#f8f9fa',
  text: '#333333'
};

const breakpoints = {
  mobile: '768px',
  tablet: '1024px',
  desktop: '1200px'
};
```

## ⚡ パフォーマンス最適化

### React.memo & useCallback
```typescript
const UnifiedLPViewer = React.memo(({ html, productName, ... }) => {
  const handleDownload = useCallback(() => {
    // ダウンロード処理
  }, [html, productName]);
  
  return (
    // コンポーネント
  );
});
```

### 仮想化対応
- **大きなリスト**: react-windowで仮想化
- **画像遅延読み込み**: Intersection Observer
- **コード分割**: React.lazy

## 🧪 テスト戦略

### コンポーネントテスト
```typescript
// UnifiedLPViewer.test.tsx
import { render, screen } from '@testing-library/react';
import { UnifiedLPViewer } from './UnifiedLPViewer';

test('HTMLコンテンツが正しく表示される', () => {
  render(
    <UnifiedLPViewer
      html="<h1>テスト</h1>"
      productName="Test Product"
      style="modern"
      colorScheme="blue"
      sections={["hero"]}
      userRequest="テスト用"
    />
  );
  
  expect(screen.getByRole('button', { name: /ダウンロード/ })).toBeInTheDocument();
});
```

### 統合テスト
```typescript
// chat.test.tsx
test('チャット → LP生成 → プレビュー の流れ', async () => {
  render(<App />);
  
  const input = screen.getByPlaceholderText(/メッセージを入力/);
  fireEvent.change(input, { target: { value: 'SaaSツールのLPを作って' } });
  fireEvent.click(screen.getByRole('button', { name: /送信/ }));
  
  await waitFor(() => {
    expect(screen.getByTestId('unified-lp-viewer')).toBeInTheDocument();
  });
});
```

## 📱 レスポンシブ対応

### ブレークポイント戦略
```typescript
const useResponsive = () => {
  const [isMobile, setIsMobile] = useState(false);
  
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  return { isMobile };
};
```

### モバイル最適化
- **タッチ操作**: タッチイベント対応
- **画面サイズ**: 小画面でのレイアウト調整
- **パフォーマンス**: モバイルでの最適化

## 🔒 セキュリティ考慮

### XSS対策
- **iframe sandbox**: 生成HTMLの安全な表示
- **CSP**: Content Security Policy
- **入力検証**: ユーザー入力のサニタイズ

### データ保護
- **APIキー**: サーバーサイドで管理
- **ローカルストレージ**: 機密情報の非保存
- **HTTPS**: 通信の暗号化

---

最終更新: 2024年12月
実装ディレクトリ: `app/components/` 