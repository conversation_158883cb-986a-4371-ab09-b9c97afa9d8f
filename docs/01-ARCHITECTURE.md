# 🏗️ LP Creator - システムアーキテクチャ

## 📋 概要

LP CreatorはNext.js 15 + React 19をベースに構築された、V0/Claude Artifacts風の統合LP生成アプリケーションです。

## 🎯 設計思想

### Before（従来の複雑システム）
```
ユーザー入力 → 20+個の個別ツール → セクション分割LP → 複雑な組み合わせ
```

### After（統合システム）
```
ユーザー入力 → 1つの統合ツール → 完全なHTML/CSS/JS → 即座にダウンロード
```

## 🗂️ ディレクトリ構成

```
lp-creator/
├── ai/                              # AI統合レイヤー
│   ├── tools.ts                     # ツール管理（統合済み）
│   └── unified-lp-generator.ts      # 統合LP生成エンジン
│
├── app/                             # Next.js App Router
│   ├── components/                  # UIコンポーネント
│   │   ├── UnifiedLPViewer.tsx      # LP表示・プレビュー
│   │   ├── ChatPanel.tsx            # OpenAI版チャット
│   │   ├── ChatPanelClaude.tsx      # Claude版チャット
│   │   ├── EditableText.tsx         # テキスト編集機能
│   │   ├── EditableImageContainer.tsx # 画像編集機能
│   │   └── DraggableImage.tsx       # ドラッグ&ドロップ
│   │
│   ├── contexts/                    # React Context
│   │   └── EditModeContext.tsx      # 編集モード管理
│   │
│   ├── api/                         # API Routes
│   │   └── chat/route.ts            # AI SDK統合エンドポイント
│   │
│   ├── claude/                      # Claude版ページ
│   │   └── page.tsx                 # Claude専用UI
│   │
│   ├── page.tsx                     # メインページ（OpenAI版）
│   └── layout.tsx                   # アプリレイアウト
│
├── mastra/                          # Mastraフレームワーク
│   ├── agents/                      # AIエージェント定義
│   ├── tools/                       # カスタムツール
│   └── index.ts                     # Mastra設定
│
├── docs/                            # ドキュメント
├── tests/                           # テストファイル
└── public/                          # 静的ファイル
```

## 🔧 技術スタック

### フロントエンド
- **Next.js 15**: App Router, Server Components
- **React 19**: 最新機能 + Concurrent Features
- **TypeScript**: 完全型安全性
- **Tailwind CSS**: ユーティリティファーストCSS

### AI統合
- **AI SDK**: Vercel AI SDK v4
- **OpenAI GPT-4**: メイン生成エンジン
- **Claude 3.5 Sonnet**: 代替生成エンジン
- **Mastra Framework**: AI統合・学習機能

### 状態管理
- **React Context**: 軽量状態管理
- **useState/useCallback**: ローカル状態
- **EditModeContext**: グローバル編集状態

## 🚀 システムフロー

### 1. ユーザー入力
```
ユーザー → チャット入力 → AI SDK
```

### 2. AI処理
```
AI SDK → generateUnifiedLP → 完全HTML生成
```

### 3. 表示・プレビュー
```
HTML → UnifiedLPViewer → iframe表示 → ダウンロード
```

## 📊 データフロー

```mermaid
graph TD
    A[ユーザー入力] --> B[ChatPanel]
    B --> C[AI SDK]
    C --> D[generateUnifiedLP]
    D --> E[HTML/CSS/JS生成]
    E --> F[UnifiedLPViewer]
    F --> G[プレビュー表示]
    F --> H[ダウンロード機能]
    
    I[EditModeContext] --> J[編集状態管理]
    J --> K[リアルタイム編集]
```

## 🎨 UI/UXアーキテクチャ

### レスポンシブデザイン
- **デスクトップ**: 2カラムレイアウト（チャット・プレビュー）
- **タブレット**: 適応的レイアウト
- **モバイル**: 単一カラム、スワイプ対応

### 編集機能
- **テキスト編集**: インライン編集（EditableText）
- **画像編集**: ドラッグ&ドロップ（DraggableImage）
- **編集モード**: 編集/表示モード切り替え

## 🔐 セキュリティ

### API保護
- 環境変数によるAPIキー管理
- Next.js API Routesでプロキシ
- サーバーサイド処理

### XSS対策
- DOMPurifyによるHTML消毒
- iframe サンドボックス化
- CSPヘッダー設定

## ⚡ パフォーマンス

### 最適化戦略
- **Server Components**: 初期レンダリング高速化
- **React 19**: Concurrent Features活用
- **コード分割**: 動的インポート
- **画像最適化**: Next.js Image最適化

### メモリ管理
- **useCallback**: 再レンダリング防止
- **React.memo**: コンポーネント最適化
- **適切なクリーンアップ**: useEffect cleanup

## 🧪 テスト戦略

### 単体テスト
- **Jest**: コンポーネントテスト
- **React Testing Library**: UI テスト
- **MSW**: API モッキング

### E2Eテスト
- **Playwright**: ブラウザ自動化
- **視覚回帰テスト**: スクリーンショット比較

## 🔄 CI/CD

### GitHub Actions
- **自動テスト**: PR時実行
- **型チェック**: TypeScript検証
- **リント**: ESLint + Prettier
- **ビルド**: 本番環境向け最適化

## 📈 監視・ログ

### アプリケーション監視
- **Console.log**: 開発時デバッグ
- **Error Boundary**: React エラーハンドリング
- **パフォーマンス**: Web Vitals測定

### AIサービス監視
- **API レスポンス時間**: 生成速度測定
- **エラー率**: 失敗率追跡
- **コスト管理**: トークン使用量監視

---

最終更新: 2024年12月
技術仕様: Next.js 15 + React 19 + AI SDK v4 