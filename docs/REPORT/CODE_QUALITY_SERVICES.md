# コード品質サービス設定ガイド

## 概要

LP Creator プロジェクトでは、以下の**内蔵サービス**を使用してコード品質を管理しています。
外部サービスへの依存を最小限に抑え、保守性を重視した構成です。

## 利用中のサービス

### 1. npm audit（セキュリティスキャン）

**概要**: Node.js標準のセキュリティ脆弱性検出ツール

**特徴**:
- ✅ 無料・内蔵
- ✅ 設定不要
- ✅ 依存関係の脆弱性を自動検出
- ✅ 修正提案も提供

**実行内容**:
- 基本的な脆弱性スキャン
- 詳細な分析レポート生成
- 修正可能な脆弱性の確認

### 2. CodeQL（静的解析）

**概要**: GitHub標準の高度な静的解析ツール

**特徴**:
- ✅ GitHub で無料利用可能
- ✅ 設定不要
- ✅ セキュリティ脆弱性の高精度検出
- ✅ JavaScript/TypeScript対応

**実行内容**:
- セキュリティ脆弱性の検出
- コード品質の分析
- SARIF形式でのレポート生成

### 3. Jest Coverage（カバレッジ測定）

**概要**: プロジェクト内蔵のテストカバレッジ測定

**特徴**:
- ✅ プロジェクトに内蔵
- ✅ 外部サービス不要
- ✅ 詳細なカバレッジレポート
- ✅ しきい値チェック機能

**実行内容**:
- ライン・ブランチ・関数カバレッジ測定
- カバレッジサマリーの表示
- 70%しきい値チェック

### 4. Lighthouse CI（パフォーマンス監査）

**概要**: Webパフォーマンスの自動監査

**特徴**:
- ✅ 無料・オープンソース
- ✅ 外部サービス不要
- ✅ パフォーマンス・アクセシビリティ・SEO監査
- ✅ 継続的な品質監視

### 5. Dependency Review（依存関係チェック）

**概要**: GitHub標準の依存関係セキュリティチェック

**特徴**:
- ✅ GitHub で無料利用可能
- ✅ プルリクエスト時の自動チェック
- ✅ ライセンス互換性確認
- ✅ 脆弱性のある依存関係の検出

## ワークフロー構成

```yaml
# .github/workflows/code-quality.yml
jobs:
  security-scan:      # npm audit
  codeql-analysis:    # CodeQL
  dependency-review:  # 依存関係チェック
  code-coverage:      # Jest coverage
  performance-audit:  # Lighthouse CI
  bundle-analysis:    # バンドルサイズ分析
  security-headers:   # セキュリティヘッダーチェック
```

## 利点

### 🔒 セキュリティ
- 多層的なセキュリティチェック
- 依存関係の脆弱性検出
- セキュリティヘッダーの確認

### 📊 品質管理
- コードカバレッジの継続監視
- パフォーマンス監査
- バンドルサイズの監視

### 🚀 保守性
- 外部サービス依存なし
- トークン管理不要
- 設定の簡素化

### 💰 コスト効率
- 全て無料で利用可能
- 追加料金なし
- スケーラブル

## 実行タイミング

- **プッシュ時**: main/develop ブランチ
- **プルリクエスト時**: 全ての品質チェック
- **定期実行**: 週次セキュリティスキャン

## 今後の拡張

必要に応じて以下のサービスを追加検討可能：

- **SonarCloud**: より詳細なコード品質分析
- **Renovate**: 依存関係の自動更新
- **Semantic Release**: 自動バージョニング

ただし、現在の構成で十分な品質管理が可能です。 