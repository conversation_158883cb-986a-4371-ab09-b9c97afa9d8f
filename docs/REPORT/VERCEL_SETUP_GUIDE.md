# Vercel設定ガイド - GitHub Actions連携

## 🔧 必要なシークレット変数

現在のワークフローで警告が出ているVercel関連のシークレット変数を設定する必要があります：

- `VERCEL_TOKEN` - Vercelアクセストークン
- `VERCEL_ORG_ID` - Vercel組織ID  
- `VERCEL_PROJECT_ID` - VercelプロジェクトID

## 📋 設定手順

### 1. Vercelアカウント・プロジェクト作成

1. **Vercelアカウント作成**
   - [vercel.com](https://vercel.com) にアクセス
   - GitHubアカウントでサインアップ（推奨）

2. **プロジェクト作成**
   - Vercelダッシュボードで「New Project」をクリック
   - GitHubリポジトリを選択してインポート
   - フレームワーク設定（Next.js等）を確認
   - 「Deploy」をクリック

### 2. 必要な情報の取得

#### A. VERCEL_TOKEN の取得
1. Vercelダッシュボードでプロフィールアイコンをクリック
2. 「Account Settings」を選択
3. 左サイドバーの「Tokens」をクリック
4. 「Create Token」をクリック
5. トークン名を入力（例：`GitHub Actions`）
6. スコープを選択（プロジェクト単位推奨）
7. 有効期限を設定
8. 「Create」をクリックしてトークンをコピー

#### B. VERCEL_PROJECT_ID の取得
1. Vercelダッシュボードでプロジェクトを選択
2. 「Settings」タブをクリック
3. 「General」セクションをスクロール
4. 「Project ID」をコピー

#### C. VERCEL_ORG_ID の取得
1. Vercelダッシュボードでコマンドメニューを開く
   - プロフィールアイコン → 「Command Menu」
   - または `Cmd + K` (Mac) / `Ctrl + K` (Windows)
2. 「Search Team」タブをクリック（または `Shift + T`）
3. 「Copy Team ID」をクリックして組織IDをコピー

### 3. GitHubシークレット設定

1. **GitHubリポジトリにアクセス**
   - リポジトリページで「Settings」タブをクリック

2. **シークレット追加**
   - 左サイドバーで「Secrets and variables」→「Actions」を選択
   - 「New repository secret」をクリック

3. **各シークレットを追加**

   **VERCEL_TOKEN**
   ```
   Name: VERCEL_TOKEN
   Secret: [取得したVercelトークン]
   ```

   **VERCEL_ORG_ID**
   ```
   Name: VERCEL_ORG_ID  
   Secret: [取得した組織ID（team_xxxxx形式）]
   ```

   **VERCEL_PROJECT_ID**
   ```
   Name: VERCEL_PROJECT_ID
   Secret: [取得したプロジェクトID]
   ```

## 🔄 設定確認

### 1. ワークフロー実行テスト
1. 設定完了後、mainブランチにpushしてワークフローを実行
2. GitHub Actionsタブで実行状況を確認
3. エラーがないことを確認

### 2. デプロイメント確認
1. Vercelダッシュボードで新しいデプロイメントを確認
2. プレビューURLにアクセスして動作確認
3. GitHub Actionsからのデプロイメントであることを確認

## ⚠️ セキュリティ注意事項

### トークンの管理
- **絶対にトークンをコードにハードコーディングしない**
- GitHubシークレットのみを使用
- 定期的にトークンをローテーション
- 最小権限の原則を適用

### 環境分離
- 本番環境とステージング環境で異なるプロジェクトを使用
- 環境ごとに異なるシークレットを設定
- 適切なブランチ保護ルールを設定

## 🛠️ トラブルシューティング

### よくあるエラー

1. **"Invalid token" エラー**
   - トークンが正しくコピーされているか確認
   - トークンの有効期限を確認
   - スコープ設定を確認

2. **"Project not found" エラー**
   - プロジェクトIDが正しいか確認
   - 組織IDが正しいか確認
   - プロジェクトへのアクセス権限を確認

3. **"Deployment failed" エラー**
   - ビルドログを確認
   - 環境変数の設定を確認
   - 依存関係の問題を確認

### デバッグ方法
```bash
# ローカルでVercel CLIをテスト
npm install -g vercel@latest
vercel login
vercel --version

# プロジェクトリンク確認
vercel link
vercel env ls
```

## 📚 参考リンク

- [Vercel CLI Documentation](https://vercel.com/docs/cli)
- [GitHub Actions with Vercel](https://vercel.com/guides/how-can-i-use-github-actions-with-vercel)
- [Vercel Environment Variables](https://vercel.com/docs/projects/environment-variables)

---

**設定完了後**: ワークフローの警告が解消され、自動デプロイメントが正常に動作します。 