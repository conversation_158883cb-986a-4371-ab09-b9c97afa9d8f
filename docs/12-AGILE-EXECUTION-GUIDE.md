# 📅 LP Creator アジャイル実行ガイド

## 🎯 概要

LP Creator MVPのGitHub Project + **4スプリント管理**の具体的な実行手順を整理します。
**新機能要望**（レター文章作成、AI対話修正、画像挿入、ホスティング）を含む完全版の開発計画です。

---

## 📊 拡張スプリント概要

| Sprint | 期間 | ゴール | 主要成果物 |
|--------|------|--------|-----------|
| **Sprint 0** | 12/13-12/20 | 環境&基盤構築 | V0風レイアウト、統一API、安全プレビュー |
| **Sprint 1** | 12/20-12/27 | コア体験縦串 | LP生成→プレビュー→拡大表示の完全動作 |
| **Sprint 2** | 12/27-01/03 | 編集&保存機能 | 編集モード、クラウド保存、共有URL |
| **Sprint 3** | 01/03-01/17 | 高度機能実装 | レター作成、AI対話修正、画像挿入 |
| **Sprint 4** | 01/17-02/07 | 本格運用機能 | ホスティング、コンセプト理論、エンタープライズ |

---

## 🚀 各スプリントの詳細目標

### 🌱 Sprint 0: 基盤構築（1週間）
**成功指標**: CI/CD緑、V0風レイアウト動作、安全プレビュー表示

```markdown
Day 1-2: 緊急修正
- Layout.tsx V0風Grid化
- /api/generate統一エンドポイント作成

Day 3-4: 基盤強化  
- PreviewPane iframe化
- GitHub Actions CI完全版

Day 5-7: テスト環境
- Jest + RTL拡充
- Provider Interface設計
```

### 🌟 Sprint 1: コア体験（1週間）
**成功指標**: エンドツーエンドLP生成動作、E2Eテスト通過

```markdown
Day 1-3: UI/UX改善
- ChatPanel V0風リニューアル
- react-split-pane導入
- 全画面/新タブ機能

Day 4-7: Provider統合
- Claude streaming集約
- Gemini Provider実装
- E2E Smoke Test作成
```

### 🛠️ Sprint 2: 編集&保存（1週間）
**成功指標**: 生成→編集→保存→共有の完全フロー動作

```markdown
Day 1-3: 編集機能
- Tiptap WYSIWYG導入
- Undo/Redo機能
- Image Upload (UploadThing)

Day 4-7: 保存機能
- KV Save API実装
- Preview Route作成
- Metadata JSON保存
```

### ✨ Sprint 3: 高度機能（2週間）
**成功指標**: レター→LP生成→AI修正→画像挿入の完全フロー

```markdown
Week 1: レター作成フロー
Day 1-2: ビジネスヒアリングフォーム設計
Day 3-4: レター文章生成API実装
Day 5: レター承認UI実装

Week 2: AI対話修正 & 画像機能
Day 1-3: 修正チャットパネル + 差分適用API
Day 4-5: 修正履歴管理システム
Day 6-7: ノーコード画像挿入機能
```

### 🌊 Sprint 4: 本格運用（3週間）
**成功指標**: ツール内ホスティング、料金体系、エンタープライズ機能

```markdown
Week 1: ホスティング機能
Day 1-3: ツール内ホスティング実装
Day 4-5: 公開設定UI

Week 2: 高度コンセプト
Day 1-3: 複数コンセプト提案機能
Day 4-5: JPRO向けコンセプト理論統合

Week 3: エンタープライズ
Day 1-3: チーム管理機能
Day 4-5: エンタープライズAPI料金制限
```

---

## 🏃‍♂️ Daily Standup (推奨フォーマット)

### ⏰ 開催：毎日朝9:00（15分）

```markdown
## 📅 Daily Standup - YYYY/MM/DD

### 👥 参加者
- [ ] フロントエンド担当
- [ ] バックエンド担当  
- [ ] QA担当

### 🎯 昨日やったこと
**[名前]**
- [ ] Issue #X: レイアウト修正 → 75%完了
- [ ] PR #Y: API統合 → レビュー待ち

### 🚀 今日やること  
**[名前]**
- [ ] Issue #X: レイアウト修正完了予定
- [ ] Issue #Z: テスト作成開始

### 🚧 ブロッカー・課題
- [ ] ❌ 外部API仕様が不明（調査中）
- [ ] ⚠️ テスト環境でエラー（対応必要）

### 📊 スプリント進捗
- Sprint 0: ⬜⬜⬜⬜⬜⬜⬜ (4/7 Issues完了 - 57%)
```

---

## 📝 週次スプリントレビュー

### ⏰ 開催：毎週金曜16:00（60分）

#### 🎯 アジェンダ (Timeboxed)

```markdown
## 📅 Sprint Review - Week XX

### 1️⃣ スプリントゴール振り返り (15分)
- [ ] 今週の目標達成度: X/Y Issues完了
- [ ] 主要成果物デモ実演
- [ ] 品質指標確認（テストカバレッジ等）

### 2️⃣ 完了Issue紹介 (20分)  
- [ ] Issue #X: 機能デモ + 受け入れ条件確認
- [ ] Issue #Y: 技術的改善内容説明
- [ ] 各Issueの「Definition of Done」チェック

### 3️⃣ 課題・ブロッカー分析 (15分)
- [ ] 未完了Issueの原因分析
- [ ] 見積もり精度の振り返り
- [ ] プロセス改善提案

### 4️⃣ 次週計画調整 (10分)
- [ ] 優先度調整の必要性確認
- [ ] 新しいIssue追加検討
- [ ] リソース配分見直し
```

---

## 🚀 実行開始チェックリスト

### ✅ Step 1: 環境準備（30分）

```bash
# 1. GitHub CLI セットアップ
brew install gh  # macOS
gh auth login

# 2. ラベル・Issue作成実行
chmod +x scripts/create-github-labels.sh
./scripts/create-github-labels.sh

chmod +x scripts/create-urgent-issues.sh  
./scripts/create-urgent-issues.sh

# 3. Projectボード確認
# GitHub WebUIでProject作成 & カラム設定
```

### ✅ Step 2: Sprint 0 開始（即日）

```markdown
## 🎯 Sprint 0 緊急対応 (High Priority)

### 今日やること (Day 1)
- [ ] Issue #1: Layout.tsx V0風Grid化 (2h)
- [ ] Issue #2: /api/generate統一エンドポイント (4h)  
- [ ] Issue #3: PreviewPane iframe化 (3h)

### 受け入れ条件確認
- [ ] 左45% × 右55%レイアウト表示確認
- [ ] 統一APIでLP生成動作確認
- [ ] iframe内で安全プレビュー表示確認
```

### ✅ Step 3: 継続運営（日次）

```markdown
## 📅 継続運営ルーチン

### 毎朝 (9:00-9:15)
1. Daily Standup実施
2. GitHub Project Boardの進捗確認  
3. ブロッカーの早期発見・対処

### 毎夕 (17:00-17:30)
1. 当日完了Issue の「Done」移動
2. 翌日予定の「In Progress」移動
3. PRレビュー・マージ処理

### 毎週金曜 (16:00-17:00)  
1. Sprint Review & Retrospective
2. 次週計画調整
3. ドキュメント更新
```

---

## 📊 進捗管理指標

### 🎯 KPI (Key Performance Indicators)

| 指標 | 目標値 | 測定方法 |
|------|--------|----------|
| **Issue完了率** | 90%以上 | 完了Issue数 / 計画Issue数 |
| **見積もり精度** | ±20%以内 | 実績時間 / 見積もり時間 |
| **バグ発生率** | 10%以下 | バグIssue数 / 全Issue数 |
| **テストカバレッジ** | 80%以上 | Jest coverage report |
| **Deploy成功率** | 95%以上 | 成功Deploy数 / 全Deploy数 |
| **AI API使用量** | 予算内 | 月次使用クレジット追跡 |

### 📈 トラッキング方法

```bash
# 毎日の進捗記録
echo "$(date): Sprint 0 - 4/7 Issues完了 (57%)" >> progress.log

# 週次レポート生成
gh issue list --milestone "sprint-0" --state closed | wc -l

# API使用量確認 (新規)
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
  https://api.openai.com/v1/usage
```

---

## 🔄 プロセス改善サイクル

### 🎯 Retrospective フォーマット (毎週金曜)

```markdown
## 🔄 Sprint Retrospective

### ✅ Keep (続けること)
- 良かった点・今後も継続したいこと
- 効果的だったツール・プロセス

### ❌ Problem (問題・課題)  
- 困ったこと・改善が必要なこと
- ボトルネックとなっている部分

### 💡 Try (次に試すこと)
- 改善アクション・新しい取り組み
- 具体的な実行計画（担当者・期限付き）

### 📊 数値による振り返り
- 今週のVelocity: X Story Points
- 見積もり精度: 平均XX%の乖離
- バグ発生件数: Y件
- AI API使用量: $XX (予算内/オーバー)
```

---

## 💰 新機能の課金・コスト管理

### 🎯 AI API使用量管理

```markdown
## API使用量予測（Sprint 3-4）

### レター文章作成機能
- 1回あたり: ~2,000 tokens
- 月間予想: 1,000回 → $20-40

### AI対話修正機能  
- 1修正あたり: ~3,000 tokens
- 月間予想: 5,000回 → $150-300

### 画像生成・処理
- 1回あたり: $0.02-0.10
- 月間予想: 500回 → $10-50

### 合計予想月額コスト: $180-390
```

### 🔐 クレジット制限システム

```typescript
// app/middleware.ts での使用量チェック
export async function middleware(request: NextRequest) {
  const userId = getUserId(request);
  const usage = await getUserUsage(userId);
  
  if (usage.credits <= 0) {
    return new Response('Credits exhausted', { status: 429 });
  }
  
  return NextResponse.next();
}
```

---

## 🛠️ ツール・リンク集

### 📱 日常使用ツール
- **GitHub Project**: `https://github.com/YOUR_USER/lp-creator/projects/1`
- **Sprint Board**: Backlog → Sprint X-Todo → In Progress → Review → Done
- **CI/CD Dashboard**: GitHub Actions
- **テスト結果**: Jest + Playwright Reports
- **API使用量監視**: OpenAI/Claude Dashboard

### 📋 定期実行コマンド
```bash
# 進捗確認
gh project view --owner YOUR_USER --number 1

# Issue作成
gh issue create --template feature.yml

# PR作成
gh pr create --template .github/pull_request_template.md

# API使用量チェック (新規)
npm run check:api-usage
```

---

## ✨ 各スプリントの成功定義

### 🎯 Sprint 0 完了条件
- [ ] V0風レイアウトが正常動作
- [ ] 統一API経由でLP生成可能
- [ ] 安全なiframeプレビュー表示
- [ ] テストカバレッジ >80%
- [ ] 全Issue の Definition of Done 達成

### 🎯 Sprint 1 完了条件
- [ ] 左チャット × 右プレビューの完全動作
- [ ] 3つのプレビュー拡大パターン実装
- [ ] マルチプロバイダー対応（OpenAI/Claude/Gemini）
- [ ] E2Eテスト全通過

### 🎯 Sprint 2 完了条件
- [ ] 編集モード + リアルタイム反映
- [ ] クラウド保存 + 共有URL生成
- [ ] 画像アップロード機能
- [ ] Undo/Redo履歴管理

### ✨ Sprint 3 完了条件（新規）
- [ ] ビジネスヒアリング → レター生成 → 承認フロー
- [ ] AI対話による修正機能の完全動作
- [ ] ノーコード画像挿入・編集
- [ ] 修正履歴管理とロールバック

### 🌊 Sprint 4 完了条件（新規）
- [ ] ツール内ホスティング + 独自URL生成
- [ ] 公開設定・アクセス制御
- [ ] 複数コンセプト提案機能
- [ ] エンタープライズ向け料金制限・チーム管理

---

## 📈 長期的な成功指標

### 🎯 MVP完成条件 (Sprint 2終了時)
- [ ] 基本LP生成機能の完全動作
- [ ] 編集・保存・共有の完全サイクル
- [ ] 本番環境でのデプロイ成功

### ✨ 高度機能完成条件 (Sprint 3終了時)
- [ ] レター → LP生成の差別化フロー
- [ ] AI対話修正による高度なカスタマイズ
- [ ] 直感的な画像挿入・編集

### 🌊 本格運用条件 (Sprint 4終了時)
- [ ] エンドユーザーの完全セルフサービス
- [ ] ホスティング・独自ドメイン対応
- [ ] 企業向け機能・料金体系

---

**🎯 次のアクション: GitHub CLI準備 → ラベル/Issue作成 → Sprint 0開始 → 4スプリント完走**

これで**LP Creator**の基本MVPから本格運用まで、完全な4スプリント開発計画が完成しました！ 