# ✨ 新機能仕様書 ― レター作成・AI対話修正・画像挿入・ホスティング

## 📋 概要

LP Creator に追加された4つの高度機能の詳細仕様をまとめています。
これらの機能により、LP Creator は単純な生成ツールから**本格的なビジネス支援プラットフォーム**に進化します。

---

## 🎯 機能1: レター文章作成機能

### 📝 機能概要
LP作成前に、ビジネスヒアリング → コンセプト提案文章（レター）生成 → 承認 → LP作成開始の段階的フローを実装します。

### 🎪 ユーザージャーニー
```mermaid
graph LR
    A[LP作成開始] --> B[チャットでヒアリング]
    B --> C[レター文章生成]
    C --> D[レター確認・承認]
    D --> E[LP生成開始]
    D --> F[レター修正要求]
    F --> C
```

### 🛠️ 技術仕様

#### チャットベースヒアリング設計
```typescript
interface BusinessInfo {
  // 基本情報
  product: string;           // 商品・サービス名
  category: string;          // 業界・カテゴリ
  target: string;            // ターゲット顧客
  
  // 課題・解決策
  problems: string[];        // 顧客の抱える問題
  solutions: string[];       // 提供する解決策
  benefits: string[];        // 得られるメリット
  
  // 差別化・証拠
  uniqueness: string;        // 競合との差別化点
  evidence: string[];        // 実績・証拠・お客様の声
  
  // 価格・提供形態
  pricing: string;           // 価格設定
  deliveryMethod: string;    // 提供方法（オンライン/オフライン/両方）
}
```

#### チャットヒアリングAPI
```typescript
// POST /api/chat/hearing
interface ChatHearingRequest {
  message: string;              // ユーザーのメッセージ
  conversationId: string;       // 会話セッションID
  context: BusinessInfo;        // これまでに収集した情報
}

interface ChatHearingResponse {
  message: string;              // AIの次の質問・応答
  nextQuestion?: string;        // 次に聞くべき質問
  collectedInfo: BusinessInfo;  // 収集済み情報
  completionRate: number;       // 情報収集完了率 (0-1)
  isComplete: boolean;          // ヒアリング完了フラグ
  suggestedTone?: string;       // 推奨されるトーン
}
```

#### レター生成API
```typescript
// POST /api/letter/generate
interface LetterGenerateRequest {
  businessInfo: BusinessInfo;
  tone: 'professional' | 'friendly' | 'urgent' | 'casual';
  length: 'short' | 'medium' | 'long';
}

interface LetterGenerateResponse {
  letter: string;               // 生成されたレター文章
  concept: string;              // 抽出されたコンセプト
  suggestions: string[];        // 改善提案
  keyMessages: string[];        // 主要メッセージ
  targetPersona: string;        // 想定ターゲット像
}
```

#### レター承認UI
```typescript
interface LetterApprovalProps {
  letter: string;
  concept: string;
  onApprove: () => void;
  onReject: (feedback: string) => void;
  onModify: (modifications: string[]) => void;
}
```

### 🎨 UI/UX設計

#### チャットベースヒアリング
```markdown
## 対話フロー
1. **開始**: 「あなたの商品・サービスについて詳しく教えてください」
2. **深掘り**: AIが段階的に質問を投げかけて情報を収集
3. **確認**: 収集した情報の確認と補完
4. **完了**: ヒアリング完了 → レター生成準備

## 収集する情報項目
- 商品・サービス名、業界
- ターゲット顧客
- 顧客の抱える問題
- 提供する解決策
- 競合との差別化点
- 実績・証拠
- 価格設定・提供方法

## チャットUI特徴
- 📊 進捗インジケーター (情報収集率表示)
- 💡 収集済み情報のサマリー表示
- 🎯 次に聞かれる内容のヒント
```

#### レター表示UI
```markdown
## レター表示画面
- 📄 生成されたレター文章 (読みやすいフォーマット)
- 🎯 抽出されたコンセプト (強調表示)
- 💡 改善提案 (アコーディオン形式)
- 👤 想定ターゲット像 (ペルソナカード)

## アクションボタン
- ✅ このレターでLP作成開始
- ✏️ レターを修正
- 🔄 別のパターンで再生成
```

---

## 🎯 機能2: AI対話による修正機能

### 📝 機能概要
完成したLPに対して、自然言語での対話を通じて細かい修正を行える機能です。
「ボタンの色を青に変更して」「見出しをもっと大きくして」といった指示が可能です。

### 🎪 ユーザージャーニー
```mermaid
graph LR
    A[LP完成] --> B[修正チャット開始]
    B --> C[修正指示入力]
    C --> D[AI修正実行]
    D --> E[差分プレビュー]
    E --> F[承認・適用]
    E --> G[やり直し]
    G --> C
    F --> H[履歴保存]
```

### 🛠️ 技術仕様

#### 修正指示の解析
```typescript
interface ModificationRequest {
  instruction: string;          // ユーザーの修正指示
  targetElement?: string;       // 対象要素（自動推定）
  modificationType: 'style' | 'content' | 'structure' | 'add' | 'remove';
  priority: 'high' | 'medium' | 'low';
}

interface ModificationContext {
  currentHtml: string;
  currentCss: string;
  previousModifications: ModificationHistory[];
  lpMetadata: LPMetadata;
}
```

#### 差分適用API
```typescript
// POST /api/modify
interface ModifyRequest {
  currentHtml: string;
  currentCss: string;
  modificationRequest: string;
  context: ModificationContext;
}

interface ModifyResponse {
  modifiedHtml: string;
  modifiedCss: string;
  changes: Change[];              // 変更箇所の詳細
  explanation: string;            // 修正内容の説明
  confidence: number;             // AI の確信度 (0-1)
  alternatives?: Alternative[];   // 代替案
}

interface Change {
  type: 'add' | 'modify' | 'remove';
  element: string;               // CSS セレクター
  property?: string;             // 変更されたプロパティ
  oldValue?: string;
  newValue?: string;
  reason: string;                // 変更理由
}
```

#### 修正履歴管理
```typescript
interface ModificationHistory {
  id: string;
  timestamp: Date;
  instruction: string;
  changes: Change[];
  htmlBefore: string;
  htmlAfter: string;
  cssBefore: string;
  cssAfter: string;
  userRating?: 'good' | 'bad';   // ユーザー評価
}

// Undo/Redo 機能
interface HistoryManager {
  undo(): Promise<LPState>;
  redo(): Promise<LPState>;
  jumpTo(historyId: string): Promise<LPState>;
  getHistory(): ModificationHistory[];
}
```

### 🎨 UI/UX設計

#### 修正チャットパネル
```markdown
## チャットインターフェース
- 💬 修正指示入力 (マルチライン対応)
- 🎯 対象要素の視覚的選択 (クリックで選択)
- ⚡ クイック修正ボタン (色変更・サイズ調整等)
- 📝 修正履歴タイムライン

## 差分表示
- Before/After サイドバイサイド表示
- 変更箇所のハイライト
- 変更内容の説明テキスト
```

#### 対話例
```markdown
👤 ユーザー: "ヘッダーの背景色を青色にして"
🤖 AI: "ヘッダーの背景色を青色(#007bff)に変更しました。プレビューをご確認ください。"

👤 ユーザー: "もう少し濃い青にして"  
🤖 AI: "背景色をより濃い青(#0056b3)に調整しました。"

👤 ユーザー: "ボタンを中央に配置して大きくして"
🤖 AI: "メインボタンを中央配置にし、サイズを1.2倍に拡大しました。"
```

---

## 🎯 機能3: ノーコード画像挿入機能

### 📝 機能概要
完成したLPに対して、マウス操作だけで画像の挿入・編集・削除ができる機能です。
ドラッグ&ドロップ、リサイズ、位置調整がノーコードで可能です。

### 🎪 ユーザージャーニー
```mermaid
graph LR
    A[LP表示] --> B[画像挿入モード]
    B --> C[画像D&D or 選択]
    C --> D[位置調整]
    D --> E[サイズ調整]
    E --> F[保存・適用]
    B --> G[既存画像編集]
    G --> H[移動・リサイズ・削除]
    H --> F
```

### 🛠️ 技術仕様

#### 画像ドロップゾーン
```typescript
interface ImageDropZone {
  position: { x: number; y: number };
  size: { width: number; height: number };
  placeholder: string;
  acceptedTypes: string[];        // ['image/*', '.jpg', '.png', '.webp']
  maxFileSize: number;           // MB
}

interface DroppedImage {
  file: File;
  preview: string;               // Data URL for preview
  position: { x: number; y: number };
  size: { width: number; height: number };
  alt: string;
  className?: string;
}
```

#### 画像操作ツールバー
```typescript
interface ImageToolbar {
  targetImage: HTMLImageElement;
  actions: ImageAction[];
}

interface ImageAction {
  type: 'resize' | 'move' | 'rotate' | 'filter' | 'delete' | 'alt';
  icon: string;
  handler: (image: HTMLImageElement) => void;
}

// リサイズハンドル
interface ResizeHandle {
  position: 'nw' | 'ne' | 'sw' | 'se' | 'n' | 's' | 'e' | 'w';
  onDrag: (delta: { x: number; y: number }) => void;
}
```

#### 画像最適化
```typescript
interface ImageOptimization {
  compress: (file: File, quality: number) => Promise<File>;
  resize: (file: File, maxWidth: number, maxHeight: number) => Promise<File>;
  convertToWebP: (file: File) => Promise<File>;
  generateAltText: (imageUrl: string) => Promise<string>;  // AI生成
}
```

### 🎨 UI/UX設計

#### 画像挿入モード
```markdown
## 挿入モード UI
- 🎯 LP上にドロップゾーン表示 (点線枠)
- 📁 ファイル選択ボタン
- 🔗 URL入力フィールド
- 🎨 Unsplash 統合検索

## ドラッグ&ドロップ体験
1. ファイルをLP上にドラッグ
2. ドロップ候補位置をリアルタイム表示
3. ドロップで即座に配置・プレビュー
4. 位置・サイズ調整ハンドル表示
```

#### 画像編集ツールバー
```markdown
## 選択時に表示されるツールバー
- 🔧 リサイズハンドル (8方向)
- 📐 回転ハンドル
- 🎨 フィルター (明度・コントラスト・彩度)
- 📝 Alt テキスト編集
- 🗑️ 削除ボタン
- 📋 画像情報 (サイズ・形式・ファイルサイズ)
```

---

## 🎯 機能4: ツール内ホスティング機能

### 📝 機能概要
作成したLPをツール内で即座に公開し、独自URLでアクセス可能にする機能です。
独自ドメイン対応、アクセス制御、簡易統計機能を含みます。

### 🎪 ユーザージャーニー
```mermaid
graph LR
    A[LP完成] --> B[公開設定]
    B --> C[URL生成]
    C --> D[公開開始]
    D --> E[アクセス監視]
    E --> F[統計確認]
    E --> G[設定変更]
    G --> B
```

### 🛠️ 技術仕様

#### 公開設定
```typescript
interface PublishSettings {
  // 基本設定
  isPublic: boolean;
  customPath?: string;           // カスタムパス (例: /my-awesome-lp)
  customDomain?: string;         // 独自ドメイン
  
  // アクセス制御
  accessControl: 'public' | 'password' | 'private';
  password?: string;
  allowedDomains?: string[];     // リファラー制限
  
  // SEO設定
  metaTitle?: string;
  metaDescription?: string;
  ogImage?: string;
  noIndex?: boolean;             // 検索エンジンのインデックス制御
  
  // 公開期間
  publishAt?: Date;              // 公開開始日時
  unpublishAt?: Date;            // 公開終了日時
}
```

#### ホスティングAPI
```typescript
// POST /api/hosting/publish
interface PublishRequest {
  lpId: string;
  settings: PublishSettings;
}

interface PublishResponse {
  success: boolean;
  publicUrl: string;             // 公開URL
  qrCode: string;               // QRコード画像
  previewUrl: string;           // プレビューURL
  errors?: string[];
}

// GET /api/hosting/stats/{lpId}
interface HostingStats {
  pageViews: number;
  uniqueVisitors: number;
  bounceRate: number;
  averageTimeOnPage: number;
  topReferrers: Array<{ domain: string; count: number }>;
  deviceBreakdown: { desktop: number; mobile: number; tablet: number };
  geographicData: Array<{ country: string; count: number }>;
}
```

#### DNS・ドメイン管理
```typescript
interface DomainSettings {
  domain: string;
  isVerified: boolean;
  verificationMethod: 'dns' | 'file';
  verificationCode: string;
  sslStatus: 'active' | 'pending' | 'failed';
  dnsRecords: DNSRecord[];
}

interface DNSRecord {
  type: 'A' | 'CNAME' | 'TXT';
  name: string;
  value: string;
  ttl: number;
}
```

### 🎨 UI/UX設計

#### 公開設定画面
```markdown
## 基本設定タブ
- 🌐 公開/非公開 トグル
- 🔗 カスタムURL設定
- 👁️ プレビューリンク
- 📱 QRコード生成

## アクセス制御タブ  
- 🔓 公開範囲設定 (パブリック/パスワード/プライベート)
- 🔐 パスワード設定
- 🌍 リファラー制限

## SEO設定タブ
- 📝 メタタイトル・説明文
- 🖼️ OGP画像設定
- 🚫 noindex設定

## 独自ドメインタブ
- 🏷️ ドメイン登録
- ✅ DNS検証状況
- 🔒 SSL証明書状況
```

#### アクセス統計ダッシュボード
```markdown
## 統計サマリー
- 📊 本日/週間/月間のPV・UU
- 📈 訪問者推移グラフ
- 🌍 地域別アクセス
- 📱 デバイス別アクセス

## リアルタイム情報
- 👁️ 現在の同時接続数
- 📊 直近の流入元
- ⏱️ 平均滞在時間
```

---

## 💰 課金・ビジネスモデル

### 🎯 機能別課金体系

| 機能 | 無料プラン | ベーシック ($19/月) | プロ ($49/月) | エンタープライズ |
|------|-----------|-------------------|---------------|----------------|
| **レター作成** | 5回/月 | 50回/月 | 無制限 | 無制限 |
| **AI対話修正** | 10回/月 | 100回/月 | 無制限 | 無制限 |
| **画像挿入** | 3枚/LP | 10枚/LP | 無制限 | 無制限 |
| **ホスティング** | 1サイト | 5サイト | 25サイト | 無制限 |
| **独自ドメイン** | ❌ | 1個 | 5個 | 無制限 |
| **アクセス統計** | 基本 | 詳細 | 高度 | カスタム |

### 💎 プレミアム機能

#### Sprint 4 で実装予定
- 🧠 **JPRO向けコンセプト理論**: オセロ理論・逆張り理論に基づく高度なコンセプト生成
- 👥 **チーム管理**: 組織アカウント、権限管理、コラボレーション
- 📊 **高度アナリティクス**: ヒートマップ、コンバージョン追跡、A/Bテスト
- 🔐 **エンタープライズセキュリティ**: SSO、監査ログ、データ暗号化

---

## 🛠️ 開発優先度

### 🔴 High Priority (Sprint 3)
1. **レター文章作成フロー** - 差別化の核心機能
2. **AI対話修正機能** - ユーザー体験の大幅向上
3. **基本的な画像挿入** - 必要最小限の機能

### 🟡 Medium Priority (Sprint 4)
1. **ツール内ホスティング** - 完全セルフサービス化
2. **高度な画像編集** - フィルター・回転等
3. **独自ドメイン対応** - ビジネス利用促進

### 🟢 Low Priority (将来)
1. **複数コンセプト提案** - 上級者向け機能
2. **JPRO向け理論統合** - ニッチ市場対応
3. **エンタープライズ機能** - 大企業向け

---

## 📊 成功指標

### 🎯 機能別KPI

| 機能 | 主要指標 | 目標値 |
|------|----------|-------|
| **レター作成** | 承認率 | >80% |
| **AI対話修正** | 修正成功率 | >90% |
| **画像挿入** | 使用率 | >60% |
| **ホスティング** | 公開率 | >70% |

### 📈 ビジネス指標
- **月次課金収益**: Sprint 4 終了時 $10,000
- **ユーザー満足度**: NPS 50以上
- **チャーン率**: <5%/月
- **機能別課金転換率**: >15%

これらの新機能により、LP Creator は業界をリードする**AI駆動ビジネス支援プラットフォーム**となります！

---

**最終更新**: 2024年12月  
**関連ドキュメント**: [10-PROJECT-MANAGEMENT-GUIDE.md](./10-PROJECT-MANAGEMENT-GUIDE.md), [11-IMPLEMENTATION-STATUS-ANALYSIS.md](./11-IMPLEMENTATION-STATUS-ANALYSIS.md), [12-AGILE-EXECUTION-GUIDE.md](./12-AGILE-EXECUTION-GUIDE.md) 