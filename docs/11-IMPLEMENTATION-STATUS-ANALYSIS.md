# 🔍 実装状況分析 ― 現在の進捗と必要な修正事項

## 📊 分析概要

LP Creator プロジェクトの現在の実装状況を **GitHub Project + Issue 管理ガイド** の計画と照らし合わせて分析し、不足している機能や修正が必要な箇所をIssue単位でまとめています。

**📅 追加機能要望**: レター文章作成、AI対話修正、画像挿入、ホスティング機能を新Sprint 3, 4として計画に反映。

## ✅ 実装済み機能の確認

### 🟢 完了済み（Good to Go）

| 機能                     | 実装状況                           | ファイル                          |
| ---------------------- | ------------------------------ | ----------------------------- |
| **Next.js 15 + TypeScript** | ✅ 正常動作                        | `package.json`, `tsconfig.json` |
| **基本的なChatPanel**      | ✅ 実装済み（リニューアル必要）             | `app/components/ChatPanel.tsx` |
| **UnifiedLPViewer**    | ✅ 実装済み（iframe化必要）              | `app/components/UnifiedLPViewer.tsx` |
| **EditModeContext**    | ✅ 編集機能の基盤                      | `app/contexts/EditModeContext.tsx` |
| **Jest + RTL環境**       | ✅ 基本設定完了                      | `jest.config.js`, `jest.setup.js` |
| **Playwright E2E**     | ✅ 設定済み                        | `playwright.config.ts` |
| **Claude Actions連携**   | ✅ 動作中                          | `.github/workflows/claude.yml` |

### 🟡 部分実装（要修正）

| 機能                  | 現在の状況                         | 必要な修正                      |
| ------------------- | ----------------------------- | -------------------------- |
| **Layout.tsx**      | 通常のNext.jsレイアウト               | V0風Grid 2-col レイアウトに変更    |
| **API Routes**      | 複数の専用API（chat, claude等）      | 統一的な `/api/generate` に集約   |
| **プレビュー表示**         | HTML文字列をそのまま表示               | iframe `srcdoc` 方式に変更       |
| **CI/CD**           | Claude Actionsのみ               | lint→test→buildの完全CI追加     |

## 🔴 未実装機能（優先度別）

### Sprint 0 関連の不足機能

| Issue | 機能                           | 現在の状況              | 必要な作業                               |
| ----- | ---------------------------- | ------------------ | ----------------------------------- |
| 0-2   | **ESLint/Prettier強化**       | 基本設定のみ             | Husky pre-commit hooks、厳格なルール設定    |
| 0-3   | **Jest環境拡充**               | 設定ファイルのみ存在         | RTLサンプルテスト作成、`npm run test:ci`対応 |
| 0-4   | **GitHub Actions CI完全版**   | Claude Actionsのみ     | lint→test→build の完全ワークフロー追加        |
| 0-5   | **Vercel Preview Hook**     | 未実装                | PR自動コメント機能追加                       |
| 0-6   | **AI Provider Interface**   | 個別Provider実装のみ      | 共通インターフェイス設計                        |
| 0-7   | **Claude Provider stub**    | 直接呼び出しのみ           | Provider抽象化、モックテスト                  |

### Sprint 1 関連の不足機能

| Issue | 機能                              | 現在の状況           | 必要な作業                              |
| ----- | ------------------------------- | --------------- | ---------------------------------- |
| 1-1   | **ChatPanel V0風リニューアル**         | 基本実装あり          | V0風デザイン、左ペイン45%レイアウト対応            |
| 1-2   | **/api/generate Edge Route**    | 複数API分散         | 統一的なgenerate APIエンドポイント作成         |
| 1-3   | **Prompt Schema & バリデーション**    | 未実装             | Zodスキーマ定義、入力検証                     |
| 1-4   | **PreviewPane iframe化**        | HTML直接表示        | iframe `srcdoc` 方式に全面変更            |
| 1-5   | **react-split-pane導入**         | 未実装             | 左右分割、リサイズ機能追加                     |
| 1-6   | **全画面/新タブボタン**                 | 未実装             | `/preview/[id]` ルート、新タブ機能追加        |
| 1-7   | **Claude streaming集約**         | 直接呼び出し          | ストリーミング対応、進捗表示                    |
| 1-8   | **Gemini Provider**             | 未実装             | Google AI Provider実装               |
| 1-9   | **E2E Smoke Test**              | 設定のみ            | 実際のE2Eテストシナリオ作成                   |

### Sprint 2 関連の未実装機能

| Issue | 機能                         | 現在の状況 | 必要な作業                    |
| ----- | -------------------------- | ----- | ------------------------ |
| 2-1   | **Image Upload**           | 未実装   | UploadThing統合、D&D機能     |
| 2-2   | **Tiptap WYSIWYG**         | 未実装   | リッチテキスト編集器導入           |
| 2-3   | **Undo/Redo機能**           | 未実装   | ヒストリ管理、キーボードショートカット   |
| 2-4   | **KV Save API**            | 未実装   | Vercel KV統合、保存API作成     |
| 2-5   | **Preview Route**          | 未実装   | `/preview/[id]` SSRページ作成 |
| 2-6   | **Metadata JSON保存**       | 未実装   | メタデータ管理機能              |
| 2-7   | **Codecov連携**             | 未実装   | テストカバレッジ表示            |
| 2-8   | **Accessibility Audit**   | 未実装   | axe-core統合、a11yテスト      |
| 2-9   | **Storybook**              | 未実装   | コンポーネントドキュメント作成       |

### ✨ Sprint 3 関連の新機能（高度機能）

| Issue | 機能                            | 現在の状況           | 必要な作業                       | 優先度   |
| ----- | ----------------------------- | --------------- | --------------------------- | ----- |
| 3-1   | **レター文章作成フロー（Epic）**        | 未実装             | ヒアリング→レター→承認→LP生成の完全フロー     | 🔴 High |
| 3-2   | **ビジネスヒアリングフォーム**           | 未実装             | 商品/サービス情報入力、バリデーション        | 🔴 High |
| 3-3   | **レター文章生成API**              | 未実装             | `/api/letter/generate` 実装    | 🔴 High |
| 3-4   | **レター承認UI**                 | 未実装             | OK/修正ボタン、LP生成開始フロー         | 🟡 Med  |
| 3-5   | **AI対話修正機能（Epic）**           | 未実装             | 完成LP→対話修正→リアルタイム反映         | 🔴 High |
| 3-6   | **修正チャットパネル**               | 未実装             | LP表示状態での修正専用チャット          | 🔴 High |
| 3-7   | **差分適用API**                 | 未実装             | `/api/modify` 修正指示→差分適用     | 🔴 High |
| 3-8   | **修正履歴管理**                  | 未実装             | 修正ステップ保存、Undo/Redo対応       | 🟡 Med  |
| 3-9   | **ノーコード画像挿入（Epic）**         | 未実装             | マウスUI画像挿入、D&D、リサイズ         | 🟡 Med  |
| 3-10  | **画像ドロップゾーン**               | 未実装             | LP上への画像D&D、プレースホルダー表示     | 🟡 Med  |
| 3-11  | **画像操作ツールバー**               | 未実装             | リサイズ、位置調整、削除ボタン           | 🟢 Low  |

### 🌊 Sprint 4 関連の将来機能（本格運用）

| Issue | 機能                      | 現在の状況 | 必要な作業                    | 優先度   |
| ----- | ----------------------- | ----- | ------------------------ | ----- |
| 4-1   | **ツール内ホスティング（Epic）**   | 未実装   | LP公開→独自URL生成→即アクセス可能    | 🟡 Med  |
| 4-2   | **公開設定UI**             | 未実装   | パブリック/プライベート、パスワード保護   | 🟡 Med  |
| 4-3   | **独自ドメイン対応**           | 未実装   | カスタムドメイン設定、DNS管理        | 🟢 Low  |
| 4-4   | **アクセス解析ダッシュボード**      | 未実装   | PV、CV、デバイス別統計表示         | 🟢 Low  |
| 4-5   | **複数コンセプト提案（Epic）**    | 未実装   | オセロ理論、逆張り理論に基づく多角度提案   | 🟢 Low  |
| 4-6   | **JPRO向けコンセプト理論**     | 未実装   | 専門的コンセプト生成、差別化戦略提案     | 🟢 Low  |
| 4-7   | **チーム管理機能**            | 未実装   | Auth + RLS、組織アカウント      | 🟢 Low  |
| 4-8   | **エンタープライズAPI料金制限**   | 未実装   | 使用量ベース課金、クレジット制限       | 🟢 Low  |

## 🛠️ 優先修正事項（Issue形式）

### 🔴 High Priority（今すぐ着手）

#### Issue #URGENT-01: Layout.tsx V0風Grid変更
**Label**: `feat, design, ui`
**Priority**: High
**Time**: 2h

**現在の問題**:
```typescript
// app/layout.tsx (現在)
<body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
  {children}
</body>
```

**必要な修正**:
```typescript
// app/layout.tsx (修正後)
<body className="grid h-screen grid-cols-[1fr_1.2fr] overflow-hidden">
  {children}
</body>
```

**受け入れ条件**:
- [ ] 左ペイン45%、右ペイン55%で表示
- [ ] 画面全体がh-screenで高さ固定
- [ ] overflow-hiddenでスクロール制御

#### Issue #URGENT-02: API統合 - /api/generate作成
**Label**: `feat, api`
**Priority**: High
**Time**: 4h

**現在の問題**:
- `app/api/chat/`, `app/api/claude/` 等が分散
- 統一的なLP生成エンドポイントが存在しない

**必要な修正**:
```typescript
// app/api/generate/route.ts (新規作成)
export async function POST(req: Request) {
  const { prompt, provider = 'openai' } = await req.json();
  // 統一的なLP生成ロジック
  return Response.json({ html, css, metadata });
}
```

**受け入れ条件**:
- [ ] POST `/api/generate` でLP生成
- [ ] provider切り替え対応（openai/claude/gemini）
- [ ] `{html, css, metadata}` 形式で返却

#### Issue #URGENT-03: PreviewPane iframe化
**Label**: `feat, ui`
**Priority**: High
**Time**: 3h

**現在の問題**:
```typescript
// app/components/UnifiedLPViewer.tsx (現在)
<div dangerouslySetInnerHTML={{ __html: lpHtml }} />
```

**必要な修正**:
```typescript
// app/components/PreviewPane.tsx (新規作成)
<iframe 
  srcDoc={`<style>${css}</style>${html}`}
  className="w-full h-full border-0"
  sandbox="allow-scripts allow-same-origin"
/>
```

**受け入れ条件**:
- [ ] iframe sandboxで安全な表示
- [ ] CSS/HTMLの動的更新対応
- [ ] フルサイズ表示（w-full h-full）

### 🟡 Medium Priority（1週間以内）

#### Issue #MED-01: react-split-pane導入
**Label**: `feat, ui`
**Priority**: Medium
**Time**: 3h

**必要な作業**:
```bash
npm install react-split-pane-next
```

```typescript
// app/page.tsx
import Split from 'react-split-pane-next/lib/Pane';

<Split className="h-full" sizes={[45, 55]} minSize={200}>
  <ChatPanel />
  <PreviewPane />
</Split>
```

#### Issue #MED-02: GitHub Actions CI完全版
**Label**: `chore, infra`
**Priority**: Medium
**Time**: 4h

**必要な作業**:
```yaml
# .github/workflows/ci.yml (新規作成)
name: CI
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
      - run: npm ci
      - run: npm run lint
      - run: npm run test
      - run: npm run build
```

#### Issue #MED-03: Provider Interface設計
**Label**: `feat, api`
**Priority**: Medium
**Time**: 4h

**必要な作業**:
```typescript
// src/llm/provider.ts (新規作成)
interface ILLMProvider {
  complete(prompt: string): Promise<{html: string, css: string}>;
  stream(prompt: string): AsyncIterable<string>;
}

class OpenAIProvider implements ILLMProvider { /* ... */ }
class ClaudeProvider implements ILLMProvider { /* ... */ }
```

### 🟢 Low Priority（2週間以内）

#### Issue #LOW-01: ESLint/Prettier強化
**Label**: `chore, dx`
**Priority**: Low
**Time**: 2h

#### Issue #LOW-02: Jest環境拡充
**Label**: `test, chore`
**Priority**: Low
**Time**: 3h

#### Issue #LOW-03: E2Eテスト作成
**Label**: `test, e2e`
**Priority**: Low
**Time**: 4h

## 📈 実装進捗サマリー（更新版）

### Sprint 0 進捗: 30% 完了
- ✅ **Next.js 15 環境**: 完了
- ✅ **基本Jest設定**: 完了
- 🔄 **ESLint強化**: 部分完了
- ❌ **GitHub Actions CI**: 未着手
- ❌ **Provider Interface**: 未着手

### Sprint 1 進捗: 20% 完了
- 🔄 **ChatPanel**: 基本実装あり（要リニューアル）
- ❌ **API統合**: 未着手
- ❌ **PreviewPane**: 要全面変更
- ❌ **分割レイアウト**: 未着手

### Sprint 2 進捗: 0% 完了
- ❌ **全機能未実装**

### ✨ Sprint 3 進捗: 0% 完了（新機能）
- ❌ **レター文章作成フロー**: 要設計・実装
- ❌ **AI対話修正機能**: 要設計・実装
- ❌ **ノーコード画像挿入**: 要設計・実装

### 🌊 Sprint 4 進捗: 0% 完了（将来機能）
- ❌ **ツール内ホスティング**: 要設計・実装
- ❌ **複数コンセプト提案**: 要設計・実装
- ❌ **エンタープライズ機能**: 要設計・実装

## 🚀 拡張されたアクションプラン（4スプリント対応）

### Phase 1: MVP基盤構築（Sprint 0-2）
**期間**: 3週間
**目標**: 基本LP生成機能の完成

### Phase 2: 高度機能実装（Sprint 3）
**期間**: 2週間
**目標**: レター作成、AI修正、画像挿入機能

### Phase 3: 本格運用（Sprint 4）
**期間**: 3週間
**目標**: ホスティング、料金体系、エンタープライズ機能

### Week 1-2: 基盤修正（緊急対応）
1. **Layout.tsx修正** (Day 1)
2. **API統合** (Day 2-3)
3. **PreviewPane iframe化** (Day 4)
4. **CI/CD追加** (Day 5)

### Week 3-4: コア機能実装
1. **react-split-pane導入** (Day 1)
2. **Provider Interface設計** (Day 2-3)
3. **全画面機能追加** (Day 4)
4. **E2Eテスト作成** (Day 5)

### Week 5-6: Sprint 3 高度機能（重要）
1. **レター文章作成フロー** (Day 1-3)
2. **AI対話修正機能** (Day 4-6)
3. **ノーコード画像挿入** (Day 7-8)

## 💡 新機能の技術要件

### レター文章作成機能
```typescript
// 期待するAPI仕様
POST /api/letter/generate
{
  businessInfo: {
    product: string,
    target: string,
    problems: string[],
    solutions: string[]
  }
}

Response: {
  letter: string,
  concept: string,
  suggestions: string[]
}
```

### AI対話修正機能
```typescript
// 期待するAPI仕様  
POST /api/modify
{
  currentHtml: string,
  currentCss: string,
  modificationRequest: string,
  context: LPContext
}

Response: {
  modifiedHtml: string,
  modifiedCss: string,
  changes: Change[],
  explanation: string
}
```

### 課金・クレジット制限
```typescript
// 期待するAPI仕様
GET /api/user/credits
Response: {
  remaining: number,
  used: number,
  limit: number,
  resetDate: string
}
```

## 📋 まとめ

**現在の実装状況**: 約25%完了（基盤のみ）
**緊急対応必要**: 3件のHigh Priority Issue
**新機能追加**: Sprint 3, 4 で高度機能・本格運用機能
**完全MVP達成まで**: 約6-8週間の集中開発が必要

**推奨次ステップ**:
1. High Priority Issueの即座着手
2. Sprint 3 新機能の詳細設計
3. GitHub Projectボード拡張（Sprint 3, 4対応）
4. 日次スタンドアップ開始

---

**最終更新**: 2024年12月  
**関連ドキュメント**: [10-PROJECT-MANAGEMENT-GUIDE.md](./10-PROJECT-MANAGEMENT-GUIDE.md), [09-V0-UIUX-SPECIFICATION.md](./09-V0-UIUX-SPECIFICATION.md) 