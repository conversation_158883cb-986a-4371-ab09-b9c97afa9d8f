# 🎯 V0 UI/UX仕様書 ――「左チャット × 右プレビュー」レイアウト

## 📋 概要

LP Creator の V0 UI/UX は、V0/Claude Artifacts 風の「左チャット × 右プレビュー」レイアウトを採用します。
ユーザーが左ペインでLP生成指示を入力し、右ペインでリアルタイムプレビューを確認できる直感的なインターフェースです。

## 🖼️ レイアウト構成

| 位置              | 役割                                                                                                                   | 技術ポイント                                                                                                                 |
| --------------- | -------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------- |
| **左ペイン（45%）**  | チャット / ヒアリング・修正指示                                                                                                    | - `<ChatPanel>` コンポーネント<br>- `useCompletion` or RSC Action で送信<br>- Markdown & コードハイライト対応                              |
| **右ペイン（55%）**  | LP プレビュー（HTML/CSS）                                                                                                   | - `<PreviewPane>` に **iframe** 埋め込み<br>- iframe `srcdoc` に生成 HTML を流し込み（安全：同一オリジン）<br>- Tailwind JIT を避け、プレーン CSS 埋め込み |
| **リサイズ & 分離表示** | 1. **ドラッグで幅調整**: `react-split-pane` or 自作 `ResizeObserver`<br>2. **🔍 全画面ボタン**: `window.open('/preview/[id]')` で新タブへ | - 分離先は `app/preview/[id]/page.tsx`（SSG）<br>- 共有用 URL 発行                                                                |

## 🏗️ コンポーネント構成（Next.js / App Router）

### ディレクトリ構造
```
app/
 ├─ layout.tsx          ← CSS Grid 2-col
 ├─ page.tsx            ← <ChatPanel> + <PreviewPane>
 ├─ api/
 │   └─ generate/
 └─ preview/[id]/page.tsx
```

### レイアウト実装例
```tsx
// layout.tsx
export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html>
      <body className="grid h-screen grid-cols-[1fr_1.2fr] overflow-hidden">
        {children}
      </body>
    </html>
  );
}
```

## 📱 プレビューの 3 つの拡大パターン

| パターン                | 実装                                | ユースケース               |
| ------------------- | --------------------------------- | -------------------- |
| **A. ペイン拡大**        | `react-split-pane` の `size=100%`  | サクッと画面を広げたい          |
| **B. モーダル全画面**      | `<Dialog><iframe ... /></Dialog>` | スクロールしながら確認          |
| **C. 別タブ / 別ウィンドウ** | `window.open('/preview/xyz')`     | Zoom 100% 等倍でデザイン確認 |

### プレビュー拡大の技術的メリット
- **レスポンシブ確認**: 実際のブラウザサイズで表示テスト
- **共有可能URL**: 生成されたLPの独立したプレビューURL
- **デバイステスト**: スマートフォン・タブレット表示確認

## ☁️ Cloud アーティファクトの扱い

| 対象                     | 保存先                                     | 生成タイミング                 |
| ---------------------- | --------------------------------------- | ----------------------- |
| HTML / CSS / Meta JSON | **Vercel KV**（MVP）<br>将来 Supabase (RLS) | 「Publish」ボタン押下時         |
| サムネイル (OGP)            | Vercel Blob or Supabase Storage         | uploadThing ➔ CDN化     |
| デプロイ URL               | Vercel Preview / Production             | `vercel deploy` Webhook |

### クラウド保存のメリット
- **KV** なら Write-Heavy な生成フェーズでも低レイテンシ
- Public Preview 用 URL（`lp-ai.vercel.app/preview/abc123`）を簡単共有
- 後から CI/CD で本番 `/lp/awesome-product` へ Promote 可能

## 💻 最小コード例：チャット入力 → 右プレビュー更新

```tsx
// app/page.tsx (抜粋)
'use client';
import { useCompletion } from 'ai/react';
import Split from 'react-split';

export default function Home() {
  const { complete, completion } = useCompletion({
    api: '/api/generate',
    onFinish() {
      // Completion は { html, css } を返す前提
      const { html, css } = JSON.parse(completion);
      setSrcDoc(`<style>${css}</style>${html}`);
    },
  });

  return (
    <Split className="h-full" sizes={[45, 55]} minSize={200}>
      <ChatPanel onSubmit={complete} />
      <PreviewPane srcDoc={srcDoc} />
    </Split>
  );
}
```

### 主要コンポーネント詳細

#### 1. ChatPanel コンポーネント
```tsx
interface ChatPanelProps {
  onSubmit: (prompt: string) => void;
  messages: Message[];
  isLoading: boolean;
}
```

**機能:**
- メッセージ履歴表示
- Markdown レンダリング
- コードハイライト
- 自動スクロール

#### 2. PreviewPane コンポーネント
```tsx
interface PreviewPaneProps {
  srcDoc: string;
  onFullscreen?: () => void;
  onShare?: () => void;
}
```

**機能:**
- iframe による安全なプレビュー表示
- 全画面表示ボタン
- 共有URLコピー機能
- レスポンシブプレビュー切り替え

## 🚀 実装タスクリスト

| # | タスク                                    | 目安     | 優先度 |
| - | -------------------------------------- | ------ | --- |
| 1 | **Grid レイアウト** + `react-split-pane` 導入 | 1h     | 🔴 高  |
| 2 | `PreviewPane` に `iframe srcdoc` 実装     | 1h     | 🔴 高  |
| 3 | 右上に **🔍 全画面ボタン**（別タブ）追加               | 30min  | 🟡 中  |
| 4 | `/preview/[id]` ルートで静的 HTML 返却         | 2h     | 🟡 中  |
| 5 | KV に `{id, html, css, meta}` 保存 API    | 1h     | 🟡 中  |
| 6 | レスポンシブプレビュー切り替え                        | 1h     | 🟢 低  |
| 7 | プレビューURL共有機能                           | 30min  | 🟢 低  |

### 追加検討事項
- **アクセシビリティ**: キーボードナビゲーション対応
- **パフォーマンス**: 大きなHTMLファイルのプレビュー最適化
- **エラーハンドリング**: 生成失敗時のフォールバック表示

## 🎨 UI/UX デザイン原則

### 1. シンプル & 直感的
- 最小限のUI要素でマキシマムな機能性
- 1クリックでアクセスできる重要機能
- 視覚的階層の明確化

### 2. レスポンシブ対応
- モバイルファーストアプローチ
- タブレット・デスクトップでの最適表示
- 縦横比に応じた自動レイアウト調整

### 3. パフォーマンス重視
- 軽量なコンポーネント設計
- 適切なメモ化とレンダリング最適化
- プレビューの遅延読み込み対応

## 🔄 今後の拡張計画

### フェーズ 1: 基本機能完成
- 左右分割レイアウト
- 基本的なプレビュー機能
- 簡単な保存・共有

### フェーズ 2: 高度なプレビュー
- デバイスプレビュー切り替え
- インタラクション付きプレビュー
- A/Bテストプレビュー

### フェーズ 3: コラボレーション
- リアルタイム共同編集
- コメント・フィードバック機能
- バージョン管理

---

**✅ ここまでで**: 「左チャット → 生成 → 右プレビュー → 拡大／別タブ → 保存&再訪」が完全に通ります。

**💡 重要ポイント**: "Cloud のアーティファクト" = **KV + Blob + Deploy URL** に一旦集約し、将来 Supabase 移行で権限やチーム機能を強化する想定です。

---

**最終更新**: 2024年12月  
**関連ドキュメント**: [01-ARCHITECTURE.md](./01-ARCHITECTURE.md), [02-UNIFIED-LP-GENERATOR.md](./02-UNIFIED-LP-GENERATOR.md) 