# Clean Code & Clean Architecture Guidelines

## DRY原則 (Don't Repeat Yourself)
- 同じスタイルオブジェクトを複数箇所で定義しない
- 共通のスタイル定数を作成して再利用する
- 似たような関数やコンポーネントロジックは統合する
- マジックナンバーや文字列は定数として定義する

## Clean Code原則
### 関数・メソッド
- 1つの関数は1つの責任のみを持つ（Single Responsibility Principle）
- 関数は短く保つ（20行以内を目安）
- 引数は3つ以下に制限
- 意味のある関数名を使用する

### 変数・定数
- 意味のある変数名を使用
- boolean変数は`is`, `has`, `can`で始める
- 定数は`UPPER_SNAKE_CASE`で命名
- スコープを最小限に保つ

### コンポーネント設計
- コンポーネントは単一責任を持つ
- propsは明確に型定義する
- 巨大なコンポーネントは小さなコンポーネントに分割
- 状態管理ロジックとUI表示ロジックを分離

## Clean Architecture原則
### レイヤー分離
- UI層: コンポーネント、ページ
- ビジネスロジック層: hooks、utils
- データ層: API、状態管理

### 依存関係の方向
- 内側のレイヤーは外側のレイヤーに依存しない
- インターフェースを通じて依存関係を逆転

### ファイル構造
```
app/
├── components/     # UI コンポーネント
├── hooks/         # カスタムフック
├── utils/         # ユーティリティ関数
├── types/         # 型定義
├── constants/     # 定数
└── contexts/      # React Context
```

## 具体的なルール

### スタイリング
- インラインスタイルは最小限に
- 共通スタイルは定数として抽出
- CSS-in-JSまたはCSS Modulesを活用
- レスポンシブ対応を考慮

### 型安全性
- 全ての関数・変数に型を明示
- anyの使用を避ける
- interface/typeで明確な契約を定義

### エラーハンドリング
- try-catchブロックで適切にエラーを処理
- ユーザーフレンドリーなエラーメッセージ
- ログ出力で問題の特定を容易に

### テスタビリティ
- 純粋関数を優先
- 副作用を最小限に
- モック可能な設計

## コミットルール
- feat: 新機能追加
- fix: バグ修正
- refactor: リファクタリング
- style: コードフォーマット
- test: テスト追加・修正
- docs: ドキュメント更新

## 現在のコードベースで改善すべき点
- app/claude/page.tsx: 巨大なコンポーネントを分割
- 重複するスタイルオブジェクトの統合
- renderToolResult関数の分割
- ナビゲーションバーの独立コンポーネント化