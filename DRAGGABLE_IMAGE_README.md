# DraggableImage Component - 完全版

## 🎯 概要

`DraggableImage`は、マウス・タッチ・キーボード操作によるドラッグ&ドロップとリサイズ機能を備えた、フル機能のReact画像コンポーネントです。アクセシビリティ、パフォーマンス、エラーハンドリングを重視した設計となっています。

## ✨ 主要機能

### 🖱️ マルチデバイス対応
- **マウス操作**: ドラッグ&ドロップ、リサイズハンドル
- **タッチ操作**: 長押し判定、タッチドラッグ、モバイル最適化
- **キーボード操作**: 矢印キー移動、Shift+矢印キーリサイズ

### ♿ アクセシビリティ対応
- **WCAG 2.1 AA準拠**
- **ARIA属性**: role, aria-label, aria-selected など
- **キーボードナビゲーション**: Tab、矢印キー、Escape
- **スクリーンリーダー対応**: 動作のアナウンス機能
- **フォーカス管理**: 視覚的フォーカスインジケーター

### 🚀 パフォーマンス最適化
- **requestAnimationFrame**: スムーズなアニメーション
- **デバウンス処理**: 高頻度イベントの最適化
- **メモリリーク防止**: 適切なイベントリスナークリーンアップ
- **遅延読み込み**: 画像のlazy loading

### 🛡️ エラーハンドリング
- **画像読み込みエラー**: フォールバック画像表示
- **データ検証**: 型安全な画像データ処理
- **境界値チェック**: 最小サイズ、画面外移動の制限
- **カスタムエラー**: ImageErrorクラスによる詳細なエラー情報

## 📁 ファイル構成

```
app/
├── components/
│   └── DraggableImage.tsx          # メインコンポーネント
├── hooks/
│   ├── useDraggable.ts             # ドラッグ機能フック
│   ├── useResizable.ts             # リサイズ機能フック
│   └── useKeyboardControls.ts      # キーボード操作フック
├── types/
│   └── image.ts                    # 型定義
├── constants/
│   └── draggable.ts               # 定数・設定値
├── utils/
│   ├── imageError.ts              # エラーハンドリング
│   └── accessibility.ts          # アクセシビリティ支援
└── contexts/
    └── EditModeContext.tsx        # 編集モード管理
```

## 🔧 使用方法

### 基本的な使用

```tsx
import { DraggableImage } from './app/components/DraggableImage';
import { EditModeProvider } from './app/contexts/EditModeContext';

function App() {
  const imageData = {
    url: 'https://example.com/image.jpg',
    alt: '説明テキスト',
    position: { x: 100, y: 100 },
    size: { width: 300, height: 200 },
    zIndex: 1,
  };

  return (
    <EditModeProvider>
      <DraggableImage
        imageId="unique-image-id"
        imageData={imageData}
        onImageChange={(updatedData) => {
          console.log('画像が更新されました:', updatedData);
        }}
        onError={(error) => {
          console.error('画像エラー:', error);
        }}
      />
    </EditModeProvider>
  );
}
```

### 高度な使用例

```tsx
import { DraggableImage } from './app/components/DraggableImage';
import { useEditMode } from './app/contexts/EditModeContext';

function ImageEditor() {
  const { isEditMode, toggleEditMode } = useEditMode();
  
  const handleImageChange = (imageData) => {
    // 画像の状態変更を処理
    saveImageToDatabase(imageData);
  };

  const handleImageError = (error) => {
    // エラーログを送信
    sendErrorReport(error);
    // ユーザーに通知
    showNotification('画像の読み込みに失敗しました');
  };

  return (
    <div>
      <button onClick={toggleEditMode}>
        {isEditMode ? '編集終了' : '編集開始'}
      </button>
      
      <DraggableImage
        imageId="hero-image"
        imageData={heroImageData}
        onImageChange={handleImageChange}
        onError={handleImageError}
        className="hero-image"
        style={{ border: '2px solid #ccc' }}
      />
    </div>
  );
}
```

## 🎮 操作方法

### マウス操作
- **ドラッグ**: 画像をクリック&ドラッグ
- **リサイズ**: ハンドルをドラッグ
- **選択**: 画像をクリック
- **アスペクト比保持**: Shift+ドラッグ

### タッチ操作
- **ドラッグ**: 長押し（500ms）後にドラッグ
- **選択**: タップ

### キーボード操作
- **移動**: 矢印キー（10px単位）
- **リサイズ**: Shift+矢印キー（10px単位）
- **選択解除**: Escape
- **選択**: Enter/Space

## ⚙️ 設定とカスタマイズ

### 定数の変更

`app/constants/draggable.ts`で各種設定を変更できます：

```typescript
export const DRAGGABLE_CONSTANTS = {
  MIN_SIZE: 50,                    // 最小サイズ
  DEFAULT_SIZE: { width: 300, height: 200 },
  HANDLE_SIZE: 8,                  // ハンドルサイズ
  BORDER_WIDTH: 2,                 // 選択時のボーダー幅
  Z_INDEX: {
    DEFAULT: 1,
    SELECTED: 1000,                // 選択時のz-index
    HANDLE: 10,
    INDICATOR: 11,
  },
} as const;
```

### カスタムテーマ

```typescript
export const COLORS = {
  PRIMARY: '#2196f3',              // メインカラー
  WHITE: '#ffffff',
  TRANSPARENT: 'transparent',
} as const;
```

### キーボード操作のカスタマイズ

`app/hooks/useKeyboardControls.ts`で移動量を変更：

```typescript
const KEYBOARD_MOVE_STEP = 10;     // 矢印キーでの移動量
const KEYBOARD_RESIZE_STEP = 10;   // リサイズ量
```

## 🧪 テスト

### ユニットテスト実行

```bash
npm test -- tests/unit/DraggableImage.test.tsx
```

### E2Eテスト実行

```bash
npm run test:e2e -- tests/e2e/draggable-image.spec.ts
```

### テストカバレッジ

- ✅ レンダリング
- ✅ マウス操作
- ✅ タッチ操作
- ✅ キーボード操作
- ✅ エラーハンドリング
- ✅ アクセシビリティ
- ✅ パフォーマンス
- ✅ メモリリーク

## 📊 パフォーマンス指標

### 推奨指標
- **初回レンダリング**: < 100ms
- **ドラッグ応答性**: < 16ms（60fps）
- **メモリ使用量**: 安定（リークなし）
- **ネットワーク**: 遅延読み込み対応

### 最適化技術
- requestAnimationFrame使用
- イベントリスナーの適切なクリーンアップ
- デバウンス処理
- 画像プリロード

## 🔒 セキュリティ

### 入力検証
- URL検証
- XSS防止
- 型安全性の確保

### エラー情報
- 詳細なエラー情報（開発時）
- 安全なエラーメッセージ（本番時）

## 📝 型定義

### ImageData

```typescript
interface ImageData {
  url: string;                     // 必須: 画像URL
  alt: string;                     // 必須: alt属性
  photographer?: string;           // 写真家情報
  description?: string;            // 詳細説明
  width?: number;                  // 元画像幅
  height?: number;                 // 元画像高さ
  position?: {                     // 表示位置
    x: number;
    y: number;
  };
  size?: {                         // 表示サイズ
    width: number;
    height: number;
  };
  zIndex?: number;                 // 重なり順序
}
```

### DraggableImageProps

```typescript
interface DraggableImageProps {
  imageId: string;                 // 必須: 一意ID
  imageData: ImageData | null;     // 画像データ
  onImageChange?: (imageData: ImageData) => void;  // 変更時コールバック
  className?: string;              // CSSクラス
  style?: React.CSSProperties;     // インラインスタイル
  children?: React.ReactNode;      // 子要素
  onError?: (error: Error) => void; // エラー時コールバック
}
```

## 🐛 既知の問題と解決済み問題

### ✅ 解決済み
- ~~メモリリーク（useEffectのクリーンアップ不備）~~
- ~~タッチイベント未対応~~
- ~~型安全性の問題~~
- ~~アクセシビリティ不備~~
- ~~パフォーマンス問題~~
- ~~エラーハンドリング不足~~

### 🔄 制限事項
- IE11未対応（モダンブラウザのみ）
- 大量画像（100枚以上）でのパフォーマンス影響
- プレースホルダー画像の要手動設定

## 🤝 コントリビューション

1. フォークしてブランチを作成
2. 変更を実装
3. テストを追加・実行
4. プルリクエストを作成

### 開発ガイドライン
- TypeScript必須
- ESLint/Prettier準拠
- 100%テストカバレッジ
- アクセシビリティ要件遵守
- パフォーマンス要件達成

## 📄 ライセンス

MIT License

## 🔗 関連リソース

- [React公式ドキュメント](https://react.dev/)
- [WCAG 2.1 ガイドライン](https://www.w3.org/WAI/WCAG21/quickref/)
- [TypeScript公式ドキュメント](https://www.typescriptlang.org/)
- [Playwright テストガイド](https://playwright.dev/)

---

**最終更新**: 2024年12月
**バージョン**: 2.0.0
**ステータス**: 本番準備完了 ✅ 