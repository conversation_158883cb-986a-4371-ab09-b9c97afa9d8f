// tests/unit/feature-flags.test.ts
import { getFeatureFlags } from '../../app/mastra/config/feature-flags';

describe('Feature Flags System', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.resetModules();
    process.env = { ...originalEnv };
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  describe('getFeatureFlags', () => {
    it('should return all flags as false when no environment variables are set', () => {
      delete process.env.ENABLE_MASTRA;
      delete process.env.MASTRA_AGENT_TOOLS;
      delete process.env.MASTRA_MEMORY;
      delete process.env.MASTRA_WORKFLOWS;
      delete process.env.MASTRA_AB_TEST;

      const flags = getFeatureFlags();

      expect(flags).toEqual({
        ENABLE_MASTRA: false,
        MASTRA_AGENT_TOOLS: false,
        MASTRA_MEMORY_SYSTEM: false,
        MASTRA_WORKFLOW_ENGINE: false,
        MASTRA_AB_TEST: false
      });
    });

    it('should return all flags as true when environment variables are set to "true"', () => {
      process.env.ENABLE_MASTRA = 'true';
      process.env.MASTRA_AGENT_TOOLS = 'true';
      process.env.MASTRA_MEMORY = 'true';
      process.env.MASTRA_WORKFLOWS = 'true';
      process.env.MASTRA_AB_TEST = 'true';

      const flags = getFeatureFlags();

      expect(flags).toEqual({
        ENABLE_MASTRA: true,
        MASTRA_AGENT_TOOLS: true,
        MASTRA_MEMORY_SYSTEM: true,
        MASTRA_WORKFLOW_ENGINE: true,
        MASTRA_AB_TEST: true
      });
    });

    it('should return flags as false when environment variables are set to non-"true" values', () => {
      process.env.ENABLE_MASTRA = 'false';
      process.env.MASTRA_AGENT_TOOLS = '1';
      process.env.MASTRA_MEMORY = 'yes';
      process.env.MASTRA_WORKFLOWS = '';
      process.env.MASTRA_AB_TEST = 'True'; // case sensitive

      const flags = getFeatureFlags();

      expect(flags).toEqual({
        ENABLE_MASTRA: false,
        MASTRA_AGENT_TOOLS: false,
        MASTRA_MEMORY_SYSTEM: false,
        MASTRA_WORKFLOW_ENGINE: false,
        MASTRA_AB_TEST: false
      });
    });

    it('should handle mixed environment variable states', () => {
      process.env.ENABLE_MASTRA = 'true';
      process.env.MASTRA_AGENT_TOOLS = 'false';
      process.env.MASTRA_MEMORY = 'true';
      delete process.env.MASTRA_WORKFLOWS;
      process.env.MASTRA_AB_TEST = 'true';

      const flags = getFeatureFlags();

      expect(flags).toEqual({
        ENABLE_MASTRA: true,
        MASTRA_AGENT_TOOLS: false,
        MASTRA_MEMORY_SYSTEM: true,
        MASTRA_WORKFLOW_ENGINE: false,
        MASTRA_AB_TEST: true
      });
    });
  });
});