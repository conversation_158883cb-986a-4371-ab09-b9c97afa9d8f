import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { DraggableImage } from '../../app/components/DraggableImage';
import { EditModeProvider } from '../../app/contexts/EditModeContext';
import { ImageData } from '../../app/types/image';

// Test utilities
const mockImageData: ImageData = {
  url: 'https://example.com/test-image.jpg',
  alt: 'Test image',
  width: 300,
  height: 200,
  position: { x: 10, y: 20 },
  size: { width: 300, height: 200 },
  zIndex: 1,
};

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <EditModeProvider>
      {component}
    </EditModeProvider>
  );
};

describe('DraggableImage', () => {
  beforeEach(() => {
    // Reset window size for consistent testing
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });
    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: 768,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders image with correct attributes', () => {
      renderWithProvider(
        <DraggableImage
          imageId="test-image"
          imageData={mockImageData}
        />
      );

      const image = screen.getByAltText('Test image');
      expect(image).toBeInTheDocument();
      expect(image).toHaveAttribute('src', mockImageData.url);
      expect(image).toHaveAttribute('draggable', 'false');
    });

    it('renders placeholder when imageData is null', () => {
      renderWithProvider(
        <DraggableImage
          imageId="test-image"
          imageData={null}
        />
      );

      // Should render placeholder instead of crashing
      expect(screen.queryByRole('img')).not.toBeInTheDocument();
    });

    it('renders fallback image on load error', async () => {
      const onError = jest.fn();
      renderWithProvider(
        <DraggableImage
          imageId="test-image"
          imageData={mockImageData}
          onError={onError}
        />
      );

      const image = screen.getByAltText('Test image');
      fireEvent.error(image);

      await waitFor(() => {
        expect(onError).toHaveBeenCalled();
      });
    });
  });

  describe('Edit Mode', () => {
    it('shows controls when in edit mode and hovered', () => {
      // This test would require mocking the edit mode context
      // Implementation depends on your testing setup
    });

    it('applies correct accessibility attributes', () => {
      renderWithProvider(
        <DraggableImage
          imageId="test-image"
          imageData={mockImageData}
        />
      );

      const container = screen.getByRole('img').parentElement;
      expect(container).toHaveAttribute('tabIndex', '-1');
    });
  });

  describe('Drag and Drop', () => {
    it('handles mouse down events correctly', () => {
      const onImageChange = jest.fn();
      renderWithProvider(
        <DraggableImage
          imageId="test-image"
          imageData={mockImageData}
          onImageChange={onImageChange}
        />
      );

      const container = screen.getByRole('img').parentElement;
      
      // Mouse events would need edit mode to be active
      fireEvent.mouseDown(container!, { clientX: 100, clientY: 100 });
      
      // Test would verify drag behavior
    });

    it('handles touch events for mobile', () => {
      const onImageChange = jest.fn();
      renderWithProvider(
        <DraggableImage
          imageId="test-image"
          imageData={mockImageData}
          onImageChange={onImageChange}
        />
      );

      const container = screen.getByRole('img').parentElement;
      
      fireEvent.touchStart(container!, {
        touches: [{ clientX: 100, clientY: 100 }],
      });
      
      // Test would verify touch behavior
    });
  });

  describe('Keyboard Controls', () => {
    it('handles arrow key navigation', () => {
      // This test would verify keyboard navigation
      // when the image is selected in edit mode
    });

    it('handles escape key to deselect', () => {
      // This test would verify escape key behavior
    });
  });

  describe('Resize Functionality', () => {
    it('shows resize handles when selected', () => {
      // This test would verify resize handles appear
      // when image is selected in edit mode
    });

    it('handles resize operations correctly', () => {
      // This test would verify resize behavior
    });

    it('maintains aspect ratio with shift key', () => {
      // This test would verify aspect ratio preservation
    });
  });

  describe('Error Handling', () => {
    it('calls onError callback when image fails to load', async () => {
      const onError = jest.fn();
      renderWithProvider(
        <DraggableImage
          imageId="test-image"
          imageData={mockImageData}
          onError={onError}
        />
      );

      const image = screen.getByAltText('Test image');
      fireEvent.error(image);

      await waitFor(() => {
        expect(onError).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'ImageError',
          })
        );
      });
    });

    it('validates image data correctly', () => {
      const invalidImageData = { ...mockImageData, url: '' };
      
      renderWithProvider(
        <DraggableImage
          imageId="test-image"
          imageData={invalidImageData}
        />
      );

      // Should render fallback image
      const image = screen.getByAltText('画像が読み込めませんでした');
      expect(image).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('does not cause memory leaks', () => {
      const { unmount } = renderWithProvider(
        <DraggableImage
          imageId="test-image"
          imageData={mockImageData}
        />
      );

      // Should cleanup event listeners on unmount
      unmount();
      
      // No specific assertion needed - test would fail if memory leaks exist
    });
  });
}); 