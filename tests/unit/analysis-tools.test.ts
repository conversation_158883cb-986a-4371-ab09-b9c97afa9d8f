import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { analyzeRequirements } from '../../mastra/tools/analysis-tools';
import { ProjectMemoryManager } from '../../mastra/memory/project-memory';

// Mock the memory manager
jest.mock('../../mastra/memory/project-memory');

describe('Analysis Tools', () => {
  let mockMemoryManager: jest.Mocked<ProjectMemoryManager>;

  beforeEach(() => {
    mockMemoryManager = {
      saveRequirementAnalysis: jest.fn(),
      getRequirementAnalysis: jest.fn(),
      findSimilarPatterns: jest.fn(),
      saveProjectSnapshot: jest.fn(),
      getProjectSnapshots: jest.fn(),
      saveGenerationPattern: jest.fn(),
      saveUserAction: jest.fn(),
      getUserActions: jest.fn(),
    } as any;

    (ProjectMemoryManager as jest.MockedClass<typeof ProjectMemoryManager>).mockImplementation(() => mockMemoryManager);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('analyzeRequirements', () => {
    it('should analyze business requirements and return strategy', async () => {
      const analysisParams = {
        businessType: 'SaaS',
        targetAudience: 'Small business owners',
        goals: ['Increase conversions', 'Build trust'],
        existingBrand: 'TechCorp',
        competitors: 'Salesforce, HubSpot',
      };

      const mockContext = {
        sessionId: 'test-session-1',
        parameters: analysisParams,
      };

      mockMemoryManager.saveRequirementAnalysis.mockResolvedValue({
        id: 'analysis-1',
        success: true,
      });

      mockMemoryManager.findSimilarPatterns.mockResolvedValue([
        {
          inputParameters: { businessType: 'SaaS' },
          outputQuality: 0.9,
          userFeedback: 'positive',
          generatedContent: { conversionRate: 0.12 },
        },
      ]);

      const result = await analyzeRequirements.execute(mockContext);

      expect(result).toEqual({
        strategy: expect.stringContaining('B2B'),
        designDirection: expect.any(String),
        contentPriorities: expect.arrayContaining([
          expect.stringContaining('Trust'),
          expect.stringContaining('ROI'),
        ]),
        insights: expect.arrayContaining([
          expect.stringContaining('小規模事業者'),
          expect.stringContaining('競合'),
        ]),
        recommendations: expect.arrayContaining([
          expect.stringContaining('証拠'),
          expect.stringContaining('差別化'),
        ]),
        confidenceScore: expect.any(Number),
        marketAnalysis: expect.objectContaining({
          competitorAnalysis: expect.stringContaining('Salesforce'),
          targetAudiencePain: expect.arrayContaining([
            expect.stringContaining('コスト'),
            expect.stringContaining('効率'),
          ]),
          opportunities: expect.any(Array),
        }),
      });

      expect(mockMemoryManager.saveRequirementAnalysis).toHaveBeenCalledWith(
        expect.objectContaining({
          businessType: 'SaaS',
          targetAudience: 'Small business owners',
          strategy: expect.any(String),
        }),
        'test-session-1'
      );
    });

    it('should handle E-commerce business analysis', async () => {
      const analysisParams = {
        businessType: 'E-commerce',
        targetAudience: 'Young professionals aged 25-35',
        goals: ['Increase sales', 'Improve user experience'],
        existingBrand: 'FashionForward',
      };

      const mockContext = {
        sessionId: 'test-session-2',
        parameters: analysisParams,
      };

      mockMemoryManager.saveRequirementAnalysis.mockResolvedValue({
        id: 'analysis-2',
        success: true,
      });

      mockMemoryManager.findSimilarPatterns.mockResolvedValue([]);

      const result = await analyzeRequirements.execute(mockContext);

      expect(result.strategy).toContain('D2C');
      expect(result.designDirection).toContain('モダン');
      expect(result.contentPriorities).toContain('ビジュアル重視');
      expect(result.insights).toContain('若年層専門家は購買決定が速い');
    });

    it('should provide recommendations based on similar successful patterns', async () => {
      const analysisParams = {
        businessType: 'Consulting',
        targetAudience: 'Enterprise executives',
        goals: ['Generate leads', 'Establish authority'],
      };

      const mockContext = {
        sessionId: 'test-session-3',
        parameters: analysisParams,
      };

      mockMemoryManager.findSimilarPatterns.mockResolvedValue([
        {
          inputParameters: { businessType: 'Consulting' },
          outputQuality: 0.95,
          userFeedback: 'positive',
          generatedContent: {
            headline: 'Transform Your Business Strategy',
            conversionRate: 0.18,
          },
          conversionMetrics: {
            conversionRate: 0.18,
            engagementRate: 0.65,
          },
        },
      ]);

      const result = await analyzeRequirements.execute(mockContext);

      expect(result.insights).toContain('過去の成功パターン: コンサルティング分野で18%のコンバージョン率を達成');
      expect(result.recommendations).toContain('「Transform Your Business Strategy」のようなヘッドラインが効果的');
      expect(result.confidenceScore).toBeGreaterThan(0.8);
    });

    it('should handle missing optional parameters gracefully', async () => {
      const analysisParams = {
        businessType: 'Health & Wellness',
        targetAudience: 'Health-conscious individuals',
        goals: ['Build community', 'Drive subscriptions'],
      };

      const mockContext = {
        sessionId: 'test-session-4',
        parameters: analysisParams,
      };

      mockMemoryManager.saveRequirementAnalysis.mockResolvedValue({
        id: 'analysis-4',
        success: true,
      });

      const result = await analyzeRequirements.execute(mockContext);

      expect(result.strategy).toBeDefined();
      expect(result.designDirection).toBeDefined();
      expect(result.contentPriorities).toHaveLength(3);
      expect(result.insights).toBeDefined();
      expect(result.marketAnalysis.competitorAnalysis).toContain('競合情報が提供されていません');
    });

    it('should handle memory save errors gracefully', async () => {
      const analysisParams = {
        businessType: 'Technology',
        targetAudience: 'Developers',
        goals: ['Increase API adoption'],
      };

      const mockContext = {
        sessionId: 'test-session-5',
        parameters: analysisParams,
      };

      mockMemoryManager.saveRequirementAnalysis.mockRejectedValue(new Error('Memory save failed'));

      // Should still return analysis even if memory save fails
      const result = await analyzeRequirements.execute(mockContext);

      expect(result.strategy).toBeDefined();
      expect(result.designDirection).toBeDefined();
      expect(result.insights).toBeDefined();
    });

    it('should calculate confidence score based on available data', async () => {
      const completeAnalysisParams = {
        businessType: 'SaaS',
        targetAudience: 'Marketing managers',
        goals: ['Increase sign-ups', 'Reduce churn'],
        existingBrand: 'MarketingPro',
        competitors: 'Mailchimp, Constant Contact',
      };

      const incompleteAnalysisParams = {
        businessType: 'Unknown',
        targetAudience: 'General users',
        goals: ['Increase sales'],
      };

      const completeContext = {
        sessionId: 'complete-session',
        parameters: completeAnalysisParams,
      };

      const incompleteContext = {
        sessionId: 'incomplete-session',
        parameters: incompleteAnalysisParams,
      };

      mockMemoryManager.saveRequirementAnalysis.mockResolvedValue({
        id: 'analysis',
        success: true,
      });

      const completeResult = await analyzeRequirements.execute(completeContext);
      const incompleteResult = await analyzeRequirements.execute(incompleteContext);

      expect(completeResult.confidenceScore).toBeGreaterThan(incompleteResult.confidenceScore);
      expect(completeResult.confidenceScore).toBeGreaterThanOrEqual(0.7);
      expect(incompleteResult.confidenceScore).toBeLessThan(0.7);
    });
  });
});