import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { Agent } from '@mastra/core';
import { lpCreatorAgent } from '../../mastra/agents/lp-creator-agent';
import { ProjectMemoryManager } from '../../mastra/memory/project-memory';

// Mock dependencies
jest.mock('@mastra/core');
jest.mock('../../mastra/memory/project-memory');
jest.mock('../../mastra/tools/legacy-tools-wrapper');
jest.mock('../../mastra/tools/analysis-tools');

describe('LP Creator Agent', () => {
  let mockMemoryManager: jest.Mocked<ProjectMemoryManager>;
  let mockAgent: jest.Mocked<Agent>;

  beforeEach(() => {
    mockMemoryManager = {
      saveProjectSnapshot: jest.fn(),
      getProjectSnapshots: jest.fn(),
      saveRequirementAnalysis: jest.fn(),
      getRequirementAnalysis: jest.fn(),
      saveGenerationPattern: jest.fn(),
      findSimilarPatterns: jest.fn(),
      saveUserAction: jest.fn(),
      getUserActions: jest.fn(),
    } as any;

    mockAgent = {
      id: 'lp-creator-agent',
      name: 'LP Creator Agent',
      instructions: expect.any(String),
      model: expect.any(Object),
      tools: expect.any(Object),
      memory: expect.any(Object),
      execute: jest.fn(),
      chat: jest.fn(),
    } as any;

    (ProjectMemoryManager as jest.MockedClass<typeof ProjectMemoryManager>).mockImplementation(() => mockMemoryManager);
    (Agent as jest.MockedClass<typeof Agent>).mockImplementation(() => mockAgent);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Agent Configuration', () => {
    it('should be configured with correct basic properties', () => {
      expect(lpCreatorAgent).toBeDefined();
      expect(lpCreatorAgent.name).toBe('LP Creator Agent');
      expect(lpCreatorAgent.id).toBe('lp-creator-agent');
    });

    it('should have comprehensive instructions for LP creation', () => {
      expect(lpCreatorAgent.instructions).toContain('LP Creator');
      expect(lpCreatorAgent.instructions).toContain('既存ツール');
      expect(lpCreatorAgent.instructions).toContain('学習');
      expect(lpCreatorAgent.instructions).toContain('段階的');
    });

    it('should have all required tools configured', () => {
      expect(lpCreatorAgent.tools).toHaveProperty('analyzeRequirements');
      expect(lpCreatorAgent.tools).toHaveProperty('legacyCreateHeroSection');
      expect(lpCreatorAgent.tools).toHaveProperty('legacyCreateFeaturesSection');
      expect(lpCreatorAgent.tools).toHaveProperty('legacyGenerateOptimalConcept');
      expect(lpCreatorAgent.tools).toHaveProperty('legacyCreateTestimonialsSection');
      expect(lpCreatorAgent.tools).toHaveProperty('legacyCreatePricingSection');
    });

    it('should have memory system configured', () => {
      expect(lpCreatorAgent.memory).toBeDefined();
    });
  });

  describe('Agent Execution', () => {
    it('should handle LP creation request with full workflow', async () => {
      const mockInput = {
        sessionId: 'test-session-1',
        message: 'SaaS企業向けのLPを作成してください。ターゲットは中小企業の経営者です。',
        context: {
          businessType: 'SaaS',
          targetAudience: '中小企業の経営者',
          goals: ['リード獲得', '無料トライアル申込'],
        },
      };

      const mockAnalysisResult = {
        strategy: 'B2B重視、ROI・効率性訴求',
        designDirection: 'プロフェッショナル、清潔感',
        contentPriorities: ['Trust building', 'ROI証明'],
        insights: ['中小企業は投資対効果を重視'],
        confidenceScore: 0.85,
      };

      const mockHeroResult = {
        originalResult: {
          headline: '中小企業の効率化を実現するSaaS',
          subheadline: '業務時間を50%削減、売上を30%向上',
          ctaText: '無料トライアルを開始',
          features: ['簡単導入', 'コスト削減', '24時間サポート'],
        },
        sessionId: 'test-session-1',
        metadata: { wrappedAt: new Date() },
      };

      // Mock tool executions
      mockAgent.execute.mockResolvedValue({
        response: 'LP作成プロセスを開始します。',
        toolResults: {
          analyzeRequirements: mockAnalysisResult,
          legacyCreateHeroSection: mockHeroResult,
        },
        sessionId: 'test-session-1',
      });

      const result = await lpCreatorAgent.execute(mockInput);

      expect(result).toEqual({
        response: 'LP作成プロセスを開始します。',
        toolResults: {
          analyzeRequirements: mockAnalysisResult,
          legacyCreateHeroSection: mockHeroResult,
        },
        sessionId: 'test-session-1',
      });
    });

    it('should handle iterative improvement workflow', async () => {
      const mockInput = {
        sessionId: 'existing-session',
        message: 'ヒーローセクションをもっと感情的にしてください',
        context: {
          previousGeneration: {
            headline: '効率的なSaaSソリューション',
            feedback: 'もっと感情に訴える表現にしたい',
          },
        },
      };

      // Mock previous context retrieval
      mockMemoryManager.getProjectSnapshots.mockResolvedValue([
        {
          sessionId: 'existing-session',
          projectData: {
            businessType: 'SaaS',
            targetAudience: '中小企業経営者',
          },
          userActions: [
            {
              type: 'feedback',
              timestamp: new Date(),
              details: { feedback: 'もっと感情的に' },
              sessionId: 'existing-session',
            },
          ],
          generatedSections: [],
          timestamp: new Date(),
          metadata: { version: '1.0', source: 'agent' },
        },
      ]);

      const mockImprovedResult = {
        originalResult: {
          headline: 'あなたの夢を実現するSaaS',
          subheadline: '忙しい毎日から解放され、本当にやりたいことに集中',
          ctaText: '今すぐ変化を始める',
          features: ['人生が変わる', '時間を取り戻す', '成功への道'],
        },
        sessionId: 'existing-session',
        metadata: { wrappedAt: new Date() },
      };

      mockAgent.execute.mockResolvedValue({
        response: 'より感情的なヒーローセクションを作成しました。',
        toolResults: {
          legacyCreateHeroSection: mockImprovedResult,
        },
        sessionId: 'existing-session',
      });

      const result = await lpCreatorAgent.execute(mockInput);

      expect(result.toolResults.legacyCreateHeroSection.originalResult.headline).toContain('夢');
      expect(result.toolResults.legacyCreateHeroSection.originalResult.subheadline).toContain('解放');
    });

    it('should handle error cases gracefully', async () => {
      const mockInput = {
        sessionId: 'error-session',
        message: '無効なリクエスト',
        context: {},
      };

      mockAgent.execute.mockRejectedValue(new Error('Invalid input'));

      await expect(lpCreatorAgent.execute(mockInput)).rejects.toThrow('Invalid input');
    });
  });

  describe('Learning and Memory Integration', () => {
    it('should save successful generations to memory', async () => {
      const mockInput = {
        sessionId: 'learning-session',
        message: 'E-commerce向けLP作成',
        context: {
          businessType: 'E-commerce',
          targetAudience: '20代女性',
        },
      };

      const mockSuccessfulGeneration = {
        originalResult: {
          headline: 'あなたのスタイルを見つけよう',
          conversionRate: 0.15,
        },
        sessionId: 'learning-session',
        metadata: { wrappedAt: new Date() },
        learningData: {
          inputParameters: {
            businessType: 'E-commerce',
            targetAudience: '20代女性',
          },
          outputQuality: 0.9,
          userFeedback: 'positive',
          generatedContent: {
            headline: 'あなたのスタイルを見つけよう',
            conversionRate: 0.15,
          },
        },
      };

      mockAgent.execute.mockResolvedValue({
        response: 'E-commerce向けLPを作成しました。',
        toolResults: {
          legacyCreateHeroSection: mockSuccessfulGeneration,
        },
        sessionId: 'learning-session',
      });

      await lpCreatorAgent.execute(mockInput);

      // The agent should internally save learning data
      expect(mockAgent.execute).toHaveBeenCalledWith(mockInput);
    });

    it('should leverage past successful patterns', async () => {
      const mockInput = {
        sessionId: 'pattern-session',
        message: 'SaaS向けLP作成（過去の成功パターンを活用）',
        context: {
          businessType: 'SaaS',
          targetAudience: 'スタートアップ創設者',
        },
      };

      // Mock similar patterns exist
      mockMemoryManager.findSimilarPatterns.mockResolvedValue([
        {
          inputParameters: { businessType: 'SaaS' },
          outputQuality: 0.95,
          userFeedback: 'positive',
          generatedContent: {
            headline: 'スタートアップの成長を加速',
            conversionRate: 0.18,
          },
        },
      ]);

      const mockPatternBasedResult = {
        originalResult: {
          headline: 'スタートアップの成長を10倍速で加速',
          subheadline: '過去の成功事例から最適化されたソリューション',
          ctaText: '成長を始める',
        },
        sessionId: 'pattern-session',
        metadata: { wrappedAt: new Date() },
      };

      mockAgent.execute.mockResolvedValue({
        response: '過去の成功パターンを活用してLPを作成しました。',
        toolResults: {
          legacyCreateHeroSection: mockPatternBasedResult,
        },
        sessionId: 'pattern-session',
      });

      const result = await lpCreatorAgent.execute(mockInput);

      expect(result.toolResults.legacyCreateHeroSection.originalResult.headline).toContain('成長');
      expect(result.toolResults.legacyCreateHeroSection.originalResult.subheadline).toContain('最適化');
    });
  });

  describe('Multi-step LP Generation Workflow', () => {
    it('should execute complete LP generation workflow', async () => {
      const mockInput = {
        sessionId: 'complete-workflow',
        message: '完全なLPを作成してください',
        context: {
          businessType: 'Consulting',
          targetAudience: 'Enterprise executives',
          goals: ['Lead generation', 'Authority building'],
        },
      };

      const mockWorkflowResult = {
        response: '完全なLPワークフローを実行しました。',
        toolResults: {
          analyzeRequirements: {
            strategy: 'B2B、権威性・専門性アピール',
            confidenceScore: 0.8,
          },
          legacyCreateHeroSection: {
            originalResult: {
              headline: 'エンタープライズ変革のパートナー',
              subheadline: '20年の実績で、あなたのビジネスを次のレベルへ',
            },
          },
          legacyCreateFeaturesSection: {
            originalResult: {
              title: '実証済みのソリューション',
              features: [
                { title: '戦略策定', description: '包括的な変革戦略' },
                { title: '実行支援', description: '確実な実装サポート' },
              ],
            },
          },
          legacyCreateTestimonialsSection: {
            originalResult: {
              title: 'お客様の成功事例',
              testimonials: [
                {
                  name: 'CEO 田中',
                  company: 'Tech Corp',
                  content: '売上が3倍になりました',
                  rating: 5,
                },
              ],
            },
          },
        },
        sessionId: 'complete-workflow',
      };

      mockAgent.execute.mockResolvedValue(mockWorkflowResult);

      const result = await lpCreatorAgent.execute(mockInput);

      expect(result.toolResults).toHaveProperty('analyzeRequirements');
      expect(result.toolResults).toHaveProperty('legacyCreateHeroSection');
      expect(result.toolResults).toHaveProperty('legacyCreateFeaturesSection');
      expect(result.toolResults).toHaveProperty('legacyCreateTestimonialsSection');
      expect(result.response).toContain('完全なLP');
    });
  });
});