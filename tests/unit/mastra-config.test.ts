/**
 * @jest-environment node
 */

// Mock should be at the top level, outside of any test
jest.mock('@mastra/core', () => ({
  MastraConfig: {},
}));

describe('Mastra Configuration', () => {
  beforeEach(() => {
    // Reset environment variables
    delete process.env.ENABLE_MASTRA;
    delete process.env.MASTRA_LOG_LEVEL;
    delete process.env.DATABASE_URL;
    delete process.env.ANTHROPIC_API_KEY;
  });

  test('should have correct default environment variables', () => {
    // Test default values
    const enableMastra = process.env.ENABLE_MASTRA;
    const logLevel = process.env.MASTRA_LOG_LEVEL;
    const databaseUrl = process.env.DATABASE_URL;

    // These should be undefined initially, but we check the fallback values
    expect(enableMastra || 'false').toBe('false');
    expect(logLevel || 'debug').toBe('debug');
    expect(databaseUrl || 'file:./.mastra/memory.db').toBe('file:./.mastra/memory.db');
  });

  test('should load Mastra config without errors', () => {
    // This test verifies that the config file can be imported without throwing
    expect(() => {
      // Import the actual config instead of duplicating it
      const { config } = require('../../mastra/mastra.config');
      expect(config.name).toBe('lp-creator-mastra');
      expect(config.port).toBe(4000);
      expect(config.logLevel).toBe('debug');
    }).not.toThrow();
  });

  test('should handle missing environment variables gracefully', () => {
    // Import and test the actual configuration
    const { config } = require('../../mastra/mastra.config');

    expect(config.logLevel).toBe('debug');
    expect(config.database.url).toBe('file:./.mastra/memory.db');
    expect(config.llm.apiKey).toBeUndefined();
    expect(config.name).toBe('lp-creator-mastra');
    expect(config.port).toBe(4000);
  });
});