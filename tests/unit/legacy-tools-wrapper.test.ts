import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { mastrafy } from '../../mastra/utils/mastrafy';
import { createHeroSection, createFeaturesSection } from '../../ai/tools';

// Mock the original tools
jest.mock('../../ai/tools', () => ({
  createHeroSection: {
    description: 'ヒーローセクション（メインビジュアル、キャッチコピー、CTA）を生成',
    parameters: expect.any(Object),
    execute: jest.fn(),
  },
  createFeaturesSection: {
    description: '特徴・機能セクション（ベネフィット重視）を生成',
    parameters: expect.any(Object),
    execute: jest.fn(),
  },
}));

describe('Legacy Tools Wrapper', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('mastrafy utility', () => {
    it('should wrap existing tool with mastra compatibility', async () => {
      const mockOriginalTool = {
        description: 'Test tool',
        parameters: {
          parse: jest.fn().mockReturnValue({ test: 'param' }),
        },
        execute: jest.fn().mockResolvedValue({ success: true, data: 'test' }),
      };

      const wrappedTool = mastrafy(mockOriginalTool, {
        description: 'Mastra wrapped test tool',
        preserveSchema: true,
        addLearning: true,
      });

      expect(wrappedTool.description).toBe('Mastra wrapped test tool');
      expect(wrappedTool.parameters).toBe(mockOriginalTool.parameters);

      const mockContext = {
        sessionId: 'test-session',
        parameters: { test: 'param' },
      };

      const result = await wrappedTool.execute(mockContext);

      expect(mockOriginalTool.execute).toHaveBeenCalledWith({ test: 'param' });
      expect(result).toEqual({
        originalResult: { success: true, data: 'test' },
        sessionId: 'test-session',
        metadata: {
          wrappedAt: expect.any(Date),
          preserveSchema: true,
          addLearning: true,
        },
      });
    });

    it('should handle tool execution errors', async () => {
      const mockOriginalTool = {
        description: 'Failing tool',
        parameters: {
          parse: jest.fn().mockReturnValue({ test: 'param' }),
        },
        execute: jest.fn().mockRejectedValue(new Error('Tool execution failed')),
      };

      const wrappedTool = mastrafy(mockOriginalTool, {
        description: 'Wrapped failing tool',
        preserveSchema: true,
        addLearning: false,
      });

      const mockContext = {
        sessionId: 'test-session',
        parameters: { test: 'param' },
      };

      await expect(wrappedTool.execute(mockContext)).rejects.toThrow('Tool execution failed');
    });

    it('should add learning metadata when enabled', async () => {
      const mockOriginalTool = {
        description: 'Learning tool',
        parameters: {
          parse: jest.fn().mockReturnValue({ businessType: 'SaaS' }),
        },
        execute: jest.fn().mockResolvedValue({
          headline: 'Great SaaS Solution',
          conversionRate: 0.15,
        }),
      };

      const wrappedTool = mastrafy(mockOriginalTool, {
        description: 'Learning enabled tool',
        preserveSchema: true,
        addLearning: true,
      });

      const mockContext = {
        sessionId: 'test-session',
        parameters: { businessType: 'SaaS' },
      };

      const result = await wrappedTool.execute(mockContext);

      expect(result.metadata.addLearning).toBe(true);
      expect(result.learningData).toEqual({
        inputParameters: { businessType: 'SaaS' },
        outputQuality: expect.any(Number),
        userFeedback: 'neutral',
        generatedContent: {
          headline: 'Great SaaS Solution',
          conversionRate: 0.15,
        },
      });
    });
  });

  describe('Hero Section Wrapper', () => {
    it('should wrap createHeroSection with mastra compatibility', async () => {
      const mockHeroResult = {
        headline: 'Test Headline',
        subheadline: 'Test Subheadline',
        ctaText: 'Click Here',
        features: ['Feature 1', 'Feature 2'],
      };

      (createHeroSection.execute as jest.Mock).mockResolvedValue(mockHeroResult);

      const { legacyCreateHeroSection } = await import('../../mastra/tools/legacy-tools-wrapper');

      const context = {
        sessionId: 'test-session',
        parameters: {
          headline: 'Test Headline',
          subheadline: 'Test Subheadline',
          ctaText: 'Click Here',
          features: ['Feature 1', 'Feature 2'],
        },
      };

      const result = await legacyCreateHeroSection.execute(context);

      expect(createHeroSection.execute).toHaveBeenCalledWith(context.parameters);
      expect(result.originalResult).toEqual(mockHeroResult);
      expect(result.sessionId).toBe('test-session');
      expect(result.metadata.preserveSchema).toBe(true);
    });
  });

  describe('Features Section Wrapper', () => {
    it('should wrap createFeaturesSection with mastra compatibility', async () => {
      const mockFeaturesResult = {
        title: 'Key Features',
        subtitle: 'What makes us special',
        features: [
          {
            icon: 'check-circle',
            title: 'Feature 1',
            description: 'Amazing feature',
            benefit: 'Saves time',
          },
        ],
      };

      (createFeaturesSection.execute as jest.Mock).mockResolvedValue(mockFeaturesResult);

      const { legacyCreateFeaturesSection } = await import('../../mastra/tools/legacy-tools-wrapper');

      const context = {
        sessionId: 'test-session',
        parameters: {
          title: 'Key Features',
          subtitle: 'What makes us special',
          features: [
            {
              icon: 'check-circle',
              title: 'Feature 1',
              description: 'Amazing feature',
              benefit: 'Saves time',
            },
          ],
        },
      };

      const result = await legacyCreateFeaturesSection.execute(context);

      expect(createFeaturesSection.execute).toHaveBeenCalledWith(context.parameters);
      expect(result.originalResult).toEqual(mockFeaturesResult);
      expect(result.sessionId).toBe('test-session');
      expect(result.metadata.addLearning).toBe(true);
    });
  });

  describe('Schema Preservation', () => {
    it('should preserve original parameter schema', () => {
      const mockOriginalTool = {
        description: 'Original tool',
        parameters: {
          parse: jest.fn(),
          shape: {
            headline: { type: 'string' },
            features: { type: 'array' },
          },
        },
        execute: jest.fn(),
      };

      const wrappedTool = mastrafy(mockOriginalTool, {
        description: 'Wrapped tool',
        preserveSchema: true,
        addLearning: false,
      });

      expect(wrappedTool.parameters).toBe(mockOriginalTool.parameters);
    });

    it('should maintain backward compatibility', async () => {
      const mockResult = { test: 'result' };
      const mockOriginalTool = {
        description: 'Compatible tool',
        parameters: {
          parse: jest.fn().mockReturnValue({ param: 'value' }),
        },
        execute: jest.fn().mockResolvedValue(mockResult),
      };

      const wrappedTool = mastrafy(mockOriginalTool, {
        description: 'Backward compatible wrapper',
        preserveSchema: true,
        addLearning: false,
      });

      const result = await wrappedTool.execute({
        sessionId: 'test',
        parameters: { param: 'value' },
      });

      expect(result.originalResult).toEqual(mockResult);
      expect(mockOriginalTool.execute).toHaveBeenCalledWith({ param: 'value' });
    });
  });
});