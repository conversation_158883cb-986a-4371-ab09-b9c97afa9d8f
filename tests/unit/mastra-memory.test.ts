import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { LibSQLStore } from '@mastra/libsql';
import { Memory } from '@mastra/core';

// Mock LibSQL store for testing
jest.mock('@mastra/libsql');

describe('Mastra Memory System', () => {
  let memory: Memory;
  let mockStore: jest.Mocked<LibSQLStore>;

  beforeEach(() => {
    // Setup mock store
    mockStore = {
      save: jest.fn(),
      retrieve: jest.fn(),
      search: jest.fn(),
      delete: jest.fn(),
    } as any;

    // Mock LibSQLStore constructor
    (LibSQLStore as jest.MockedClass<typeof LibSQLStore>).mockImplementation(() => mockStore);

    // Initialize memory with mock store
    memory = new Memory({
      storage: mockStore,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Project Memory Management', () => {
    it('should save project snapshot', async () => {
      const projectData = {
        sessionId: 'test-session-1',
        projectData: {
          productName: 'Test Product',
          targetAudience: 'Test Audience',
        },
        userActions: [],
        generatedSections: [],
        timestamp: new Date(),
        metadata: {
          version: '1.0',
          source: 'test',
        },
      };

      mockStore.save.mockResolvedValue({ id: 'memory-1', success: true });

      const result = await memory.save({
        type: 'project_snapshot',
        data: projectData,
        sessionId: projectData.sessionId,
      });

      expect(mockStore.save).toHaveBeenCalledWith(expect.objectContaining({
        type: 'project_snapshot',
        data: projectData,
        sessionId: 'test-session-1',
      }));
      expect(result).toEqual({ id: 'memory-1', success: true });
    });

    it('should retrieve project snapshots by session', async () => {
      const mockSnapshots = [
        {
          id: 'memory-1',
          type: 'project_snapshot',
          sessionId: 'test-session-1',
          data: { productName: 'Test Product' },
          timestamp: new Date(),
        },
      ];

      mockStore.search.mockResolvedValue(mockSnapshots);

      const snapshots = await memory.search({
        type: 'project_snapshot',
        sessionId: 'test-session-1',
      });

      expect(mockStore.search).toHaveBeenCalledWith({
        type: 'project_snapshot',
        sessionId: 'test-session-1',
      });
      expect(snapshots).toEqual(mockSnapshots);
    });

    it('should save requirement analysis', async () => {
      const analysisData = {
        businessType: 'SaaS',
        targetAudience: 'Business owners',
        strategy: 'B2B focused approach',
        insights: ['Focus on ROI', 'Emphasize time savings'],
      };

      mockStore.save.mockResolvedValue({ id: 'analysis-1', success: true });

      const result = await memory.save({
        type: 'requirement_analysis',
        data: analysisData,
        sessionId: 'test-session-1',
      });

      expect(mockStore.save).toHaveBeenCalledWith(expect.objectContaining({
        type: 'requirement_analysis',
        data: analysisData,
        sessionId: 'test-session-1',
      }));
      expect(result).toEqual({ id: 'analysis-1', success: true });
    });
  });

  describe('Learning Memory', () => {
    it('should save successful generation patterns', async () => {
      const patternData = {
        inputParameters: {
          businessType: 'E-commerce',
          targetAudience: 'Young professionals',
        },
        outputQuality: 0.95,
        userFeedback: 'positive',
        generatedContent: {
          headline: 'Revolutionary E-commerce Solution',
          conversionRate: 0.12,
        },
      };

      mockStore.save.mockResolvedValue({ id: 'pattern-1', success: true });

      const result = await memory.save({
        type: 'generation_pattern',
        data: patternData,
        sessionId: 'test-session-1',
      });

      expect(mockStore.save).toHaveBeenCalledWith(expect.objectContaining({
        type: 'generation_pattern',
        data: patternData,
      }));
      expect(result).toEqual({ id: 'pattern-1', success: true });
    });

    it('should retrieve successful patterns for similar contexts', async () => {
      const mockPatterns = [
        {
          id: 'pattern-1',
          type: 'generation_pattern',
          data: {
            inputParameters: { businessType: 'E-commerce' },
            outputQuality: 0.95,
          },
        },
      ];

      mockStore.search.mockResolvedValue(mockPatterns);

      const patterns = await memory.search({
        type: 'generation_pattern',
        filters: {
          'data.inputParameters.businessType': 'E-commerce',
          'data.outputQuality': { $gte: 0.8 },
        },
      });

      expect(patterns).toEqual(mockPatterns);
    });
  });

  describe('Error Handling', () => {
    it('should handle storage errors gracefully', async () => {
      mockStore.save.mockRejectedValue(new Error('Storage error'));

      await expect(memory.save({
        type: 'test',
        data: { test: 'data' },
        sessionId: 'test-session',
      })).rejects.toThrow('Storage error');
    });

    it('should handle search errors gracefully', async () => {
      mockStore.search.mockRejectedValue(new Error('Search error'));

      await expect(memory.search({
        type: 'test',
      })).rejects.toThrow('Search error');
    });
  });
});