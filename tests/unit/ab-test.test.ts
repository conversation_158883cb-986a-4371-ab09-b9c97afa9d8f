// tests/unit/ab-test.test.ts
import { getExperimentGroup, shouldUseMastra } from '../../app/mastra/utils/ab-test';
import { getFeatureFlags } from '../../app/mastra/config/feature-flags';

// Mock the feature flags module
jest.mock('../../app/mastra/config/feature-flags');
const mockGetFeatureFlags = getFeatureFlags as jest.MockedFunction<typeof getFeatureFlags>;

describe('A/B Testing System', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getExperimentGroup', () => {
    it('should return "control" when MASTRA_AB_TEST is disabled', () => {
      mockGetFeatureFlags.mockReturnValue({
        ENABLE_MASTRA: true,
        MASTRA_AGENT_TOOLS: true,
        MASTRA_MEMORY_SYSTEM: true,
        MASTRA_WORKFLOW_ENGINE: true,
        MASTRA_AB_TEST: false
      });

      const result = getExperimentGroup('test-session-123');
      expect(result).toBe('control');
    });

    it('should return consistent groups for same session ID', () => {
      mockGetFeatureFlags.mockReturnValue({
        ENABLE_MASTRA: true,
        MASTRA_AGENT_TOOLS: true,
        MASTRA_MEMORY_SYSTEM: true,
        MASTRA_WORKFLOW_ENGINE: true,
        MASTRA_AB_TEST: true
      });

      const sessionId = 'consistent-session-id';
      const group1 = getExperimentGroup(sessionId);
      const group2 = getExperimentGroup(sessionId);
      const group3 = getExperimentGroup(sessionId);

      expect(group1).toBe(group2);
      expect(group2).toBe(group3);
    });

    it('should distribute sessions between control and mastra groups', () => {
      mockGetFeatureFlags.mockReturnValue({
        ENABLE_MASTRA: true,
        MASTRA_AGENT_TOOLS: true,
        MASTRA_MEMORY_SYSTEM: true,
        MASTRA_WORKFLOW_ENGINE: true,
        MASTRA_AB_TEST: true
      });

      const results = new Set<string>();
      
      // Generate results for 100 different session IDs
      for (let i = 0; i < 100; i++) {
        const sessionId = `session-${i}`;
        const group = getExperimentGroup(sessionId);
        results.add(group);
      }

      // Should have both control and mastra groups
      expect(results.has('control')).toBe(true);
      expect(results.has('mastra')).toBe(true);
      expect(results.size).toBe(2);
    });

    it('should provide roughly 50/50 distribution over many sessions', () => {
      mockGetFeatureFlags.mockReturnValue({
        ENABLE_MASTRA: true,
        MASTRA_AGENT_TOOLS: true,
        MASTRA_MEMORY_SYSTEM: true,
        MASTRA_WORKFLOW_ENGINE: true,
        MASTRA_AB_TEST: true
      });

      let controlCount = 0;
      let mastraCount = 0;
      const totalSessions = 1000;

      for (let i = 0; i < totalSessions; i++) {
        const sessionId = `session-${i}-${Math.random()}`;
        const group = getExperimentGroup(sessionId);
        
        if (group === 'control') {
          controlCount++;
        } else {
          mastraCount++;
        }
      }

      // Allow for some variance, but should be roughly balanced
      const tolerance = totalSessions * 0.1; // 10% tolerance
      expect(Math.abs(controlCount - mastraCount)).toBeLessThan(tolerance);
    });
  });

  describe('shouldUseMastra', () => {
    it('should return false when ENABLE_MASTRA is false', () => {
      mockGetFeatureFlags.mockReturnValue({
        ENABLE_MASTRA: false,
        MASTRA_AGENT_TOOLS: true,
        MASTRA_MEMORY_SYSTEM: true,
        MASTRA_WORKFLOW_ENGINE: true,
        MASTRA_AB_TEST: true
      });

      const result = shouldUseMastra('test-session');
      expect(result).toBe(false);
    });

    it('should return true when ENABLE_MASTRA is true and MASTRA_AB_TEST is false', () => {
      mockGetFeatureFlags.mockReturnValue({
        ENABLE_MASTRA: true,
        MASTRA_AGENT_TOOLS: true,
        MASTRA_MEMORY_SYSTEM: true,
        MASTRA_WORKFLOW_ENGINE: true,
        MASTRA_AB_TEST: false
      });

      const result = shouldUseMastra('test-session');
      expect(result).toBe(true);
    });

    it('should follow experiment group when both ENABLE_MASTRA and MASTRA_AB_TEST are true', () => {
      mockGetFeatureFlags.mockReturnValue({
        ENABLE_MASTRA: true,
        MASTRA_AGENT_TOOLS: true,
        MASTRA_MEMORY_SYSTEM: true,
        MASTRA_WORKFLOW_ENGINE: true,
        MASTRA_AB_TEST: true
      });

      // Test multiple session IDs to verify the logic
      const results = new Set<boolean>();
      
      for (let i = 0; i < 50; i++) {
        const sessionId = `session-${i}`;
        const shouldUse = shouldUseMastra(sessionId);
        results.add(shouldUse);
      }

      // Should have both true and false results
      expect(results.has(true)).toBe(true);
      expect(results.has(false)).toBe(true);
    });

    it('should be consistent for the same session ID', () => {
      mockGetFeatureFlags.mockReturnValue({
        ENABLE_MASTRA: true,
        MASTRA_AGENT_TOOLS: true,
        MASTRA_MEMORY_SYSTEM: true,
        MASTRA_WORKFLOW_ENGINE: true,
        MASTRA_AB_TEST: true
      });

      const sessionId = 'consistent-test-session';
      const result1 = shouldUseMastra(sessionId);
      const result2 = shouldUseMastra(sessionId);
      const result3 = shouldUseMastra(sessionId);

      expect(result1).toBe(result2);
      expect(result2).toBe(result3);
    });
  });
});