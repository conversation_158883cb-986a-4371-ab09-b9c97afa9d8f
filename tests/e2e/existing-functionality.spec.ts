import { test, expect } from '@playwright/test';

test.describe('Existing Functionality Verification', () => {
  test('should load the main page without errors', async ({ page }) => {
    await page.goto('/');
    
    // Verify the page loads
    await expect(page).toHaveTitle(/LP Creator/i);
    
    // Check for any JavaScript errors
    const errors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    await page.waitForLoadState('networkidle');
    
    // Filter out known acceptable errors (like network timeouts in CI)
    const acceptableErrorPatterns = [
      /net::ERR_/,
      /Loading chunk/,
      /ChunkLoadError/
    ];
    
    const criticalErrors = errors.filter(error => 
      !acceptableErrorPatterns.some(pattern => pattern.test(error))
    );
    
    expect(criticalErrors).toHaveLength(0);
  });

  test('should have working API endpoints', async ({ page }) => {
    // Test chat API
    const response = await page.request.post('/api/chat', {
      data: {
        messages: [{ role: 'user', content: 'Hello test' }]
      },
      failOnStatusCode: false
    });
    
    // Should return 200 or 500 (API key might not be configured)
    expect([200, 500]).toContain(response.status());

    // Test chat-claude API
    const claudeResponse = await page.request.post('/api/chat-claude', {
      data: {
        messages: [{ role: 'user', content: 'Hello test' }]
      },
      failOnStatusCode: false
    });
    
    // Should return 200 or 500 (API key might not be configured)
    expect([200, 500]).toContain(claudeResponse.status());
  });

  test('should load Claude page', async ({ page }) => {
    await page.goto('/claude');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Check that the page doesn't have any rendering errors
    await expect(page).toHaveTitle(/LP Creator/i);
    
    // Verify the page contains expected elements
    const pageContent = await page.textContent('body');
    expect(pageContent).toBeTruthy();
    
    // Check for JavaScript errors similar to main page test
    const errors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    const acceptableErrorPatterns = [/net::ERR_/, /Loading chunk/];
    const criticalErrors = errors.filter(error => 
      !acceptableErrorPatterns.some(pattern => pattern.test(error))
    );
    
    expect(criticalErrors).toHaveLength(0);
  });
});