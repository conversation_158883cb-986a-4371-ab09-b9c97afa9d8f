import { test, expect } from '@playwright/test';

test.describe('手動テキスト調整機能', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    
    // LP生成してテキスト編集可能な状態にする
    const chatInput = page.locator('textarea, input[type="text"]').first();
    await chatInput.fill('テスト用のLPを作成してください');
    await page.keyboard.press('Enter');
    
    // LP生成完了を待機（決定的な条件）
    await expect(page.locator('[data-section-type="hero"]')).toBeVisible({ timeout: 30000 });
    
    // 編集モードを有効にする
    const editModeButton = page.locator('button:has-text("編集モード")');
    await editModeButton.waitFor({ timeout: 15000 });
    await editModeButton.click();
    
    // テキスト編集モードを有効にする
    const textEditButton = page.locator('button:has-text("テキスト編集")');
    if (await textEditButton.isVisible()) {
      await textEditButton.click();
    }
  });

  test('HeroSectionヘッドライン編集', async ({ page }) => {
    // ヒーローセクションのヘッドラインを探す
    const headline = page.locator('[data-editable="true"]').first();
    await expect(headline).toBeVisible({ timeout: 10000 });
    
    // 編集可能な状態であることを確認
    await expect(headline).toHaveAttribute('data-editable', 'true');
    
    // クリックして編集モードに入る
    await headline.click();
    
    // 入力フィールドが表示されることを確認
    const input = page.locator(':focus').first();
    await expect(input).toBeVisible({ timeout: 3000 });
    
    // テキストを編集
    await input.fill('編集されたヘッドライン');
    
    // Enterで確定
    await input.press('Enter');
    
    // 変更が反映されることを確認
    await expect(page.locator('text=編集されたヘッドライン')).toBeVisible({ timeout: 3000 });
  });

  test('各セクションタイトル・内容編集', async ({ page }) => {
    // 編集可能な要素を全て取得
    const editableElements = page.locator('[data-editable="true"]');
    const count = await editableElements.count();
    
    expect(count).toBeGreaterThan(0);
    
    // 複数の要素を編集
    for (let i = 0; i < Math.min(count, 3); i++) {
      const element = editableElements.nth(i);
      await element.scrollIntoViewIfNeeded();
      await element.click();
      
      const input = page.locator(':focus').first();
      if (await input.isVisible({ timeout: 2000 })) {
        await input.fill(`編集されたテキスト ${i + 1}`);
        await input.press('Enter');
        
        // 変更の確認
        await expect(page.locator(`text=編集されたテキスト ${i + 1}`)).toBeVisible({ timeout: 3000 });
      }
    }
  });

  test('キーボードショートカット動作', async ({ page }) => {
    // Ctrl+T でテキスト編集モード切り替え
    await page.keyboard.press('Control+t');
    await page.waitForTimeout(500);
    
    // 編集可能な要素を確認
    const editableElement = page.locator('[data-editable="true"]').first();
    if (await editableElement.isVisible()) {
      await editableElement.click();
      
      const input = page.locator(':focus').first();
      if (await input.isVisible({ timeout: 2000 })) {
        await input.fill('キーボードショートカットテスト');
        
        // Escキーでキャンセル
        await input.press('Escape');
        
        // 元のテキストが復元されることを確認
        await expect(page.locator('text=キーボードショートカットテスト')).not.toBeVisible({ timeout: 3000 });
      }
    }
  });

  test('長文編集・文字数制限', async ({ page }) => {
    const editableElement = page.locator('[data-editable="true"]').first();
    await editableElement.click();
    
    const input = page.locator(':focus').first();
    if (await input.isVisible({ timeout: 2000 })) {
      // 長いテキストを入力
      const longText = 'これは非常に長いテキストです。'.repeat(20);
      await input.fill(longText);
      
      // 文字数制限がある場合の確認
      const inputValue = await input.inputValue();
      
      // 適切な長さに制限されているかチェック（制限がある場合）
      if (await input.getAttribute('maxlength')) {
        const maxLength = parseInt(await input.getAttribute('maxlength') || '0');
        expect(inputValue.length).toBeLessThanOrEqual(maxLength);
      }
      
      await input.press('Enter');
    }
  });

  test('編集内容の保存・復元', async ({ page }) => {
    // 初期値を取得
    const editableElement = page.locator('[data-editable="true"]').first();
    const originalText = await editableElement.textContent();
    
    // 編集
    await editableElement.click();
    const input = page.locator(':focus').first();
    if (await input.isVisible({ timeout: 2000 })) {
      const newText = '保存テスト用テキスト';
      await input.fill(newText);
      await input.press('Enter');
      
      // 変更の確認
      await expect(page.locator(`text=${newText}`)).toBeVisible({ timeout: 3000 });
      
      // ページリロード
      await page.reload();
      await page.waitForTimeout(3000);
      
      // 変更が保持されているかチェック（実装に依存）
      // 注意: 実際の実装では永続化されない可能性があるため、
      // 元のテキストに戻ることも正常な動作
      const currentText = await page.locator('[data-testid="hero-title"], h1, h2').first().textContent();
      expect(currentText).toBeTruthy();
    }
  });

  test('複数テキスト要素の同時編集制限', async ({ page }) => {
    const editableElements = page.locator('[data-editable="true"]');
    const count = await editableElements.count();
    
    if (count >= 2) {
      // 最初の要素を編集モードにする
      const firstElement = editableElements.first();
      await firstElement.click();
      
      const firstInput = page.locator(':focus').first();
      if (await firstInput.isVisible({ timeout: 2000 })) {
        // 2番目の要素をクリック
        const secondElement = editableElements.nth(1);
        await secondElement.click();
        
        // 最初の編集がキャンセルされることを確認
        await expect(firstInput).not.toBeVisible({ timeout: 3000 });
        
        // 2番目の要素の編集フィールドが表示されることを確認
        const secondInput = page.locator(':focus').first();
        await expect(secondInput).toBeVisible({ timeout: 3000 });
      }
    }
  });

  test('Tab キーでの要素間ナビゲーション', async ({ page }) => {
    const editableElements = page.locator('[data-editable="true"]');
    const count = await editableElements.count();
    
    if (count >= 2) {
      // 最初の要素から開始
      const firstElement = editableElements.first();
      await firstElement.click();
      
      // Tab キーで次の要素へ
      await page.keyboard.press('Tab');
      await page.waitForTimeout(500);
      
      // フォーカスが移動していることを確認
      const activeElement = page.locator(':focus');
      if (await activeElement.isVisible()) {
        const activeId = await activeElement.getAttribute('data-text-id');
        expect(activeId).toBeTruthy();
      }
      
      // Shift+Tab で前の要素へ
      await page.keyboard.press('Shift+Tab');
      await page.waitForTimeout(500);
    }
  });

  test('入力中のリアルタイムプレビュー', async ({ page }) => {
    const editableElement = page.locator('[data-editable="true"]').first();
    await editableElement.click();
    
    const input = page.locator(':focus').first();
    if (await input.isVisible({ timeout: 2000 })) {
      // 段階的にテキストを入力
      const testText = 'リアルタイム';
      
      for (const char of testText) {
        await input.type(char);
        await page.waitForTimeout(100);
      }
      
      // 入力内容が反映されていることを確認
      const inputValue = await input.inputValue();
      expect(inputValue).toContain(testText);
    }
  });

  test('エラーハンドリング（無効な入力）', async ({ page }) => {
    const editableElement = page.locator('[data-editable="true"]').first();
    await editableElement.click();
    
    const input = page.locator(':focus').first();
    if (await input.isVisible({ timeout: 2000 })) {
      // 空文字列を入力
      await input.fill('');
      await input.press('Enter');
      
      // プレースホルダーまたはデフォルトテキストが表示されることを確認
      await page.waitForTimeout(1000);
      
      // XSS保護テスト - アラートダイアログの監視
      let dialogTriggered = false;
      page.on('dialog', async (dialog) => {
        dialogTriggered = true;
        await dialog.dismiss();
      });
      
      // スクリプトタグなど危険な入力をテスト
      await editableElement.click();
      const newInput = page.locator(':focus').first();
      if (await newInput.isVisible({ timeout: 2000 })) {
        await newInput.fill('<script>alert("test")</script>');
        await newInput.press('Enter');
        
        // スクリプトが実行されないことを確認
        await page.waitForTimeout(1000);
        
        // アラートが表示されていないことを確認（XSS保護が機能している）
        expect(dialogTriggered).toBe(false);
      }
    }
  });

  test('アクセシビリティ対応確認', async ({ page }) => {
    // 編集可能要素にフォーカス
    const editableElement = page.locator('[data-editable="true"]').first();
    await editableElement.click();
    
    // キーボードナビゲーション
    await page.keyboard.press('Tab');
    await page.keyboard.press('Enter');
    
    const input = page.locator(':focus').first();
    if (await input.isVisible({ timeout: 2000 })) {
      // スクリーンリーダー用の属性確認
      const ariaLabel = await input.getAttribute('aria-label');
      const placeholder = await input.getAttribute('placeholder');
      
      // アクセシビリティ属性が適切に設定されていることを確認
      expect(ariaLabel || placeholder).toBeTruthy();
    }
  });
});