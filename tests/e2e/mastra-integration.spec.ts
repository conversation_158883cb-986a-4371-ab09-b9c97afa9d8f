import { test, expect } from '@playwright/test';

test.describe('Mastra Integration Tests', () => {
  test('should access Mastra health check endpoint', async ({ page }) => {
    // Test the Mastra health check API
    const response = await page.request.get('/api/mastra-health');
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data).toHaveProperty('status');
    expect(['healthy', 'disabled']).toContain(data.status);
  });

  test('should maintain existing chat API functionality', async ({ page }) => {
    // Verify existing APIs still work
    await page.goto('/');
    
    // Test that the page loads correctly
    await expect(page).toHaveTitle(/LP Creator/i);
    
    // Check that existing functionality is preserved
    const chatButton = page.locator('[data-testid="chat-button"]').first();
    if (await chatButton.isVisible()) {
      await chatButton.click();
      // Verify chat panel opens without errors
      await expect(page.locator('[data-testid="chat-panel"]')).toBeVisible();
    }
  });

  test('should integrate Mastra Agent with existing system', async ({ page }) => {
    // Test that Mastra LP Creator Agent can be accessed
    await page.goto('/');
    
    // Look for agent integration points
    const agentTriggers = page.locator('[data-testid*="agent"], [data-testid*="mastra"]');
    if (await agentTriggers.count() > 0) {
      await expect(agentTriggers.first()).toBeVisible();
    }
    
    // Verify no integration conflicts
    const errors: string[] = [];
    page.on('pageerror', (error) => {
      errors.push(error.message);
    });
    
    await page.waitForTimeout(2000);
    
    const mastraErrors = errors.filter(error => 
      error.toLowerCase().includes('mastra') && 
      error.toLowerCase().includes('error')
    );
    expect(mastraErrors.length).toBe(0);
  });

  test('should preserve existing LP generation workflow', async ({ page }) => {
    await page.goto('/');
    
    // Test that existing LP generation still works
    const generateButtons = page.locator('button:has-text("Generate"), button:has-text("作成"), button:has-text("Create")');
    
    if (await generateButtons.count() > 0) {
      const firstButton = generateButtons.first();
      await expect(firstButton).toBeVisible();
      
      // Verify button is clickable (doesn't test actual generation to avoid API calls)
      await expect(firstButton).toBeEnabled();
    }
  });

  test('should load without TypeScript errors', async ({ page }) => {
    // Go to the main page
    await page.goto('/');
    
    // Check that there are no console errors related to TypeScript compilation
    const logs: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        logs.push(msg.text());
      }
    });
    
    // Wait for page to fully load
    await page.waitForLoadState('networkidle');
    
    // Filter for specific TypeScript and Mastra-related errors
    const tsErrors = logs.filter(log => {
      const lowerLog = log.toLowerCase();
      return (
        lowerLog.includes('typescript') ||
        lowerLog.includes('ts(') ||
        lowerLog.includes('cannot find module') ||
        (lowerLog.includes('mastra') && (
          lowerLog.includes('error') ||
          lowerLog.includes('failed') ||
          lowerLog.includes('undefined')
        ))
      );
    });
    
    expect(tsErrors).toHaveLength(0);
  });

  test('should verify existing API endpoints work', async ({ page }) => {
    // Test existing chat API
    const chatResponse = await page.request.post('/api/chat', {
      data: {
        messages: [{ role: 'user', content: 'test message' }]
      }
    });
    expect(chatResponse.status()).toBe(200);
    
    // Verify response structure
    const chatData = await chatResponse.json();
    expect(chatData).toBeDefined();

    // Test existing chat-claude API
    const claudeResponse = await page.request.post('/api/chat-claude', {
      data: {
        messages: [{ role: 'user', content: 'test message' }]
      }
    });
    expect(claudeResponse.status()).toBe(200);
    
    const claudeData = await claudeResponse.json();
    expect(claudeData).toBeDefined();

    // Test unsplash search API
    const unsplashResponse = await page.request.get('/api/unsplash-search?query=test');
    expect(unsplashResponse.status()).toBe(200);
    
    const unsplashData = await unsplashResponse.json();
    expect(unsplashData).toBeDefined();
  });
});