import { test, expect } from '@playwright/test';

test.describe('統合フロー', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('コンセプト提案→LP生成→テキスト調整の完全フロー', async ({ page }) => {
    // Step 1: AIコンセプト提案
    await expect(page.locator('text=AIコンセプト提案')).toBeVisible({ timeout: 10000 });
    
    // フォーム入力
    await page.locator('textarea[name="productOverview"]').fill('革新的なオンライン学習プラットフォーム');
    await page.locator('input[name="targetAudience"]').fill('20-30代の社会人、スキルアップを目指す人');
    await page.locator('input[name="industry"]').fill('EdTech・教育');
    await page.locator('input[name="budget"]').fill('月額100万円');
    await page.locator('input[name="competitors"]').fill('<PERSON><PERSON><PERSON>、<PERSON><PERSON>、Schoo');
    
    // AI分析実行
    const analyzeButton = page.locator('button:has-text("AIコンセプト分析を開始")');
    await analyzeButton.click();
    
    // 分析結果待機
    await expect(page.locator('text=AI推奨コンセプト')).toBeVisible({ timeout: 60000 });
    
    // Step 2: コンセプト採用→LP生成
    const adoptConceptButton = page.locator('button:has-text("このコンセプトでLP生成")');
    await expect(adoptConceptButton).toBeVisible();
    await adoptConceptButton.click();
    
    // LP生成の開始を確認
    await expect(page.locator('text=LP生成中')).toBeVisible({ timeout: 10000 });
    
    // Step 3: LP生成完了の確認
    // ヒーローセクションが生成されていることを確認
    await expect(page.locator('h1, [role="heading"]')).toBeVisible({ timeout: 30000 });
    
    // Step 4: 編集モードに切り替え
    const editModeButton = page.locator('button:has-text("編集モード")');
    await editModeButton.waitFor({ timeout: 10000 });
    await editModeButton.click();
    
    // テキスト編集モードを有効にする
    const textEditButton = page.locator('button:has-text("テキスト編集")');
    if (await textEditButton.isVisible()) {
      await textEditButton.click();
    }
    
    // Step 5: テキスト調整
    const editableElement = page.locator('[data-editable="true"]').first();
    await expect(editableElement).toBeVisible({ timeout: 5000 });
    
    // テキストを編集
    await editableElement.click();
    const input = page.locator('input, textarea').filter({ hasText: /.+/ }).first();
    if (await input.isVisible({ timeout: 3000 })) {
      await input.fill('');
      await input.type('統合フローテスト完了：最終的なヘッドライン');
      await input.press('Enter');
      
      // 変更の確認
      await expect(page.locator('text=統合フローテスト完了：最終的なヘッドライン')).toBeVisible({ timeout: 3000 });
    }
    
    // Step 6: 完成したLPの品質確認
    // 基本的な要素が存在することを確認
    const headingCount = await page.locator('h1, h2, h3').count();
    expect(headingCount).toBeGreaterThan(0);
    
    const buttonCount = await page.locator('button, [role="button"]').count();
    expect(buttonCount).toBeGreaterThan(0);
    
  });

  test('機能間のデータ連携確認', async ({ page }) => {
    // AIコンセプト提案で設定したデータがLP生成に反映されることを確認
    
    // 特定の商品特徴を含むコンセプト提案
    await expect(page.locator('text=AIコンセプト提案')).toBeVisible({ timeout: 10000 });
    
    const uniqueFeature = 'AI個別最適化学習システム';
    await page.locator('textarea[name="productOverview"]').fill(`${uniqueFeature}を核とした学習プラットフォーム`);
    await page.locator('input[name="targetAudience"]').fill('効率的学習を求める社会人');
    
    // AI分析実行
    await page.locator('button:has-text("AIコンセプト分析を開始")').click();
    await expect(page.locator('text=AI推奨コンセプト')).toBeVisible({ timeout: 60000 });
    
    // コンセプト採用
    await page.locator('button:has-text("このコンセプトでLP生成")').click();
    await expect(page.locator('h1, h2')).toBeVisible({ timeout: 30000 });
    
    // 生成されたLPに入力した特徴が反映されているか確認
    // （実際の実装に依存するため、部分的な文字列検索）
    const pageContent = await page.content();
    const hasRelatedContent = 
      pageContent.includes('AI') || 
      pageContent.includes('最適化') || 
      pageContent.includes('学習') ||
      pageContent.includes('効率');
    
    expect(hasRelatedContent).toBeTruthy();
  });

  test('状態管理の一貫性', async ({ page }) => {
    // 各ステップでの状態変化を追跡
    let currentStep = 'initial';
    
    // 初期状態
    await expect(page.locator('text=AIコンセプト提案')).toBeVisible({ timeout: 10000 });
    currentStep = 'concept-input';
    
    // フォーム入力
    await page.locator('textarea[name="productOverview"]').fill('状態管理テスト商品');
    await page.locator('input[name="targetAudience"]').fill('テストユーザー');
    
    // 分析開始
    await page.locator('button:has-text("AIコンセプト分析を開始")').click();
    currentStep = 'analyzing';
    
    // 分析中の状態確認
    await expect(page.locator('text=AI分析中')).toBeVisible({ timeout: 3000 });
    
    // 結果表示
    await expect(page.locator('text=AI推奨コンセプト')).toBeVisible({ timeout: 60000 });
    currentStep = 'concept-result';
    
    // LP生成へ進む
    await page.locator('button:has-text("このコンセプトでLP生成")').click();
    currentStep = 'lp-generation';
    
    // LP生成完了
    await expect(page.locator('h1, h2')).toBeVisible({ timeout: 30000 });
    currentStep = 'lp-completed';
    
    // 編集モード
    const editModeButton = page.locator('button:has-text("編集モード")');
    if (await editModeButton.isVisible()) {
      await editModeButton.click();
      currentStep = 'edit-mode';
    }
    
    expect(currentStep).toBe('edit-mode');
  });

  test('ユーザーフロー自然性', async ({ page }) => {
    // ユーザーが迷わずに操作できるかのフロー確認
    
    // Step 1: 明確な開始点
    await expect(page.locator('text=AIコンセプト提案')).toBeVisible({ timeout: 10000 });
    
    // Step 2: 必須項目の明確な表示
    const requiredFields = page.locator('label:has-text("*")');
    const requiredCount = await requiredFields.count();
    expect(requiredCount).toBeGreaterThan(0);
    
    // Step 3: 進行状況の可視化
    await page.locator('textarea[name="productOverview"]').fill('自然なフローテスト');
    await page.locator('input[name="targetAudience"]').fill('一般ユーザー');
    
    // ボタンの状態変化確認
    const analyzeButton = page.locator('button:has-text("AIコンセプト分析を開始")');
    await expect(analyzeButton).toBeEnabled();
    
    await analyzeButton.click();
    
    // Step 4: 処理中の適切なフィードバック
    await expect(page.locator('text=AI分析中')).toBeVisible({ timeout: 3000 });
    
    // Step 5: 結果の明確な表示
    await expect(page.locator('text=AI推奨コンセプト')).toBeVisible({ timeout: 60000 });
    
    // Step 6: 次のアクションの明確性
    const nextActionButtons = page.locator('button:has-text("このコンセプトでLP生成"), button:has-text("再分析")');
    await expect(nextActionButtons.first()).toBeVisible();
  });

  test('エラー処理と復旧フロー', async ({ page }) => {
    // ネットワークエラーからの復旧をテスト
    
    // 初回は正常に実行
    await expect(page.locator('text=AIコンセプト提案')).toBeVisible({ timeout: 10000 });
    
    await page.locator('textarea[name="productOverview"]').fill('エラー復旧テスト');
    await page.locator('input[name="targetAudience"]').fill('テストユーザー');
    
    // ネットワークエラーをシミュレート
    await page.route('**/api/chat-claude', route => {
      route.abort('failed');
    });
    
    await page.locator('button:has-text("AIコンセプト分析を開始")').click();
    
    // エラー処理の確認 - エラーメッセージの表示
    await expect(page.locator('text*="エラー"')).toBeVisible({ timeout: 10000 });
    
    // ネットワークを復旧
    await page.unroute('**/api/chat-claude');
    
    // 再試行可能な状態に戻ることを確認
    const analyzeButton = page.locator('button:has-text("AIコンセプト分析を開始")');
    await expect(analyzeButton).toBeEnabled({ timeout: 10000 });
    
    // 再試行
    await analyzeButton.click();
    
    // 正常に処理されることを確認
    await expect(page.locator('text=AI推奨コンセプト')).toBeVisible({ timeout: 60000 });
  });

  test('全体パフォーマンス測定', async ({ page }) => {
    const startTime = Date.now();
    
    // 完全フローの実行時間を測定
    await expect(page.locator('text=AIコンセプト提案')).toBeVisible({ timeout: 10000 });
    
    // フォーム入力（高速）
    await page.locator('textarea[name="productOverview"]').fill('パフォーマンステスト商品');
    await page.locator('input[name="targetAudience"]').fill('パフォーマンステストユーザー');
    
    const formTime = Date.now();
    
    // AI分析
    await page.locator('button:has-text("AIコンセプト分析を開始")').click();
    await expect(page.locator('text=AI推奨コンセプト')).toBeVisible({ timeout: 60000 });
    
    const analysisTime = Date.now();
    
    // LP生成
    await page.locator('button:has-text("このコンセプトでLP生成")').click();
    await expect(page.locator('h1, h2')).toBeVisible({ timeout: 30000 });
    
    const lpGenerationTime = Date.now();
    
    // 編集モード切り替え
    const editModeButton = page.locator('button:has-text("編集モード")');
    if (await editModeButton.isVisible()) {
      await editModeButton.click();
      await expect(page.locator('[data-editable="true"]')).toBeVisible({ timeout: 5000 });
    }
    
    const totalTime = Date.now() - startTime;
    const analysisOnlyTime = analysisTime - formTime;
    const lpGenerationOnlyTime = lpGenerationTime - analysisTime;
    
    console.log(`パフォーマンス測定結果:
      - 全体時間: ${totalTime}ms
      - AI分析時間: ${analysisOnlyTime}ms
      - LP生成時間: ${lpGenerationOnlyTime}ms`);
    
    // 環境変数から性能要件を取得（デフォルト値付き）
    const maxTotalTime = parseInt(process.env.E2E_MAX_TOTAL_TIME || '120000');
    const maxAnalysisTime = parseInt(process.env.E2E_MAX_ANALYSIS_TIME || '45000');
    const maxLpGenerationTime = parseInt(process.env.E2E_MAX_LP_GENERATION_TIME || '30000');
    
    // パフォーマンス要件の確認
    expect(totalTime).toBeLessThan(maxTotalTime); // 2分以内（設定可能）
    expect(analysisOnlyTime).toBeLessThan(maxAnalysisTime); // AI分析45秒以内（設定可能）
    expect(lpGenerationOnlyTime).toBeLessThan(maxLpGenerationTime); // LP生成30秒以内（設定可能）
  });

  test('複数セッションでの一貫性', async ({ page, context }) => {
    // 別タブで同じフローを実行
    const page2 = await context.newPage();
    
    // 両方のページで同じ操作を実行
    for (const testPage of [page, page2]) {
      await testPage.goto('/');
      await expect(testPage.locator('text=AIコンセプト提案')).toBeVisible({ timeout: 10000 });
      
      await testPage.locator('textarea[name="productOverview"]').fill('マルチセッションテスト');
      await testPage.locator('input[name="targetAudience"]').fill('テストユーザー');
      
      await testPage.locator('button:has-text("AIコンセプト分析を開始")').click();
      await expect(testPage.locator('text=AI推奨コンセプト')).toBeVisible({ timeout: 60000 });
    }
    
    // 両方のページが独立して動作することを確認
    await page2.close();
  });
});