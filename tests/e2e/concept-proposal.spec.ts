import { test, expect } from '@playwright/test';

test.describe('AIコンセプト提案機能', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('基本情報入力→AI分析→コンセプト提案の流れ', async ({ page }) => {
    // AIコンセプト提案コンポーネントが表示されることを確認
    await expect(page.locator('text=AIコンセプト提案')).toBeVisible({ timeout: 10000 });
    
    // 基本情報の入力
    const productOverviewInput = page.locator('textarea[name="productOverview"]');
    await productOverviewInput.fill('オンライン英会話サービス、AIを活用した個人向け学習プラットフォーム');
    
    const targetAudienceInput = page.locator('input[name="targetAudience"]');
    await targetAudienceInput.fill('30-40代のビジネスパーソン、英語学習に挫折経験のある人');
    
    const industryInput = page.locator('input[name="industry"]');
    await industryInput.fill('教育・EdTech');
    
    const budgetInput = page.locator('input[name="budget"]');
    await budgetInput.fill('月額50万円');
    
    const competitorsInput = page.locator('input[name="competitors"]');
    await competitorsInput.fill('DMM英会話、レアジョブ、ネイティブキャンプ');
    
    // AI分析開始ボタンをクリック
    const analyzeButton = page.locator('button:has-text("AIコンセプト分析を開始")');
    await expect(analyzeButton).toBeEnabled();
    await analyzeButton.click();
    
    // 分析中の表示を確認
    await expect(page.locator('text=AI分析中')).toBeVisible({ timeout: 3000 });
    await expect(analyzeButton).toBeDisabled();
    
    // 結果の表示を待機（最大60秒）
    await expect(page.locator('text=AI推奨コンセプト')).toBeVisible({ timeout: 60000 });
    
    // コンセプト提案結果の内容確認
    await expect(page.locator('[data-testid="confidence-score"]')).toBeVisible();
    await expect(page.locator('[data-testid="conversion-lift"]')).toBeVisible();
    await expect(page.locator('[data-testid="differentiators"]')).toBeVisible();
    await expect(page.locator('[data-testid="actionable-insights"]')).toBeVisible();
    await expect(page.locator('[data-testid="market-analysis"]')).toBeVisible();
    
    // アクションボタンの確認
    await expect(page.locator('button:has-text("このコンセプトでLP生成")')).toBeVisible();
    await expect(page.locator('button:has-text("再分析")')).toBeVisible();
  });

  test('必須フィールド検証', async ({ page }) => {
    await expect(page.locator('text=AIコンセプト提案')).toBeVisible({ timeout: 10000 });
    
    // 空の状態で送信ボタンが無効であることを確認
    const analyzeButton = page.locator('button:has-text("AIコンセプト分析を開始")');
    await expect(analyzeButton).toBeDisabled();
    
    // 商品概要のみ入力した場合
    const productOverviewInput = page.locator('textarea[name="productOverview"]');
    await productOverviewInput.fill('テスト商品');
    await expect(analyzeButton).toBeDisabled();
    
    // ターゲット層も入力した場合にボタンが有効になることを確認
    const targetAudienceInput = page.locator('input[name="targetAudience"]');
    await targetAudienceInput.fill('テストターゲット');
    await expect(analyzeButton).toBeEnabled();
  });

  test('異常ケース（空入力、API障害）の処理', async ({ page }) => {
    // ネットワークエラーをシミュレート
    await page.route('**/api/chat-claude', route => {
      route.abort('failed');
    });
    
    await expect(page.locator('text=AIコンセプト提案')).toBeVisible({ timeout: 10000 });
    
    // 必須フィールドを入力
    await page.locator('textarea[name="productOverview"]').fill('テスト商品');
    await page.locator('input[name="targetAudience"]').fill('テストターゲット');
    
    // 分析開始
    const analyzeButton = page.locator('button:has-text("AIコンセプト分析を開始")');
    await analyzeButton.click();
    
    // エラーハンドリングの確認（タイムアウトや適切なエラーメッセージ）
    await expect(page.locator('[data-testid="error-banner"]')).toBeVisible();
    
    // 分析中の状態から復帰することを確認
    await expect(analyzeButton).toBeEnabled({ timeout: 10000 });
  });

  test('コンセプト採用→LP生成連携', async ({ page }) => {
    // モックされたAIレスポンスを設定
    await page.route('**/api/chat-claude', route => {
      const mockResponse = {
        object: 'text_completion',
        choices: [{
          text: '{"headline":"革新的英会話学習","subheadline":"AIがあなたの学習スタイルに合わせて最適化","valueProposition":"忙しいビジネスパーソンでも続けられる","targetJustification":"30-40代の実用的学習ニーズ","marketAnalysis":"競合差別化が重要","reasoning":"AI個別最適化が鍵","confidenceScore":92,"expectedConversionLift":"25-30%","keyDifferentiators":["AI個別最適化","実践的ビジネス英語","短時間効率学習"],"actionableInsights":["CTA強化","無料体験強調","成果保証追加"]}'
        }]
      };
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockResponse)
      });
    });

    await expect(page.locator('text=AIコンセプト提案')).toBeVisible({ timeout: 10000 });
    
    // フォーム入力
    await page.locator('textarea[name="productOverview"]').fill('英会話学習サービス');
    await page.locator('input[name="targetAudience"]').fill('ビジネスパーソン');
    
    // 分析実行
    await page.locator('button:has-text("AIコンセプト分析を開始")').click();
    
    // 結果待機
    await expect(page.locator('text=AI推奨コンセプト')).toBeVisible({ timeout: 30000 });
    
    // LP生成ボタンをクリック
    const generateButton = page.locator('button:has-text("このコンセプトでLP生成")');
    await expect(generateButton).toBeVisible();
    await generateButton.click();
    
    // LP生成処理の開始を確認
    await page.waitForTimeout(2000);
    
    // 適切なページ遷移やLPコンテンツの生成を確認
    // （実際の実装に応じて調整）
  });

  test('パフォーマンス（分析時間30秒以内）', async ({ page }) => {
    await expect(page.locator('text=AIコンセプト提案')).toBeVisible({ timeout: 10000 });
    
    // フォーム入力
    await page.locator('textarea[name="productOverview"]').fill('パフォーマンステスト用商品');
    await page.locator('input[name="targetAudience"]').fill('テストターゲット');
    
    const startTime = Date.now();
    
    // 分析開始
    await page.locator('button:has-text("AIコンセプト分析を開始")').click();
    
    // 結果表示を待機
    await expect(page.locator('text=AI推奨コンセプト')).toBeVisible({ timeout: 35000 });
    
    const endTime = Date.now();
    const analysisTime = endTime - startTime;
    
    // 30秒以内であることを確認
    expect(analysisTime).toBeLessThan(30000);
  });

  test('再分析機能', async ({ page }) => {
    await expect(page.locator('text=AIコンセプト提案')).toBeVisible({ timeout: 10000 });
    
    // 初回分析
    await page.locator('textarea[name="productOverview"]').fill('テスト商品');
    await page.locator('input[name="targetAudience"]').fill('テストターゲット');
    await page.locator('button:has-text("AIコンセプト分析を開始")').click();
    
    // 結果待機
    await expect(page.locator('text=AI推奨コンセプト')).toBeVisible({ timeout: 30000 });
    
    // 再分析ボタンをクリック
    const reanalyzeButton = page.locator('button:has-text("再分析")');
    await expect(reanalyzeButton).toBeVisible();
    await reanalyzeButton.click();
    
    // フォームに戻ることを確認
    await expect(page.locator('textarea[name="productOverview"]')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('button:has-text("AIコンセプト分析を開始")')).toBeVisible();
  });

  test('レスポンシブ対応の確認', async ({ page }) => {
    // モバイルビューポートに設定
    await page.setViewportSize({ width: 375, height: 667 });
    
    await expect(page.locator('text=AIコンセプト提案')).toBeVisible({ timeout: 10000 });
    
    // フォーム要素がモバイルで適切に表示されることを確認
    const productOverviewInput = page.locator('textarea[name="productOverview"]');
    await expect(productOverviewInput).toBeVisible();
    
    const targetAudienceInput = page.locator('input[name="targetAudience"]');
    await expect(targetAudienceInput).toBeVisible();
    
    // ボタンがタップ可能であることを確認
    const analyzeButton = page.locator('button:has-text("AIコンセプト分析を開始")');
    await expect(analyzeButton).toBeVisible();
    
    // タブレットビューポートでも確認
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('text=AIコンセプト提案')).toBeVisible();
  });
});