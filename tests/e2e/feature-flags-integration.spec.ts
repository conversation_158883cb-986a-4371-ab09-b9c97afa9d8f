// tests/e2e/feature-flags-integration.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Feature Flags Integration', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the main page
    await page.goto('/');
  });

  test('should access feature flags API endpoint', async ({ page }) => {
    // Test the API endpoint directly
    const response = await page.request.get('/api/feature-flags');
    
    expect(response.status()).toBe(200);
    
    const flags = await response.json();
    
    // Verify response structure
    expect(flags).toHaveProperty('ENABLE_MASTRA');
    expect(flags).toHaveProperty('MASTRA_AGENT_TOOLS');
    expect(flags).toHaveProperty('MASTRA_MEMORY_SYSTEM');
    expect(flags).toHaveProperty('MASTRA_WORKFLOW_ENGINE');
    expect(flags).toHaveProperty('MASTRA_AB_TEST');
    
    // Verify types are boolean
    expect(typeof flags.ENABLE_MASTRA).toBe('boolean');
    expect(typeof flags.MASTRA_AGENT_TOOLS).toBe('boolean');
    expect(typeof flags.MASTRA_MEMORY_SYSTEM).toBe('boolean');
    expect(typeof flags.MASTRA_WORKFLOW_ENGINE).toBe('boolean');
    expect(typeof flags.MASTRA_AB_TEST).toBe('boolean');
  });

  test('should handle feature flags API with different environment states', async ({ page }) => {
    // Test that API consistently returns the same values
    const response1 = await page.request.get('/api/feature-flags');
    const response2 = await page.request.get('/api/feature-flags');
    
    const flags1 = await response1.json();
    const flags2 = await response2.json();
    
    expect(flags1).toEqual(flags2);
  });

  test('should have proper cache headers on feature flags endpoint', async ({ page }) => {
    const response = await page.request.get('/api/feature-flags');
    
    // Verify no-cache headers are set
    expect(response.headers()['cache-control']).toContain('no-cache');
    expect(response.headers()['pragma']).toBe('no-cache');
    expect(response.headers()['expires']).toBe('0');
  });

  test('should handle session ID persistence in localStorage', async ({ page }) => {
    // Execute JavaScript to check session ID behavior
    await page.evaluate(() => {
      // Clear any existing session ID
      localStorage.removeItem('mastra-session-id');
    });

    // Reload page to trigger session ID generation
    await page.reload();

    // Wait a bit for any React effects to run
    await page.waitForTimeout(100);

    // Check that session ID was created
    const sessionId1 = await page.evaluate(() => {
      return localStorage.getItem('mastra-session-id');
    });

    expect(sessionId1).toBeTruthy();
    expect(sessionId1).toMatch(/^session-/);

    // Reload page again and verify session ID persists
    await page.reload();
    await page.waitForTimeout(100);

    const sessionId2 = await page.evaluate(() => {
      return localStorage.getItem('mastra-session-id');
    });

    expect(sessionId2).toBe(sessionId1);
  });

  test('should handle feature flags gracefully when API fails', async ({ page }) => {
    // Mock API failure by intercepting the request
    await page.route('/api/feature-flags', (route) => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal Server Error' })
      });
    });

    await page.goto('/');
    
    // The page should still load despite API failure
    await expect(page.locator('body')).toBeVisible();
    
    // The application should handle the error gracefully
    // (specific behavior depends on how the React components handle errors)
  });

  test('should provide consistent A/B test assignment for same session', async ({ page }) => {
    // Set a specific session ID
    await page.evaluate(() => {
      localStorage.setItem('mastra-session-id', 'test-session-consistent');
    });

    await page.reload();
    await page.waitForTimeout(100);

    // Make multiple requests and verify consistency
    const results = [];
    for (let i = 0; i < 5; i++) {
      await page.reload();
      await page.waitForTimeout(50);
      
      // Here we would check for specific UI elements or behavior
      // that indicates which experiment group the user is in
      // For now, we just verify the session ID remains consistent
      const sessionId = await page.evaluate(() => {
        return localStorage.getItem('mastra-session-id');
      });
      
      results.push(sessionId);
    }

    // All session IDs should be the same
    expect(new Set(results).size).toBe(1);
    expect(results[0]).toBe('test-session-consistent');
  });

  test('should handle different session IDs for A/B testing', async ({ page }) => {
    const testResults = [];

    // Test multiple different session IDs
    for (let i = 0; i < 10; i++) {
      await page.evaluate((index) => {
        localStorage.setItem('mastra-session-id', `test-session-${index}`);
      }, i);

      await page.reload();
      await page.waitForTimeout(50);

      const sessionId = await page.evaluate(() => {
        return localStorage.getItem('mastra-session-id');
      });

      testResults.push(sessionId);
    }

    // Verify we got different session IDs
    expect(new Set(testResults).size).toBe(10);
  });
});