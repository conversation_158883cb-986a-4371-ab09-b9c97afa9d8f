import { test, expect } from '@playwright/test';

test.describe('DraggableImage E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Setup test page with DraggableImage component
    await page.goto('/test-draggable-image'); // テスト用ページのURL
  });

  test.describe('Desktop Interactions', () => {
    test('should drag image with mouse', async ({ page }) => {
      // 編集モードを有効化
      await page.click('[data-testid="edit-mode-toggle"]');
      
      // 画像を選択
      const image = page.locator('[data-testid="draggable-image-test"]');
      await expect(image).toBeVisible();
      
      // 初期位置を取得
      const initialBox = await image.boundingBox();
      expect(initialBox).toBeTruthy();
      
      // ドラッグ操作
      await image.hover();
      await page.mouse.down();
      await page.mouse.move(initialBox!.x + 100, initialBox!.y + 50);
      await page.mouse.up();
      
      // 位置が変更されたことを確認
      const newBox = await image.boundingBox();
      expect(newBox!.x).toBe(initialBox!.x + 100);
      expect(newBox!.y).toBe(initialBox!.y + 50);
    });

    test('should resize image with handles', async ({ page }) => {
      await page.click('[data-testid="edit-mode-toggle"]');
      
      const image = page.locator('[data-testid="draggable-image-test"]');
      await image.click(); // 選択状態にする
      
      // リサイズハンドルが表示されることを確認
      const resizeHandle = page.locator('[data-testid="resize-handle-se"]');
      await expect(resizeHandle).toBeVisible();
      
      // 初期サイズを取得
      const initialBox = await image.boundingBox();
      
      // リサイズ操作（右下ハンドル）
      await resizeHandle.hover();
      await page.mouse.down();
      await page.mouse.move(
        initialBox!.x + initialBox!.width + 50,
        initialBox!.y + initialBox!.height + 30
      );
      await page.mouse.up();
      
      // サイズが変更されたことを確認
      const newBox = await image.boundingBox();
      expect(newBox!.width).toBe(initialBox!.width + 50);
      expect(newBox!.height).toBe(initialBox!.height + 30);
    });

    test('should maintain aspect ratio with shift key', async ({ page }) => {
      await page.click('[data-testid="edit-mode-toggle"]');
      
      const image = page.locator('[data-testid="draggable-image-test"]');
      await image.click();
      
      const resizeHandle = page.locator('[data-testid="resize-handle-se"]');
      const initialBox = await image.boundingBox();
      const initialAspectRatio = initialBox!.width / initialBox!.height;
      
      // Shiftキーを押しながらリサイズ
      await page.keyboard.down('Shift');
      await resizeHandle.hover();
      await page.mouse.down();
      await page.mouse.move(
        initialBox!.x + initialBox!.width + 100,
        initialBox!.y + initialBox!.height + 100
      );
      await page.mouse.up();
      await page.keyboard.up('Shift');
      
      const newBox = await image.boundingBox();
      const newAspectRatio = newBox!.width / newBox!.height;
      
      // アスペクト比が保持されていることを確認（誤差許容）
      expect(Math.abs(newAspectRatio - initialAspectRatio)).toBeLessThan(0.01);
    });
  });

  test.describe('Mobile Touch Interactions', () => {
    test('should drag image with touch', async ({ page, isMobile }) => {
      test.skip(!isMobile, 'Mobile-specific test');
      
      await page.click('[data-testid="edit-mode-toggle"]');
      
      const image = page.locator('[data-testid="draggable-image-test"]');
      const initialBox = await image.boundingBox();
      
      // 長押し（500ms）してドラッグモードに入る
      await image.touchScreen.tap();
      await page.waitForTimeout(600); // 長押し判定時間を超える
      
      // タッチドラッグ
      await image.touchScreen.dragTo(
        page.locator('body'),
        { 
          targetPosition: { 
            x: initialBox!.x + 100, 
            y: initialBox!.y + 50 
          } 
        }
      );
      
      const newBox = await image.boundingBox();
      expect(newBox!.x).toBe(initialBox!.x + 100);
      expect(newBox!.y).toBe(initialBox!.y + 50);
    });
  });

  test.describe('Keyboard Navigation', () => {
    test('should move image with arrow keys', async ({ page }) => {
      await page.click('[data-testid="edit-mode-toggle"]');
      
      const image = page.locator('[data-testid="draggable-image-test"]');
      await image.click(); // フォーカス
      
      const initialBox = await image.boundingBox();
      
      // 矢印キーで移動
      await page.keyboard.press('ArrowRight'); // 10px移動
      await page.keyboard.press('ArrowDown');  // 10px移動
      
      const newBox = await image.boundingBox();
      expect(newBox!.x).toBe(initialBox!.x + 10);
      expect(newBox!.y).toBe(initialBox!.y + 10);
    });

    test('should resize with shift+arrow keys', async ({ page }) => {
      await page.click('[data-testid="edit-mode-toggle"]');
      
      const image = page.locator('[data-testid="draggable-image-test"]');
      await image.click();
      
      const initialBox = await image.boundingBox();
      
      // Shift+矢印キーでリサイズ
      await page.keyboard.press('Shift+ArrowRight');
      await page.keyboard.press('Shift+ArrowDown');
      
      const newBox = await image.boundingBox();
      expect(newBox!.width).toBe(initialBox!.width + 10);
      expect(newBox!.height).toBe(initialBox!.height + 10);
    });

    test('should deselect with escape key', async ({ page }) => {
      await page.click('[data-testid="edit-mode-toggle"]');
      
      const image = page.locator('[data-testid="draggable-image-test"]');
      await image.click();
      
      // 選択状態であることを確認
      await expect(image).toHaveClass(/selected/);
      
      // Escapeで選択解除
      await page.keyboard.press('Escape');
      
      // 選択が解除されたことを確認
      await expect(image).not.toHaveClass(/selected/);
    });
  });

  test.describe('Accessibility', () => {
    test('should have proper ARIA attributes', async ({ page }) => {
      await page.click('[data-testid="edit-mode-toggle"]');
      
      const image = page.locator('[data-testid="draggable-image-test"]');
      
      // ARIA属性の確認
      await expect(image).toHaveAttribute('role', 'button');
      await expect(image).toHaveAttribute('aria-label');
      await expect(image).toHaveAttribute('tabindex', '0');
    });

    test('should announce actions to screen readers', async ({ page }) => {
      await page.click('[data-testid="edit-mode-toggle"]');
      
      const image = page.locator('[data-testid="draggable-image-test"]');
      await image.click();
      
      // ARIA live regionの確認
      const liveRegion = page.locator('[aria-live="polite"]');
      await expect(liveRegion).toBeVisible();
    });

    test('should be navigable by keyboard only', async ({ page }) => {
      await page.click('[data-testid="edit-mode-toggle"]');
      
      // Tabキーでナビゲーション
      await page.keyboard.press('Tab');
      const focusedElement = page.locator(':focus');
      
      // 画像がフォーカスされていることを確認
      await expect(focusedElement).toHaveAttribute('data-testid', 'draggable-image-test');
    });
  });

  test.describe('Error Handling', () => {
    test('should handle image load errors gracefully', async ({ page }) => {
      // 無効な画像URLでテスト
      await page.route('**/invalid-image.jpg', route => route.abort());
      
      // ページに無効な画像を追加
      await page.evaluate(() => {
        // ここで無効な画像データをコンポーネントに設定
      });
      
      // フォールバック画像が表示されることを確認
      const fallbackImage = page.getByAltText('画像が読み込めませんでした');
      await expect(fallbackImage).toBeVisible();
    });
  });

  test.describe('Performance', () => {
    test('should handle multiple images without performance issues', async ({ page }) => {
      await page.click('[data-testid="edit-mode-toggle"]');
      
      // 複数画像の同時操作テスト
      const images = page.locator('[data-testid^="draggable-image-"]');
      const imageCount = await images.count();
      
      // 各画像を順次ドラッグ
      for (let i = 0; i < imageCount; i++) {
        const image = images.nth(i);
        await image.hover();
        await page.mouse.down();
        await page.mouse.move(100 + i * 10, 100 + i * 10);
        await page.mouse.up();
        
        // パフォーマンスチェック - 操作が100ms以内に完了することを確認
        const startTime = Date.now();
        await page.waitForTimeout(10);
        const endTime = Date.now();
        expect(endTime - startTime).toBeLessThan(100);
      }
    });

    test('should not cause memory leaks', async ({ page }) => {
      // メモリリークテスト
      // 大量の画像操作を行った後、メモリ使用量が適切であることを確認
      
      await page.click('[data-testid="edit-mode-toggle"]');
      
      const image = page.locator('[data-testid="draggable-image-test"]');
      
      // 100回のドラッグ操作
      for (let i = 0; i < 100; i++) {
        await image.hover();
        await page.mouse.down();
        await page.mouse.move(100 + (i % 10), 100 + (i % 10));
        await page.mouse.up();
      }
      
      // ページが正常に動作していることを確認
      await expect(image).toBeVisible();
    });
  });
}); 