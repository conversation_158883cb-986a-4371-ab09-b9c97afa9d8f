import { test, expect } from '@playwright/test';
import path from 'path';

test.describe('ドラッグ&ドロップ機能', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('ドラッグ&ドロップ基盤機能の確認', async ({ page }) => {
    // LP生成
    const chatInput = page.locator('textarea, input[type="text"]').first();
    await chatInput.fill('テスト用のヒーローセクションを作って');
    await page.keyboard.press('Enter');
    await page.waitForTimeout(3000);
    
    // 編集モードを有効にする
    const editModeButton = page.locator('button:has-text("編集モード")');
    await editModeButton.waitFor({ timeout: 10000 });
    await editModeButton.click();
    
    // 画像を挿入
    const heroSection = page.locator('[style*="background"]').first();
    await heroSection.waitFor({ timeout: 5000 });
    await heroSection.click();
    
    // モーダルが開いたら画像を選択
    await expect(page.locator('text=画像を選択')).toBeVisible({ timeout: 5000 });
    
    const searchInput = page.locator('input[placeholder*="画像を検索"]');
    await searchInput.fill('business');
    await page.locator('button:has-text("検索")').click();
    
    // 画像を選択
    await page.waitForTimeout(2000);
    const firstImage = page.locator('img').first();
    await firstImage.waitFor({ timeout: 10000 });
    await firstImage.click();
    
    // ドラッグ可能な画像が表示されることを確認
    await page.waitForTimeout(1000);
    const draggableImage = page.locator('div[style*="position: absolute"]');
    await expect(draggableImage).toBeVisible({ timeout: 5000 });
  });

  test('画像のドラッグ移動機能', async ({ page }) => {
    // LP生成と画像挿入
    const chatInput = page.locator('textarea, input[type="text"]').first();
    await chatInput.fill('ヒーローセクション作って');
    await page.keyboard.press('Enter');
    await page.waitForTimeout(3000);
    
    const editModeButton = page.locator('button:has-text("編集モード")');
    await editModeButton.waitFor({ timeout: 10000 });
    await editModeButton.click();
    
    const heroSection = page.locator('[style*="background"]').first();
    await heroSection.waitFor({ timeout: 5000 });
    await heroSection.click();
    
    await expect(page.locator('text=画像を選択')).toBeVisible({ timeout: 5000 });
    
    const searchInput = page.locator('input[placeholder*="画像を検索"]');
    await searchInput.fill('business');
    await page.locator('button:has-text("検索")').click();
    await page.waitForTimeout(2000);
    
    const firstImage = page.locator('img').first();
    await firstImage.waitFor({ timeout: 10000 });
    await firstImage.click();
    
    // ドラッグ可能な画像を確認
    await page.waitForTimeout(1000);
    const draggableImage = page.locator('div[style*="position: absolute"]').first();
    await expect(draggableImage).toBeVisible({ timeout: 5000 });
    
    // 画像の初期位置を取得
    const initialBoundingBox = await draggableImage.boundingBox();
    
    // 画像をドラッグして移動
    await draggableImage.hover();
    await page.mouse.down();
    await page.mouse.move(initialBoundingBox!.x + 100, initialBoundingBox!.y + 50);
    await page.mouse.up();
    
    // 位置が変更されたことを確認
    await page.waitForTimeout(500);
    const newBoundingBox = await draggableImage.boundingBox();
    expect(newBoundingBox!.x).not.toBe(initialBoundingBox!.x);
  });

  test('リサイズハンドルの表示と動作', async ({ page }) => {
    // LP生成と画像挿入
    const chatInput = page.locator('textarea, input[type="text"]').first();
    await chatInput.fill('ヒーローセクション作って');
    await page.keyboard.press('Enter');
    await page.waitForTimeout(3000);
    
    const editModeButton = page.locator('button:has-text("編集モード")');
    await editModeButton.waitFor({ timeout: 10000 });
    await editModeButton.click();
    
    const heroSection = page.locator('[style*="background"]').first();
    await heroSection.waitFor({ timeout: 5000 });
    await heroSection.click();
    
    await expect(page.locator('text=画像を選択')).toBeVisible({ timeout: 5000 });
    
    const searchInput = page.locator('input[placeholder*="画像を検索"]');
    await searchInput.fill('business');
    await page.locator('button:has-text("検索")').click();
    await page.waitForTimeout(2000);
    
    const firstImage = page.locator('img').first();
    await firstImage.waitFor({ timeout: 10000 });
    await firstImage.click();
    
    // ドラッグ可能な画像にホバー
    await page.waitForTimeout(1000);
    const draggableImage = page.locator('div[style*="position: absolute"]').first();
    await draggableImage.hover();
    
    // リサイズハンドルが表示されることを確認
    const resizeHandles = page.locator('div[style*="cursor: se-resize"], div[style*="cursor: nw-resize"]');
    await expect(resizeHandles.first()).toBeVisible({ timeout: 3000 });
    
    // 編集インジケーターが表示されることを確認
    const editIndicator = page.locator('text=ドラッグ・リサイズ可能');
    await expect(editIndicator).toBeVisible({ timeout: 3000 });
  });

  test('ファイルドロップ機能', async ({ page }) => {
    // LP生成
    const chatInput = page.locator('textarea, input[type="text"]').first();
    await chatInput.fill('ヒーローセクション作って');
    await page.keyboard.press('Enter');
    await page.waitForTimeout(3000);
    
    // 編集モードを有効にする
    const editModeButton = page.locator('button:has-text("編集モード")');
    await editModeButton.waitFor({ timeout: 10000 });
    await editModeButton.click();
    
    // プレースホルダーエリアが表示されることを確認
    const placeholder = page.locator('text=画像を挿入');
    await expect(placeholder).toBeVisible({ timeout: 5000 });
    
    // ドロップゾーンのヒントが表示されることを確認
    const dropHint = page.locator('text=またはファイルをドラッグ&ドロップ');
    await expect(dropHint).toBeVisible({ timeout: 3000 });
    
    // DropZoneコンテナが存在することを確認
    const dropZone = page.locator('div').filter({ hasText: 'またはファイルをドラッグ&ドロップ' }).first();
    await expect(dropZone).toBeVisible();
  });

  test('複数画像のドロップ処理', async ({ page }) => {
    // LP生成
    const chatInput = page.locator('textarea, input[type="text"]').first();
    await chatInput.fill('ヒーローセクション作って');
    await page.keyboard.press('Enter');
    await page.waitForTimeout(3000);
    
    // 編集モードを有効にする
    const editModeButton = page.locator('button:has-text("編集モード")');
    await editModeButton.waitFor({ timeout: 10000 });
    await editModeButton.click();
    
    // プレースホルダーエリアを確認
    const placeholder = page.locator('text=画像を挿入');
    await expect(placeholder).toBeVisible({ timeout: 5000 });
    
    // 注意: 実際のファイルドロップはE2Eテストでは難しいため、
    // UIが正しく表示されることを確認
    const dropZone = page.locator('div').filter({ hasText: 'またはファイルをドラッグ&ドロップ' });
    await expect(dropZone).toBeVisible();
  });

  test('モバイルタッチ対応の確認', async ({ page }) => {
    // モバイルビューポートに設定
    await page.setViewportSize({ width: 375, height: 667 });
    
    // LP生成と画像挿入
    const chatInput = page.locator('textarea, input[type="text"]').first();
    await chatInput.fill('ヒーローセクション作って');
    await page.keyboard.press('Enter');
    await page.waitForTimeout(3000);
    
    const editModeButton = page.locator('button:has-text("編集モード")');
    await editModeButton.waitFor({ timeout: 10000 });
    await editModeButton.click();
    
    const heroSection = page.locator('[style*="background"]').first();
    await heroSection.waitFor({ timeout: 5000 });
    await heroSection.click();
    
    await expect(page.locator('text=画像を選択')).toBeVisible({ timeout: 5000 });
    
    const searchInput = page.locator('input[placeholder*="画像を検索"]');
    await searchInput.fill('business');
    await page.locator('button:has-text("検索")').click();
    await page.waitForTimeout(2000);
    
    const firstImage = page.locator('img').first();
    await firstImage.waitFor({ timeout: 10000 });
    await firstImage.click();
    
    // ドラッグ可能な画像が表示されることを確認
    await page.waitForTimeout(1000);
    const draggableImage = page.locator('div[style*="position: absolute"]');
    await expect(draggableImage).toBeVisible({ timeout: 5000 });
    
    // タッチイベントが処理されることを確認（UI表示のみ）
    await draggableImage.tap();
    
    // モバイルでもリサイズハンドルが適切に表示されることを確認
    const resizeHandles = page.locator('div[style*="cursor: se-resize"], div[style*="cursor: nw-resize"]');
    await expect(resizeHandles.first()).toBeVisible({ timeout: 3000 });
  });

  test('ドラッグ中の視覚的フィードバック', async ({ page }) => {
    // LP生成と画像挿入
    const chatInput = page.locator('textarea, input[type="text"]').first();
    await chatInput.fill('ヒーローセクション作って');
    await page.keyboard.press('Enter');
    await page.waitForTimeout(3000);
    
    const editModeButton = page.locator('button:has-text("編集モード")');
    await editModeButton.waitFor({ timeout: 10000 });
    await editModeButton.click();
    
    const heroSection = page.locator('[style*="background"]').first();
    await heroSection.waitFor({ timeout: 5000 });
    await heroSection.click();
    
    await expect(page.locator('text=画像を選択')).toBeVisible({ timeout: 5000 });
    
    const searchInput = page.locator('input[placeholder*="画像を検索"]');
    await searchInput.fill('business');
    await page.locator('button:has-text("検索")').click();
    await page.waitForTimeout(2000);
    
    const firstImage = page.locator('img').first();
    await firstImage.waitFor({ timeout: 10000 });
    await firstImage.click();
    
    // ドラッグ可能な画像にホバー
    await page.waitForTimeout(1000);
    const draggableImage = page.locator('div[style*="position: absolute"]').first();
    await draggableImage.hover();
    
    // ホバー時のスタイル変更を確認
    const imageStyle = await draggableImage.getAttribute('style');
    expect(imageStyle).toContain('border: 2px solid #2196f3');
    
    // 編集インジケーターが表示されることを確認
    const editIndicator = page.locator('text=ドラッグ・リサイズ可能');
    await expect(editIndicator).toBeVisible({ timeout: 3000 });
  });

  test('境界制限とグリッドスナップ', async ({ page }) => {
    // LP生成と画像挿入
    const chatInput = page.locator('textarea, input[type="text"]').first();
    await chatInput.fill('ヒーローセクション作って');
    await page.keyboard.press('Enter');
    await page.waitForTimeout(3000);
    
    const editModeButton = page.locator('button:has-text("編集モード")');
    await editModeButton.waitFor({ timeout: 10000 });
    await editModeButton.click();
    
    const heroSection = page.locator('[style*="background"]').first();
    await heroSection.waitFor({ timeout: 5000 });
    await heroSection.click();
    
    await expect(page.locator('text=画像を選択')).toBeVisible({ timeout: 5000 });
    
    const searchInput = page.locator('input[placeholder*="画像を検索"]');
    await searchInput.fill('business');
    await page.locator('button:has-text("検索")').click();
    await page.waitForTimeout(2000);
    
    const firstImage = page.locator('img').first();
    await firstImage.waitFor({ timeout: 10000 });
    await firstImage.click();
    
    await page.waitForTimeout(1000);
    const draggableImage = page.locator('div[style*="position: absolute"]').first();
    
    // 画像をビューポートの境界まで移動を試行
    const initialBoundingBox = await draggableImage.boundingBox();
    
    await draggableImage.hover();
    await page.mouse.down();
    // 画面の右端外に移動を試行
    await page.mouse.move(page.viewportSize()!.width + 100, initialBoundingBox!.y);
    await page.mouse.up();
    
    // 境界内に制限されていることを確認
    await page.waitForTimeout(500);
    const newBoundingBox = await draggableImage.boundingBox();
    expect(newBoundingBox!.x + newBoundingBox!.width).toBeLessThanOrEqual(page.viewportSize()!.width);
  });
});