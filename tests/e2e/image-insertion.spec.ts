import { test, expect } from '@playwright/test';

test.describe('画像挿入機能', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('編集モードの切り替えができる', async ({ page }) => {
    // 最初はLP生成が必要なので、メッセージがあるかチェック
    await page.waitForSelector('h1', { timeout: 10000 });
    
    // チャットエリアに何かメッセージを送信してLPを生成する
    const chatInput = page.locator('textarea, input[type="text"]').first();
    await chatInput.fill('テスト用のヒーローセクションを作って');
    await page.keyboard.press('Enter');
    
    // LP生成を待つ（タイムアウトを長めに設定）
    await page.waitForTimeout(3000);
    
    // 編集モードボタンが表示されるかチェック
    const editModeButton = page.locator('button:has-text("編集モード")');
    await editModeButton.waitFor({ timeout: 10000 });
    
    // 編集モードをオンにする
    await editModeButton.click();
    await expect(page.locator('button:has-text("編集終了")')).toBeVisible();
  });

  test('画像選択モーダルが開ける', async ({ page }) => {
    // LP生成
    const chatInput = page.locator('textarea, input[type="text"]').first();
    await chatInput.fill('ヒーローセクション作って');
    await page.keyboard.press('Enter');
    await page.waitForTimeout(3000);
    
    // 編集モードを有効にする
    const editModeButton = page.locator('button:has-text("編集モード")');
    await editModeButton.waitFor({ timeout: 10000 });
    await editModeButton.click();
    
    // EditableImageContainerをクリック（ヒーローセクション）
    const heroSection = page.locator('[style*="background"]').first();
    await heroSection.waitFor({ timeout: 5000 });
    await heroSection.click();
    
    // 画像選択モーダルが開くことを確認
    await expect(page.locator('text=画像を選択')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('text=Unsplash検索')).toBeVisible();
    await expect(page.locator('text=ファイルアップロード')).toBeVisible();
    await expect(page.locator('text=AI画像生成')).toBeVisible();
  });

  test('Unsplash検索ができる', async ({ page }) => {
    // LP生成
    const chatInput = page.locator('textarea, input[type="text"]').first();
    await chatInput.fill('ヒーローセクション作って');
    await page.keyboard.press('Enter');
    await page.waitForTimeout(3000);
    
    // 編集モードを有効にする
    const editModeButton = page.locator('button:has-text("編集モード")');
    await editModeButton.waitFor({ timeout: 10000 });
    await editModeButton.click();
    
    // ヒーローセクションをクリック
    const heroSection = page.locator('[style*="background"]').first();
    await heroSection.waitFor({ timeout: 5000 });
    await heroSection.click();
    
    // モーダルが開いたら検索を実行
    await expect(page.locator('text=画像を選択')).toBeVisible({ timeout: 5000 });
    
    const searchInput = page.locator('input[placeholder*="画像を検索"]');
    await searchInput.fill('business');
    await page.locator('button:has-text("検索")').click();
    
    // 検索結果の画像が表示されることを確認
    await page.waitForTimeout(2000);
    const images = page.locator('img').filter({ hasText: /business|photographer/ });
    await expect(images.first()).toBeVisible({ timeout: 10000 });
  });

  test('画像選択後にモーダルが閉じる', async ({ page }) => {
    // LP生成
    const chatInput = page.locator('textarea, input[type="text"]').first();
    await chatInput.fill('ヒーローセクション作って');
    await page.keyboard.press('Enter');
    await page.waitForTimeout(3000);
    
    // 編集モードを有効にする
    const editModeButton = page.locator('button:has-text("編集モード")');
    await editModeButton.waitFor({ timeout: 10000 });
    await editModeButton.click();
    
    // ヒーローセクションをクリック
    const heroSection = page.locator('[style*="background"]').first();
    await heroSection.waitFor({ timeout: 5000 });
    await heroSection.click();
    
    // モーダルが開いたら画像を選択
    await expect(page.locator('text=画像を選択')).toBeVisible({ timeout: 5000 });
    
    const searchInput = page.locator('input[placeholder*="画像を検索"]');
    await searchInput.fill('business');
    await page.locator('button:has-text("検索")').click();
    
    // 最初の画像をクリック
    await page.waitForTimeout(2000);
    const firstImage = page.locator('img').first();
    await firstImage.waitFor({ timeout: 10000 });
    await firstImage.click();
    
    // モーダルが閉じることを確認
    await expect(page.locator('text=画像を選択')).not.toBeVisible({ timeout: 5000 });
  });

  test('ファイルアップロードタブが機能する', async ({ page }) => {
    // LP生成
    const chatInput = page.locator('textarea, input[type="text"]').first();
    await chatInput.fill('ヒーローセクション作って');
    await page.keyboard.press('Enter');
    await page.waitForTimeout(3000);
    
    // 編集モードを有効にする
    const editModeButton = page.locator('button:has-text("編集モード")');
    await editModeButton.waitFor({ timeout: 10000 });
    await editModeButton.click();
    
    // ヒーローセクションをクリック
    const heroSection = page.locator('[style*="background"]').first();
    await heroSection.waitFor({ timeout: 5000 });
    await heroSection.click();
    
    // ファイルアップロードタブをクリック
    await expect(page.locator('text=画像を選択')).toBeVisible({ timeout: 5000 });
    await page.locator('button:has-text("ファイルアップロード")').click();
    
    // ファイルアップロードエリアが表示されることを確認
    await expect(page.locator('text=ファイルを選択してアップロード')).toBeVisible();
    await expect(page.locator('text=クリックしてファイルを選択')).toBeVisible();
  });

  test('AI画像生成タブが機能する', async ({ page }) => {
    // LP生成
    const chatInput = page.locator('textarea, input[type="text"]').first();
    await chatInput.fill('ヒーローセクション作って');
    await page.keyboard.press('Enter');
    await page.waitForTimeout(3000);
    
    // 編集モードを有効にする
    const editModeButton = page.locator('button:has-text("編集モード")');
    await editModeButton.waitFor({ timeout: 10000 });
    await editModeButton.click();
    
    // ヒーローセクションをクリック
    const heroSection = page.locator('[style*="background"]').first();
    await heroSection.waitFor({ timeout: 5000 });
    await heroSection.click();
    
    // AI画像生成タブをクリック
    await expect(page.locator('text=画像を選択')).toBeVisible({ timeout: 5000 });
    await page.locator('button:has-text("AI画像生成")').click();
    
    // AI画像生成エリアが表示されることを確認
    await expect(page.locator('text=AI画像生成プロンプト')).toBeVisible();
    await expect(page.locator('textarea[placeholder*="professional business"]')).toBeVisible();
    await expect(page.locator('button:has-text("画像を生成")')).toBeVisible();
  });

  test('Claude版でも画像挿入機能が動作する', async ({ page }) => {
    await page.goto('/claude');
    
    // LP生成
    const chatInput = page.locator('textarea, input[type="text"]').first();
    await chatInput.fill('テスト用のヒーローセクションを作って');
    await page.keyboard.press('Enter');
    await page.waitForTimeout(3000);
    
    // 編集モードボタンが表示されるかチェック
    const editModeButton = page.locator('button:has-text("編集モード")');
    await editModeButton.waitFor({ timeout: 10000 });
    
    // 編集モードをオンにする
    await editModeButton.click();
    await expect(page.locator('button:has-text("編集終了")')).toBeVisible();
    
    // ヒーローセクションをクリック
    const heroSection = page.locator('[style*="background"]').first();
    await heroSection.waitFor({ timeout: 5000 });
    await heroSection.click();
    
    // 画像選択モーダルが開くことを確認
    await expect(page.locator('text=画像を選択')).toBeVisible({ timeout: 5000 });
  });
});