/**
 * AI品質テスト用ヘルパー関数
 * AI提案コンセプトの品質評価と検証を行う
 */

import React from 'react';

export interface ConceptData {
  headline: string;
  subheadline: string;
  valueProposition: string;
  targetJustification: string;
  marketAnalysis: string;
  reasoning: string;
  confidenceScore: number;
  expectedConversionLift: string;
  keyDifferentiators: string[];
  actionableInsights: string[];
}

export interface QualityMetrics {
  conceptRelevance: number;      // 提案コンセプトが入力に適合している
  logicalConsistency: number;    // 提案理由が論理的で一貫している
  marketAlignment: number;       // 市場ニーズに適合している
  creativityLevel: number;       // 適度な創造性がある
  practicalUsability: number;    // 実用的で実装可能
  overallScore: number;          // 総合評価スコア
}

/**
 * AIコンセプト提案の品質を評価する
 */
export function evaluateConceptQuality(
  concept: ConceptData,
  inputData: Record<string, string>
): QualityMetrics {
  const metrics: QualityMetrics = {
    conceptRelevance: 0,
    logicalConsistency: 0,
    marketAlignment: 0,
    creativityLevel: 0,
    practicalUsability: 0,
    overallScore: 0
  };

  // 1. コンセプト関連性の評価
  metrics.conceptRelevance = evaluateConceptRelevance(concept, inputData);

  // 2. 論理的一貫性の評価
  metrics.logicalConsistency = evaluateLogicalConsistency(concept);

  // 3. 市場適合性の評価
  metrics.marketAlignment = evaluateMarketAlignment(concept, inputData);

  // 4. 創造性レベルの評価
  metrics.creativityLevel = evaluateCreativityLevel(concept);

  // 5. 実用性の評価
  metrics.practicalUsability = evaluatePracticalUsability(concept);

  // 総合スコア計算
  metrics.overallScore = calculateOverallScore(metrics);

  return metrics;
}

/**
 * コンセプト関連性を評価
 */
function evaluateConceptRelevance(concept: ConceptData, inputData: Record<string, string>): number {
  let score = 0;
  const maxScore = 100;

  // 商品概要との関連性チェック
  if (inputData.productOverview) {
    const productKeywords = extractKeywords(inputData.productOverview);
    const conceptText = `${concept.headline} ${concept.subheadline} ${concept.valueProposition}`;
    const matchedKeywords = productKeywords.filter(keyword => 
      conceptText.toLowerCase().includes(keyword.toLowerCase())
    );
    if (productKeywords.length > 0) {
      score += (matchedKeywords.length / productKeywords.length) * 40;
    }
  }

  // ターゲット層との整合性チェック
  if (inputData.targetAudience) {
    const targetKeywords = extractKeywords(inputData.targetAudience);
    const targetText = concept.targetJustification.toLowerCase();
    const matchedTargets = targetKeywords.filter(keyword => 
      targetText.includes(keyword.toLowerCase())
    );
    score += (matchedTargets.length / targetKeywords.length) * 30;
  }

  // 業界との関連性チェック
  if (inputData.industry) {
    const industryKeywords = extractKeywords(inputData.industry);
    const marketText = concept.marketAnalysis.toLowerCase();
    const matchedIndustry = industryKeywords.filter(keyword => 
      marketText.includes(keyword.toLowerCase())
    );
    score += (matchedIndustry.length / industryKeywords.length) * 30;
  }

  return Math.min(score, maxScore);
}

/**
 * 論理的一貫性を評価
 */
function evaluateLogicalConsistency(concept: ConceptData): number {
  let score = 0;

  // ヘッドラインとサブヘッドラインの整合性
  if (isConsistent(concept.headline, concept.subheadline)) {
    score += 25;
  }

  // 価値提案と差別化ポイントの整合性
  if (concept.keyDifferentiators.some(diff => 
    isRelated(diff, concept.valueProposition)
  )) {
    score += 25;
  }

  // 推論と結論の論理性
  if (isLogicalReasoning(concept.reasoning, concept.confidenceScore)) {
    score += 25;
  }

  // アクションプランの実現可能性
  if (areActionableInsightsRealistic(concept.actionableInsights)) {
    score += 25;
  }

  return score;
}

/**
 * 市場適合性を評価
 */
function evaluateMarketAlignment(concept: ConceptData, inputData: Record<string, string>): number {
  let score = 0;

  // 競合分析の深度
  if (inputData.competitors && concept.marketAnalysis.length > 100) {
    score += 30;
  }

  // ターゲット分析の具体性
  if (concept.targetJustification.length > 50 && 
      containsSpecificTerms(concept.targetJustification)) {
    score += 30;
  }

  // CV向上率の現実性
  const conversionLift = parseConversionLift(concept.expectedConversionLift);
  if (conversionLift > 0 && conversionLift <= 100) {
    score += 40;
  }

  return score;
}

/**
 * 創造性レベルを評価
 */
function evaluateCreativityLevel(concept: ConceptData): number {
  let score = 0;

  // ヘッドラインの独創性
  if (!isGenericHeadline(concept.headline)) {
    score += 30;
  }

  // 差別化ポイントの独自性
  if (concept.keyDifferentiators.length >= 3 && 
      hasUniqueElements(concept.keyDifferentiators)) {
    score += 35;
  }

  // 価値提案の創意工夫
  if (!isGenericValueProposition(concept.valueProposition)) {
    score += 35;
  }

  return score;
}

/**
 * 実用性を評価
 */
function evaluatePracticalUsability(concept: ConceptData): number {
  let score = 0;

  // アクションプランの具体性
  if (concept.actionableInsights.length >= 3 && 
      areInsightsSpecific(concept.actionableInsights)) {
    score += 40;
  }

  // 信頼度スコアの妥当性
  if (concept.confidenceScore >= 70 && concept.confidenceScore <= 95) {
    score += 30;
  }

  // 実装可能性
  if (areInsightsImplementable(concept.actionableInsights)) {
    score += 30;
  }

  return score;
}

/**
 * 総合スコアを計算
 */
function calculateOverallScore(metrics: QualityMetrics): number {
  const weights = {
    conceptRelevance: 0.25,
    logicalConsistency: 0.25,
    marketAlignment: 0.20,
    creativityLevel: 0.15,
    practicalUsability: 0.15
  };

  return (
    metrics.conceptRelevance * weights.conceptRelevance +
    metrics.logicalConsistency * weights.logicalConsistency +
    metrics.marketAlignment * weights.marketAlignment +
    metrics.creativityLevel * weights.creativityLevel +
    metrics.practicalUsability * weights.practicalUsability
  );
}

// ヘルパー関数群

function extractKeywords(text: string): string[] {
  return text
    .replace(/[^\w\s]/g, '')
    .split(/\s+/)
    .filter(word => word.length > 2)
    .map(word => word.toLowerCase());
}

function isConsistent(text1: string, text2: string): boolean {
  const keywords1 = extractKeywords(text1);
  const keywords2 = extractKeywords(text2);
  const commonKeywords = keywords1.filter(word => keywords2.includes(word));
  return commonKeywords.length > 0;
}

function isRelated(text1: string, text2: string): boolean {
  const keywords1 = extractKeywords(text1);
  const keywords2 = extractKeywords(text2);
  return keywords1.some(word => keywords2.includes(word));
}

function isLogicalReasoning(reasoning: string, confidenceScore: number): boolean {
  const hasReasoning = reasoning.length > 30;
  const confidenceMatch = 
    (confidenceScore >= 90 && reasoning.includes('強い')) ||
    (confidenceScore >= 80 && reasoning.includes('高い')) ||
    (confidenceScore >= 70 && reasoning.includes('適切'));
  
  return hasReasoning && (confidenceMatch || confidenceScore <= 85);
}

function areActionableInsightsRealistic(insights: string[]): boolean {
  const actionWords = ['実装', '追加', '強化', '改善', '最適化', '導入', '活用'];
  return insights.every(insight => 
    actionWords.some(word => insight.includes(word)) &&
    insight.length > 10
  );
}

function containsSpecificTerms(text: string): boolean {
  const specificTerms = ['年代', '職業', '課題', 'ニーズ', '行動', '特徴'];
  return specificTerms.some(term => text.includes(term));
}

function parseConversionLift(liftText: string): number {
  const match = liftText.match(/(\d+)/);
  return match ? parseInt(match[1]) : 0;
}

function isGenericHeadline(headline: string): boolean {
  const genericPhrases = ['最高の', '究極の', '革新的な', 'No.1', '最新の'];
  return genericPhrases.some(phrase => headline.includes(phrase)) &&
         headline.length < 20;
}

function hasUniqueElements(differentiators: string[]): boolean {
  const commonTerms = ['高品質', '低価格', '簡単', '便利', '安心'];
  const uniqueCount = differentiators.filter(diff => 
    !commonTerms.some(term => diff.includes(term))
  ).length;
  
  return uniqueCount >= 2;
}

function isGenericValueProposition(proposition: string): boolean {
  const genericPhrases = ['お客様満足', '高品質サービス', '安心・安全'];
  return genericPhrases.some(phrase => proposition.includes(phrase)) &&
         proposition.length < 30;
}

function areInsightsSpecific(insights: string[]): boolean {
  return insights.every(insight => 
    insight.length > 15 && 
    !insight.includes('検討') &&
    !insight.includes('考慮')
  );
}

function areInsightsImplementable(insights: string[]): boolean {
  const implementableWords = ['ボタン', 'テキスト', '画像', 'セクション', 'フォーム'];
  return insights.some(insight => 
    implementableWords.some(word => insight.includes(word))
  );
}

/**
 * 品質テスト結果を人間が読める形式で出力
 */
export function formatQualityReport(metrics: QualityMetrics): string {
  const getGrade = (score: number): string => {
    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  };

  return `
AI品質評価レポート
==================
総合スコア: ${metrics.overallScore.toFixed(1)}点 (${getGrade(metrics.overallScore)})

詳細評価:
- コンセプト関連性: ${metrics.conceptRelevance.toFixed(1)}点 (${getGrade(metrics.conceptRelevance)})
- 論理的一貫性: ${metrics.logicalConsistency.toFixed(1)}点 (${getGrade(metrics.logicalConsistency)})
- 市場適合性: ${metrics.marketAlignment.toFixed(1)}点 (${getGrade(metrics.marketAlignment)})
- 創造性レベル: ${metrics.creativityLevel.toFixed(1)}点 (${getGrade(metrics.creativityLevel)})
- 実用性: ${metrics.practicalUsability.toFixed(1)}点 (${getGrade(metrics.practicalUsability)})
`;
}

/**
 * 品質テストの合格基準を判定
 */
export function isQualityAcceptable(metrics: QualityMetrics): boolean {
  return (
    metrics.overallScore >= 75 &&
    metrics.conceptRelevance >= 70 &&
    metrics.logicalConsistency >= 70 &&
    metrics.marketAlignment >= 60 &&
    metrics.creativityLevel >= 50 &&
    metrics.practicalUsability >= 70
  );
}

/**
 * レスポンス時間品質を評価
 */
export function evaluateResponseTime(responseTime: number): {
  score: number;
  grade: string;
  acceptable: boolean;
} {
  let score = 100;
  
  if (responseTime > 30000) {
    score = Math.max(0, 100 - (responseTime - 30000) / 1000 * 10);
  }
  
  const grade = score >= 90 ? 'A' : score >= 80 ? 'B' : score >= 70 ? 'C' : score >= 60 ? 'D' : 'F';
  const acceptable = responseTime <= 45000; // 45秒以内
  
  return { score, grade, acceptable };
}

/**
 * AI安定性評価（複数回のテスト結果から）
 */
export interface StabilityMetrics {
  successRate: number;
  averageResponseTime: number;
  qualityConsistency: number;
  errorRate: number;
}

export function evaluateStability(testResults: Array<{
  success: boolean;
  responseTime: number;
  qualityScore: number;
}>): StabilityMetrics {
  const totalTests = testResults.length;
  const successfulTests = testResults.filter(r => r.success);
  
  const successRate = (successfulTests.length / totalTests) * 100;
  
  // 平均レスポンス時間の計算を修正
  const averageResponseTime = successfulTests.length === 0
    ? 0
    : successfulTests.reduce((sum, r) => sum + r.responseTime, 0) / successfulTests.length;
  
  // 品質の一貫性（標準偏差で評価）- ゼロ除算対策
  const qualityScores = successfulTests.map(r => r.qualityScore);
  const avgQuality = qualityScores.length === 0
    ? 0
    : qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length;
  
  const variance = qualityScores.length === 0
    ? 0
    : qualityScores.reduce((sum, score) => sum + Math.pow(score - avgQuality, 2), 0) / qualityScores.length;
  
  const standardDeviation = Math.sqrt(variance);
  const qualityConsistency = Math.max(0, 100 - standardDeviation);
  
  const errorRate = ((totalTests - successfulTests.length) / totalTests) * 100;
  
  return {
    successRate,
    averageResponseTime,
    qualityConsistency,
    errorRate
  };
}

/**
 * セクション型の定義（型安全性向上）
 */
export interface HeroSectionData {
  title: string;
  subtitle: string;
  ctaText: string;
  backgroundImage?: string;
}

export interface FeaturesSectionData {
  title: string;
  features: Array<{
    title: string;
    description: string;
    icon?: string;
  }>;
}

export interface TestimonialsSectionData {
  title: string;
  testimonials: Array<{
    name: string;
    role: string;
    content: string;
    avatar?: string;
  }>;
}

export interface PricingSectionData {
  title: string;
  plans: Array<{
    name: string;
    price: string;
    features: string[];
    highlighted?: boolean;
  }>;
}

export type SectionData = 
  | { type: 'hero'; data: HeroSectionData }
  | { type: 'features'; data: FeaturesSectionData }
  | { type: 'testimonials'; data: TestimonialsSectionData }
  | { type: 'pricing'; data: PricingSectionData };

/**
 * 安全なテキストサニタイゼーション
 */
export function sanitizeText(text: string): string {
  return text
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
}

/**
 * 安全なDOM要素作成（innerHTML使用を避ける）
 */
export function createSafeElement(
  tagName: string, 
  textContent: string, 
  attributes?: Record<string, string>
): HTMLElement {
  const element = document.createElement(tagName);
  element.textContent = sanitizeText(textContent);
  
  if (attributes) {
    Object.entries(attributes).forEach(([key, value]) => {
      element.setAttribute(key, sanitizeText(value));
    });
  }
  
  return element;
}

/**
 * React要素として返すセクション生成（推奨アプローチ）
 */
export function generateSectionReactElement(sectionData: SectionData): React.ReactElement {
  switch (sectionData.type) {
    case 'hero':
      return React.createElement('section', {
        'data-section-type': 'hero',
        'data-testid': 'hero-section',
        className: 'lp-section lp-hero'
      }, [
        React.createElement('h1', {
          key: 'title',
          'data-editable': 'true',
          'data-testid': 'hero-title'
        }, sectionData.data.title),
        React.createElement('p', {
          key: 'subtitle',
          'data-editable': 'true',
          'data-testid': 'hero-subtitle'
        }, sectionData.data.subtitle)
      ]);
      
    case 'features':
      return React.createElement('section', {
        'data-section-type': 'features',
        'data-testid': 'features-section',
        className: 'lp-section lp-features'
      }, [
        React.createElement('h2', {
          key: 'title',
          'data-editable': 'true'
        }, sectionData.data.title),
        ...sectionData.data.features.map((feature, index) =>
          React.createElement('div', {
            key: `feature-${index}`,
            className: 'feature-item'
          }, [
            React.createElement('h3', {
              key: 'feature-title',
              'data-editable': 'true'
            }, feature.title),
            React.createElement('p', {
              key: 'feature-desc',
              'data-editable': 'true'
            }, feature.description)
          ])
        )
      ]);
      
    default:
      return React.createElement('div', {}, 'Unknown section type');
  }
}